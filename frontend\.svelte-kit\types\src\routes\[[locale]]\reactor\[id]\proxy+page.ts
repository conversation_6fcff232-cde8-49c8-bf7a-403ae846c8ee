// @ts-nocheck
import type { PageLoad } from './$types';
import type { PostEntity, CommentEntity } from './types';

import { error } from '@sveltejs/kit';
import { handleUnauthorized } from "$lib";

export const load = async ({ fetch, params }: Parameters<PageLoad>[0]) => {
  const [
    postResponse,
    commentsResponse,
  ] = await Promise.all([
    fetch(`/api/reactor/post/${params.id}`),
    fetch(`/api/reactor/comment?entityType=post&entityId=${params.id}`),
  ]);

  handleUnauthorized(postResponse);
  handleUnauthorized(commentsResponse);

  if (postResponse.status === 404) {
    throw error(404, {
      message: "Post not found",
    });
  }

  if (!postResponse.ok) {
    throw error(500, {
      message: `Failed to fetch post: ${postResponse.statusText}`,
    });
  }

  const post: PostEntity = await postResponse.json();

  // Convert date strings to Date objects for post
  const processedPost = {
    ...post,
    createdAt: new Date(post.createdAt),
    updatedAt: new Date(post.updatedAt),
  };

  let comments: CommentEntity[] = [];
  if (commentsResponse.ok) {
    const commentsData = await commentsResponse.json() as {
      items: CommentEntity[];
      total: number;
    };

    // Convert date strings to Date objects for comments
    comments = commentsData.items.map((comment) => ({
      ...comment,
      createdAt: new Date(comment.createdAt),
      updatedAt: new Date(comment.updatedAt),
      deletedAt: comment.deletedAt ? new Date(comment.deletedAt) : null,
    }));
  }

  return {
    post: processedPost,
    comments,
  };
};

import { z, <PERSON><PERSON><PERSON><PERSON><PERSON> } from "src/zod";
export type PostStatus = ZodHelper.Infer<typeof PostStatus>;
export declare const PostStatus: z.Zod<PERSON><PERSON>Enum<{
    draft: "draft";
    published: "published";
    archived: "archived";
}>;
export type Post = ZodHelper.Infer<typeof Post>;
export declare const Post: z.ZodObject<{
    id: z.ZodString;
    title: z.<PERSON><z.ZodObject<{
        locale: z.ZodEnum<["en", "ru"]>;
        value: z.ZodString;
    }, "strip", z.ZodType<PERSON>ny, {
        value: string;
        locale: "en" | "ru";
    }, {
        value: string;
        locale: "en" | "ru";
    }>, "many">;
    description: z.<PERSON><z.ZodObject<{
        locale: z.ZodEnum<["en", "ru"]>;
        value: z.ZodString;
    }, "strip", z.ZodTypeAny, {
        value: string;
        locale: "en" | "ru";
    }, {
        value: string;
        locale: "en" | "ru";
    }>, "many">;
    status: z.Z<PERSON><{
        draft: "draft";
        published: "published";
        archived: "archived";
    }>;
    publishedAt: z.ZodNullable<z.ZodPipeline<z.ZodUnion<[z.ZodNumber, z.ZodString, z.ZodDate]>, z.ZodDate>>;
    images: z.ZodOptional<z.ZodArray<z.ZodObject<{
        id: z.ZodString;
        url: z.ZodString;
        createdAt: z.ZodPipeline<z.ZodUnion<[z.ZodNumber, z.ZodString, z.ZodDate]>, z.ZodDate>;
        updatedAt: z.ZodPipeline<z.ZodUnion<[z.ZodNumber, z.ZodString, z.ZodDate]>, z.ZodDate>;
    }, "strip", z.ZodTypeAny, {
        id: string;
        url: string;
        createdAt: Date;
        updatedAt: Date;
    }, {
        id: string;
        url: string;
        createdAt: string | number | Date;
        updatedAt: string | number | Date;
    }>, "many">>;
    createdAt: z.ZodPipeline<z.ZodUnion<[z.ZodNumber, z.ZodString, z.ZodDate]>, z.ZodDate>;
    updatedAt: z.ZodPipeline<z.ZodUnion<[z.ZodNumber, z.ZodString, z.ZodDate]>, z.ZodDate>;
}, "strip", z.ZodTypeAny, {
    status: "draft" | "published" | "archived";
    id: string;
    createdAt: Date;
    updatedAt: Date;
    description: {
        value: string;
        locale: "en" | "ru";
    }[];
    title: {
        value: string;
        locale: "en" | "ru";
    }[];
    publishedAt: Date | null;
    images?: {
        id: string;
        url: string;
        createdAt: Date;
        updatedAt: Date;
    }[] | undefined;
}, {
    status: "draft" | "published" | "archived";
    id: string;
    createdAt: string | number | Date;
    updatedAt: string | number | Date;
    description: {
        value: string;
        locale: "en" | "ru";
    }[];
    title: {
        value: string;
        locale: "en" | "ru";
    }[];
    publishedAt: string | number | Date | null;
    images?: {
        id: string;
        url: string;
        createdAt: string | number | Date;
        updatedAt: string | number | Date;
    }[] | undefined;
}>;
export declare const Posts: z.ZodArray<z.ZodObject<{
    id: z.ZodString;
    title: z.ZodArray<z.ZodObject<{
        locale: z.ZodEnum<["en", "ru"]>;
        value: z.ZodString;
    }, "strip", z.ZodTypeAny, {
        value: string;
        locale: "en" | "ru";
    }, {
        value: string;
        locale: "en" | "ru";
    }>, "many">;
    description: z.ZodArray<z.ZodObject<{
        locale: z.ZodEnum<["en", "ru"]>;
        value: z.ZodString;
    }, "strip", z.ZodTypeAny, {
        value: string;
        locale: "en" | "ru";
    }, {
        value: string;
        locale: "en" | "ru";
    }>, "many">;
    status: z.ZodNativeEnum<{
        draft: "draft";
        published: "published";
        archived: "archived";
    }>;
    publishedAt: z.ZodNullable<z.ZodPipeline<z.ZodUnion<[z.ZodNumber, z.ZodString, z.ZodDate]>, z.ZodDate>>;
    images: z.ZodOptional<z.ZodArray<z.ZodObject<{
        id: z.ZodString;
        url: z.ZodString;
        createdAt: z.ZodPipeline<z.ZodUnion<[z.ZodNumber, z.ZodString, z.ZodDate]>, z.ZodDate>;
        updatedAt: z.ZodPipeline<z.ZodUnion<[z.ZodNumber, z.ZodString, z.ZodDate]>, z.ZodDate>;
    }, "strip", z.ZodTypeAny, {
        id: string;
        url: string;
        createdAt: Date;
        updatedAt: Date;
    }, {
        id: string;
        url: string;
        createdAt: string | number | Date;
        updatedAt: string | number | Date;
    }>, "many">>;
    createdAt: z.ZodPipeline<z.ZodUnion<[z.ZodNumber, z.ZodString, z.ZodDate]>, z.ZodDate>;
    updatedAt: z.ZodPipeline<z.ZodUnion<[z.ZodNumber, z.ZodString, z.ZodDate]>, z.ZodDate>;
}, "strip", z.ZodTypeAny, {
    status: "draft" | "published" | "archived";
    id: string;
    createdAt: Date;
    updatedAt: Date;
    description: {
        value: string;
        locale: "en" | "ru";
    }[];
    title: {
        value: string;
        locale: "en" | "ru";
    }[];
    publishedAt: Date | null;
    images?: {
        id: string;
        url: string;
        createdAt: Date;
        updatedAt: Date;
    }[] | undefined;
}, {
    status: "draft" | "published" | "archived";
    id: string;
    createdAt: string | number | Date;
    updatedAt: string | number | Date;
    description: {
        value: string;
        locale: "en" | "ru";
    }[];
    title: {
        value: string;
        locale: "en" | "ru";
    }[];
    publishedAt: string | number | Date | null;
    images?: {
        id: string;
        url: string;
        createdAt: string | number | Date;
        updatedAt: string | number | Date;
    }[] | undefined;
}>, "many">;
export type CreatePostInput = ZodHelper.Infer<typeof CreatePostInput>;
export declare const CreatePostInput: z.ZodPipeline<z.ZodEffects<z.ZodString, any, string>, z.ZodObject<{
    title: z.ZodArray<z.ZodObject<{
        locale: z.ZodEnum<["en", "ru"]>;
        value: z.ZodString;
    }, "strip", z.ZodTypeAny, {
        value: string;
        locale: "en" | "ru";
    }, {
        value: string;
        locale: "en" | "ru";
    }>, "many">;
    description: z.ZodArray<z.ZodObject<{
        locale: z.ZodEnum<["en", "ru"]>;
        value: z.ZodString;
    }, "strip", z.ZodTypeAny, {
        value: string;
        locale: "en" | "ru";
    }, {
        value: string;
        locale: "en" | "ru";
    }>, "many">;
    status: z.ZodNativeEnum<{
        draft: "draft";
        published: "published";
        archived: "archived";
    }>;
    publishedAt: z.ZodNullable<z.ZodPipeline<z.ZodUnion<[z.ZodNumber, z.ZodString, z.ZodDate]>, z.ZodDate>>;
}, "strip", z.ZodTypeAny, {
    status: "draft" | "published" | "archived";
    description: {
        value: string;
        locale: "en" | "ru";
    }[];
    title: {
        value: string;
        locale: "en" | "ru";
    }[];
    publishedAt: Date | null;
}, {
    status: "draft" | "published" | "archived";
    description: {
        value: string;
        locale: "en" | "ru";
    }[];
    title: {
        value: string;
        locale: "en" | "ru";
    }[];
    publishedAt: string | number | Date | null;
}>>;
export type UpdatePostInput = ZodHelper.Infer<typeof UpdatePostInput>;
export declare const UpdatePostInput: z.ZodObject<{
    title: z.ZodOptional<z.ZodArray<z.ZodObject<{
        locale: z.ZodEnum<["en", "ru"]>;
        value: z.ZodString;
    }, "strip", z.ZodTypeAny, {
        value: string;
        locale: "en" | "ru";
    }, {
        value: string;
        locale: "en" | "ru";
    }>, "many">>;
    description: z.ZodOptional<z.ZodArray<z.ZodObject<{
        locale: z.ZodEnum<["en", "ru"]>;
        value: z.ZodString;
    }, "strip", z.ZodTypeAny, {
        value: string;
        locale: "en" | "ru";
    }, {
        value: string;
        locale: "en" | "ru";
    }>, "many">>;
    status: z.ZodOptional<z.ZodNativeEnum<{
        draft: "draft";
        published: "published";
        archived: "archived";
    }>>;
}, "strip", z.ZodTypeAny, {
    status?: "draft" | "published" | "archived" | undefined;
    description?: {
        value: string;
        locale: "en" | "ru";
    }[] | undefined;
    title?: {
        value: string;
        locale: "en" | "ru";
    }[] | undefined;
}, {
    status?: "draft" | "published" | "archived" | undefined;
    description?: {
        value: string;
        locale: "en" | "ru";
    }[] | undefined;
    title?: {
        value: string;
        locale: "en" | "ru";
    }[] | undefined;
}>;

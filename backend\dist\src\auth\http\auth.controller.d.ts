import { UserService } from "src/user/user.service";
import { CurrentUser } from "../types";
import { AuthService } from "../auth.service";
import * as Dto from "./dto";
import { Request, Response } from "express";
export declare class AuthController {
    private readonly authService;
    private readonly userService;
    constructor(authService: AuthService, userService: UserService);
    test(): Promise<boolean>;
    me(currentUser: CurrentUser): Promise<{
        id: string;
        email: string;
        role: "user" | "admin" | "moderator";
        name: {
            value: string;
            locale: "en" | "ru";
        }[];
        description: {
            value: string;
            locale: "en" | "ru";
        }[];
        joinedAt: Date;
        images?: {
            id: string;
            url: string;
            createdAt: Date;
            updatedAt: Date;
        }[] | undefined;
    }>;
    otp(body: Dto.Otp, ipAddress: string, userAgent: string): Promise<{
        isSent: boolean;
    }>;
    register(req: Request, body: Dto.Register, ipAddress: string, userAgent: string): Promise<{
        id: string;
        email: string;
        role: import("@prisma/client").$Enums.UserRole;
    }>;
    login(req: Request, body: Dto.Login, ipAddress: string, userAgent: string): Promise<{
        id: string;
        email: string;
        role: import("@prisma/client").$Enums.UserRole;
    }>;
    signOut(req: Request, res: Response): Promise<void>;
}

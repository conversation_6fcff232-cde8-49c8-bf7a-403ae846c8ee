"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.UserController = void 0;
const common_1 = require("@nestjs/common");
const platform_express_1 = require("@nestjs/platform-express");
const zod_1 = require("zod");
const zod_2 = require("../../zod");
const errors_1 = require("../../common/errors");
const user_service_1 = require("../user.service");
const user_title_service_1 = require("../user-title.service");
const Dto = __importStar(require("./dto"));
const current_user_decorator_1 = require("../../auth/http/current-user.decorator");
const session_auth_guard_1 = require("../../auth/http/session-auth.guard");
const MAX_FILE_SIZE = 5 * 1024 * 1024;
const ALLOWED_FILE_TYPES = ["image/jpeg", "image/png", "image/webp"];
let UserController = class UserController {
    constructor(userService, userTitleService) {
        this.userService = userService;
        this.userTitleService = userTitleService;
    }
    async getUsers(page, size) {
        const users = await this.userService.getMany({}, { page, size });
        return zod_2.ZodHelper.parseInput(Dto.Users, users);
    }
    async getUser(id) {
        const user = await this.userService.getOne(id);
        if (!user) {
            throw new common_1.NotFoundException(...(0, errors_1.getError)("user_not_found"));
        }
        return zod_2.ZodHelper.parseInput(Dto.User, user);
    }
    async updateUser(id, body, currentUser) {
        const user = await this.userService.update(id, body, currentUser);
        return zod_2.ZodHelper.parseInput(Dto.User, user);
    }
    async uploadUserImage(id, currentUser, file) {
        try {
            await this.userService.canChange(id, currentUser);
            if (!file) {
                throw new common_1.BadRequestException("No file uploaded");
            }
            const image = await this.userService.uploadUserImage(id, file);
            return zod_2.ZodHelper.parseInput(zod_2.ZodHelper.Image, image);
        }
        catch (error) {
            console.error("Error uploading image:", error);
            if (error instanceof common_1.BadRequestException) {
                throw error;
            }
            throw new common_1.BadRequestException("Failed to upload image");
        }
    }
    async getUserTitles(id, active, page, size) {
        const userTitles = await this.userTitleService.getMany({
            ownerId: id,
            isActive: active,
        }, { page, size });
        return zod_2.ZodHelper.parseInput(Dto.UserTitles, userTitles);
    }
    async updateUserTitle(id, titleId, body, user) {
        const userTitle = await this.userTitleService.update(id, body, user);
        return zod_2.ZodHelper.parseInput(Dto.UserTitle, userTitle);
    }
};
exports.UserController = UserController;
__decorate([
    (0, common_1.Get)(),
    __param(0, (0, common_1.Query)("page", new zod_2.ZodPipe(zod_2.ZodHelper.pagination.page))),
    __param(1, (0, common_1.Query)("size", new zod_2.ZodPipe(zod_2.ZodHelper.pagination.size))),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Number, Number]),
    __metadata("design:returntype", Promise)
], UserController.prototype, "getUsers", null);
__decorate([
    (0, common_1.Get)(":id"),
    __param(0, (0, common_1.Param)("id", new zod_2.ZodPipe(zod_2.ZodHelper.Uuid))),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", Promise)
], UserController.prototype, "getUser", null);
__decorate([
    (0, common_1.Put)(":id"),
    (0, common_1.UseGuards)(session_auth_guard_1.HttpSessionAuthGuard),
    __param(0, (0, common_1.Param)("id", new zod_2.ZodPipe(zod_2.ZodHelper.Uuid))),
    __param(1, (0, common_1.Body)(new zod_2.ZodPipe(Dto.UpdateUser))),
    __param(2, (0, current_user_decorator_1.HttpCurrentUser)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, Object, Object]),
    __metadata("design:returntype", Promise)
], UserController.prototype, "updateUser", null);
__decorate([
    (0, common_1.Post)(":id/image"),
    (0, common_1.UseGuards)(session_auth_guard_1.HttpSessionAuthGuard),
    (0, common_1.UseInterceptors)((0, platform_express_1.FileInterceptor)("image")),
    __param(0, (0, common_1.Param)("id", new zod_2.ZodPipe(zod_2.ZodHelper.Uuid))),
    __param(1, (0, current_user_decorator_1.HttpCurrentUser)()),
    __param(2, (0, common_1.UploadedFile)(new common_1.ParseFilePipe({
        validators: [
            new common_1.MaxFileSizeValidator({ maxSize: MAX_FILE_SIZE }),
            new common_1.FileTypeValidator({
                fileType: ALLOWED_FILE_TYPES.join("|"),
            }),
        ],
    }))),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, Object, Object]),
    __metadata("design:returntype", Promise)
], UserController.prototype, "uploadUserImage", null);
__decorate([
    (0, common_1.Get)(":id/title"),
    __param(0, (0, common_1.Param)("id", new zod_2.ZodPipe(zod_2.ZodHelper.Uuid))),
    __param(1, (0, common_1.Query)("active", new zod_2.ZodPipe(zod_1.z.boolean()))),
    __param(2, (0, common_1.Query)("page", new zod_2.ZodPipe(zod_2.ZodHelper.pagination.page))),
    __param(3, (0, common_1.Query)("size", new zod_2.ZodPipe(zod_2.ZodHelper.pagination.size))),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, Boolean, Number, Number]),
    __metadata("design:returntype", Promise)
], UserController.prototype, "getUserTitles", null);
__decorate([
    (0, common_1.Put)(":id/title/:titleId"),
    __param(0, (0, common_1.Param)("id", new zod_2.ZodPipe(zod_2.ZodHelper.Uuid))),
    __param(1, (0, common_1.Param)("titleId", new zod_2.ZodPipe(zod_2.ZodHelper.Uuid))),
    __param(2, (0, common_1.Body)(new zod_2.ZodPipe(Dto.UpdateUserTitle))),
    __param(3, (0, current_user_decorator_1.HttpCurrentUser)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, String, Object, Object]),
    __metadata("design:returntype", Promise)
], UserController.prototype, "updateUserTitle", null);
exports.UserController = UserController = __decorate([
    (0, common_1.Controller)("user"),
    __metadata("design:paramtypes", [user_service_1.UserService,
        user_title_service_1.UserTitleService])
], UserController);
//# sourceMappingURL=user.controller.js.map
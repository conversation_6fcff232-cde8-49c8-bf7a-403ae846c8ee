import type { Localization } from "$lib";
import type { PageLoad } from "./$types";

import { handleUnauthorized } from "$lib";

export type Me = {
  id: string;
  email: string;
  role: "user" | "admin";
  name: Localization[];
  description: Localization[];
  images: {
    id: string;
    url: string;
    source: string;
  }[];
  joinedAt: string;
};

export const load: PageLoad = async ({ fetch, url }) => {
  const response = await fetch("/api/auth/me");

  handleUnauthorized(response);

  const me: Me = await response.json();

  return { me };
};

import { statement } from "./validate.mjs";
function array(value) {
    return `(${value.map((v) => `'${v}'`).join(", ")})`;
}
function arrayEq(operator) {
    if (operator === "=") {
        return "IN";
    }
    return "NOT IN";
}
function like(value) {
    return `LIKE '%${value}%'`;
}
export function generateSql(statement) {
    switch (statement.type) {
        case "comparison": {
            switch (statement.identifier) {
                case "hub": {
                    return `hub ${arrayEq(statement.operator)} ${array(statement.value)}`;
                }
                case "group": {
                    return `group ${arrayEq(statement.operator)} ${array(statement.value)}`;
                }
                case "author": {
                    return `author ${arrayEq(statement.operator)} ${array(statement.value)}`;
                }
                case "rating": {
                    return `rating ${statement.operator} ${statement.value}`;
                }
                case "usefulness": {
                    return `usefulness ${statement.operator} ${statement.value}`;
                }
                case "tag": {
                    return `tag ${arrayEq(statement.operator)} ${array(statement.value)}`;
                }
                case "difficulty": {
                    return `difficulty ${arrayEq(statement.operator)} ${array(statement.value)}`;
                }
                case "duration": {
                    return `duration ${statement.operator} ${statement.value}`;
                }
                case "age": {
                    return `age ${statement.operator} ${statement.value}`;
                }
                case "title": {
                    switch (statement.operator) {
                        case "~":
                            return `title ${like(statement.value)}`;
                    }
                }
                case "body": {
                    switch (statement.operator) {
                        case "~":
                            return `body ${like(statement.value)}`;
                    }
                }
            }
        }
        case "and": {
            return `(${statement.statements.map(generateSql).join(" AND ")})`;
        }
        case "or": {
            return `(${statement.statements.map(generateSql).join(" OR ")})`;
        }
    }
}
export const sql = generateSql(statement);
console.log(sql);
//# sourceMappingURL=sql.mjs.map
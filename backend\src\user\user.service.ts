import {
    BadRequestException,
    ForbiddenException,
    Injectable,
} from "@nestjs/common";
import { Prisma, UserRole } from "@prisma/client";
import { toPrismaPagination } from "src/utils";
import { BaseService } from "src/common/base-service";
import { PrismaService } from "src/prisma/prisma.service";
import { getError } from "src/common/errors";
import { CurrentUser } from "src/auth/types";
import { Zod<PERSON>elper } from "src/zod";
import { MinioService, FileInfo } from "src/minio/minio.service";

export type CreateUser = {
    referrerId: string | null;
    email: string;
};

@Injectable()
export class UserService extends BaseService {
    constructor(
        private readonly prisma: PrismaService,
        private readonly minioService: MinioService,
    ) {
        super("user");
    }

    async canGet(id: string, user: CurrentUser): Promise<true> {
        await this.getOneOrThrow(id);

        return true;
    }

    async canChange(id: string, user: CurrentUser): Promise<true> {
        await this.getOneOrThrow(id);

        if (user.role !== UserRole.admin) {
            if (user.id !== id) {
                throw new ForbiddenException(
                    ...getError("must_be_user_itself"),
                );
            }
        }

        return true;
    }

    async getByEmail(email: string) {
        return await this.prisma.user.findUnique({
            where: { email, deletedAt: null },
        });
    }

    async create(data: CreateUser) {
        {
            const user = await this.getByEmail(data.email);

            if (user) {
                throw new BadRequestException(
                    ...getError("user_email_is_busy"),
                );
            }
        }

        const user = await this.prisma.user.create({
            data: {
                referrerId: data.referrerId,
                email: data.email,
            },
        });

        return user;
    }

    async check(ids: string[]) {
        return await this._check(ids, this.prisma.user, "user");
    }

    async getOne(id: string) {
        return await this.prisma.user.findUnique({
            where: { id, deletedAt: null },
            include: {
                titles: true,
                name: true,
                description: true,
                images: {
                    orderBy: {
                        createdAt: "desc",
                    },
                },
            },
        });
    }

    async getOneOrThrow(id: string) {
        const user = await this.getOne(id);

        if (!user) {
            throw this.createNotFoundException();
        }

        return user;
    }

    async getMany(
        where: Prisma.UserWhereInput,
        pagination?: { page: number; size: number },
    ) {
        return await this.prisma.user.findMany({
            ...toPrismaPagination(pagination),
            where: {
                ...where,
                deletedAt: null,
            },
            include: {
                titles: true,
                name: true,
                description: true,
                images: true,
            },
        });
    }

    async createOne(data: Prisma.UserCreateInput) {
        return await this.prisma.user.create({ data });
    }

    async createMany(data: Prisma.UserCreateManyInput[]) {
        return await this.prisma.user.createMany({ data });
    }

    async updateOne(id: string, data: Prisma.UserUpdateInput) {
        return await this.prisma.user.update({
            where: {
                id,
                deletedAt: null,
            },
            data,
        });
    }

    async update(
        id: string,
        data: Partial<{
            name: ZodHelper.Localization[];
            description: ZodHelper.Localization[];
        }>,
        user: CurrentUser,
    ) {
        await this.canChange(id, user);

        return await this.prisma.$transaction(async (trx) => {
            await trx.user.update({
                where: {
                    id,
                },
                data: {
                    name: data.name && {
                        deleteMany: {},
                    },
                    description: data.description && {
                        deleteMany: {},
                    },
                },
            });

            return await trx.user.update({
                where: {
                    id,
                },
                data: {
                    name: {
                        create: data.name?.map((name) => ({
                            key: "name",
                            locale: name.locale,
                            value: name.value,
                        })),
                    },
                    description: {
                        create: data.description?.map((description) => ({
                            key: "description",
                            locale: description.locale,
                            value: description.value,
                        })),
                    },
                },
                include: {
                    images: true,
                    name: true,
                    description: true,
                },
            });
        });
    }

    async uploadUserImage(userId: string, file: FileInfo) {
        // Verify user exists
        await this.getOneOrThrow(userId);

        // Get current images count to determine the index
        const user = await this.prisma.user.findUnique({
            where: { id: userId },
            include: { images: true },
        });

        const index = user?.images?.length || 0;

        // Upload the image to MinIO
        const imageUrl = await this.minioService.uploadUserImage(
            file,
            userId,
            index,
        );

        // Create image record
        const image = await this.prisma.image.create({
            data: {
                url: imageUrl,
            },
        });

        // Connect the image to the user
        await this.prisma.user.update({
            where: { id: userId },
            data: {
                images: {
                    connect: [{ id: image.id }],
                },
            },
        });

        return image;
    }

    async updateMany(
        where: Prisma.UserWhereInput,
        data: Prisma.UserUpdateInput,
    ) {
        return await this.prisma.user.updateMany({ where, data });
    }

    async softDeleteOne(id: string) {
        return await this.updateOne(id, { deletedAt: new Date() });
    }

    async softDeleteMany(where: Prisma.UserWhereInput) {
        return await this.updateMany(where, { deletedAt: new Date() });
    }

    async deleteOne(id: string) {
        return await this.prisma.user.delete({ where: { id } });
    }

    async deleteMany(where: Prisma.UserWhereInput) {
        return await this.prisma.user.deleteMany({ where });
    }
}

import { defineConfig } from "tsup";

export default defineConfig([
    {
        entry: ["src/index.ts"],
        format: "cjs",
        outDir: "dist",
        splitting: true,
        clean: true,
        sourcemap: true,
        bundle: false,
        outExtension: () => ({ js: ".cjs" }),
    },
    {
        entry: ["src/index.ts"],
        format: "esm",
        outDir: "dist",
        splitting: true,
        clean: true,
        dts: true,
        sourcemap: true,
        bundle: false,
        outExtension: () => ({ js: ".mjs" }),
    }
]);

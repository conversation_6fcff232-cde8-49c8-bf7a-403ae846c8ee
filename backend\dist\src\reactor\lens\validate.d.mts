import { z } from "zod";
import { Statement as RawStatement } from "./create-ast.mjs";
type Comparison = Normalize<z.infer<typeof ComparisonSchema>>;
declare const ComparisonSchema: z.ZodIntersection<z.ZodObject<{
    type: z.<PERSON>al<"comparison">;
}, "strip", z.<PERSON>, {
    type: "comparison";
}, {
    type: "comparison";
}>, z.<PERSON><[z.ZodObject<{
    identifier: z.Zod<PERSON>iteral<"hub">;
    operator: z.<PERSON><[z.ZodLiteral<"=">, z.ZodLiteral<"!=">]>;
    value: z.<PERSON>od<PERSON>rray<z.ZodString, "many">;
}, "strip", z.<PERSON>ype<PERSON>, {
    value: string[];
    identifier: "hub";
    operator: "=" | "!=";
}, {
    value: string[];
    identifier: "hub";
    operator: "=" | "!=";
}>, z.<PERSON><{
    identifier: z.<PERSON>Literal<"group">;
    operator: z.<PERSON><[z.ZodLiteral<"=">, z.<PERSON>al<"!=">]>;
    value: z.<PERSON><z.ZodString, "many">;
}, "strip", z.ZodTypeAny, {
    value: string[];
    identifier: "group";
    operator: "=" | "!=";
}, {
    value: string[];
    identifier: "group";
    operator: "=" | "!=";
}>, z.ZodObject<{
    identifier: z.ZodLiteral<"author">;
    operator: z.ZodUnion<[z.ZodLiteral<"=">, z.ZodLiteral<"!=">]>;
    value: z.ZodArray<z.ZodString, "many">;
}, "strip", z.ZodTypeAny, {
    value: string[];
    identifier: "author";
    operator: "=" | "!=";
}, {
    value: string[];
    identifier: "author";
    operator: "=" | "!=";
}>, z.ZodObject<{
    identifier: z.ZodLiteral<"rating">;
    operator: z.ZodUnion<[z.ZodLiteral<"=">, z.ZodLiteral<"!=">, z.ZodLiteral<">">, z.ZodLiteral<">=">, z.ZodLiteral<"<">, z.ZodLiteral<"<=">]>;
    value: z.ZodNumber;
}, "strip", z.ZodTypeAny, {
    value: number;
    identifier: "rating";
    operator: "=" | "!=" | ">" | ">=" | "<" | "<=";
}, {
    value: number;
    identifier: "rating";
    operator: "=" | "!=" | ">" | ">=" | "<" | "<=";
}>, z.ZodObject<{
    identifier: z.ZodLiteral<"usefulness">;
    operator: z.ZodUnion<[z.ZodLiteral<"=">, z.ZodLiteral<"!=">, z.ZodLiteral<">">, z.ZodLiteral<">=">, z.ZodLiteral<"<">, z.ZodLiteral<"<=">]>;
    value: z.ZodNumber;
}, "strip", z.ZodTypeAny, {
    value: number;
    identifier: "usefulness";
    operator: "=" | "!=" | ">" | ">=" | "<" | "<=";
}, {
    value: number;
    identifier: "usefulness";
    operator: "=" | "!=" | ">" | ">=" | "<" | "<=";
}>, z.ZodObject<{
    identifier: z.ZodLiteral<"tag">;
    operator: z.ZodUnion<[z.ZodLiteral<"=">, z.ZodLiteral<"!=">]>;
    value: z.ZodArray<z.ZodString, "many">;
}, "strip", z.ZodTypeAny, {
    value: string[];
    identifier: "tag";
    operator: "=" | "!=";
}, {
    value: string[];
    identifier: "tag";
    operator: "=" | "!=";
}>, z.ZodObject<{
    identifier: z.ZodLiteral<"difficulty">;
    operator: z.ZodUnion<[z.ZodLiteral<"=">, z.ZodLiteral<"!=">]>;
    value: z.ZodArray<z.ZodUnion<[z.ZodLiteral<"easy">, z.ZodLiteral<"medium">, z.ZodLiteral<"hard">]>, "many">;
}, "strip", z.ZodTypeAny, {
    value: ("easy" | "medium" | "hard")[];
    identifier: "difficulty";
    operator: "=" | "!=";
}, {
    value: ("easy" | "medium" | "hard")[];
    identifier: "difficulty";
    operator: "=" | "!=";
}>, z.ZodObject<{
    identifier: z.ZodLiteral<"title">;
    operator: z.ZodLiteral<"~">;
    value: z.ZodString;
}, "strip", z.ZodTypeAny, {
    value: string;
    identifier: "title";
    operator: "~";
}, {
    value: string;
    identifier: "title";
    operator: "~";
}>, z.ZodObject<{
    identifier: z.ZodLiteral<"body">;
    operator: z.ZodLiteral<"~">;
    value: z.ZodString;
}, "strip", z.ZodTypeAny, {
    value: string;
    identifier: "body";
    operator: "~";
}, {
    value: string;
    identifier: "body";
    operator: "~";
}>, z.ZodObject<{
    identifier: z.ZodLiteral<"duration">;
    operator: z.ZodUnion<[z.ZodLiteral<">">, z.ZodLiteral<">=">, z.ZodLiteral<"<">, z.ZodLiteral<"<=">]>;
    value: z.ZodNumber;
}, "strip", z.ZodTypeAny, {
    value: number;
    identifier: "duration";
    operator: ">" | ">=" | "<" | "<=";
}, {
    value: number;
    identifier: "duration";
    operator: ">" | ">=" | "<" | "<=";
}>, z.ZodObject<{
    identifier: z.ZodLiteral<"age">;
    operator: z.ZodUnion<[z.ZodLiteral<">">, z.ZodLiteral<">=">, z.ZodLiteral<"<">, z.ZodLiteral<"<=">]>;
    value: z.ZodEffects<z.ZodString, number, string>;
}, "strip", z.ZodTypeAny, {
    value: number;
    identifier: "age";
    operator: ">" | ">=" | "<" | "<=";
}, {
    value: string;
    identifier: "age";
    operator: ">" | ">=" | "<" | "<=";
}>]>>;
type And = {
    type: "and";
    statements: Statement[];
};
type Or = {
    type: "or";
    statements: Statement[];
};
export type Statement = And | Or | Comparison;
export declare function validate(statement: RawStatement): Statement;
export {};

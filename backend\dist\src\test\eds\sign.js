"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const node_path_1 = __importDefault(require("node:path"));
const promises_1 = __importDefault(require("node:fs/promises"));
const node_crypto_1 = require("node:crypto");
const root = node_path_1.default.join(process.cwd(), "src/test/eds");
const PRIVATE_KEY_PASSPHRASE = "secret";
async function sign(data, privateKey) {
    const signer = (0, node_crypto_1.createSign)("rsa-sha256");
    signer.update(data);
    const privateKeyObject = (0, node_crypto_1.createPrivateKey)({
        key: privateKey,
        type: "pkcs8",
        format: "pem",
        passphrase: PRIVATE_KEY_PASSPHRASE,
    });
    return signer.sign(privateKeyObject);
}
(async () => {
    const documentName = process.argv[2];
    const privateKeyName = process.argv[3];
    if (!documentName) {
        throw new Error("Document name is required.");
    }
    if (!privateKeyName) {
        throw new Error("Private key name is required.");
    }
    const document = await promises_1.default.readFile(node_path_1.default.join(root, documentName));
    const privateKey = await promises_1.default.readFile(node_path_1.default.join(root, `${privateKeyName}-private.pem`));
    const signature = await sign(document, privateKey);
    await promises_1.default.writeFile(node_path_1.default.join(root, `${documentName}.sig`), signature);
})();
//# sourceMappingURL=sign.js.map
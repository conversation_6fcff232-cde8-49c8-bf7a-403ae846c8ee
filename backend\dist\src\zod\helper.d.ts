import { z } from "zod";
export type Infer<T extends z.ZodTypeAny> = Normalize<z.infer<T>>;
export type InferObject<T extends Record<string, z.ZodType>> = {
    [K in keyof T]: z.infer<T[K]>;
};
export declare function Typename<const T extends string>(name: T): z.Zod<PERSON>ef<PERSON><z.ZodLiteral<T>>;
export declare const ToDateTime: z.ZodPipeline<z.ZodUnion<[z.ZodNumber, z.ZodString, z.ZodDate]>, z.ZodDate>;
export declare function JsonToObject<T extends z.ZodRawShape>(schema: T): z.ZodPipeline<z.ZodEffects<z.ZodString, any, string>, z.ZodObject<T, "strip", z.ZodTypeAny, z.objectUtil.addQuestionMarks<z.baseObjectOutputType<T>, any> extends infer T_1 ? { [k in keyof T_1]: z.objectUtil.addQuestionMarks<z.baseObjectOutputType<T>, any>[k]; } : never, z.baseObjectInputType<T> extends infer T_2 ? { [k_1 in keyof T_2]: z.baseObjectInputType<T>[k_1]; } : never>>;
export declare function FormDataToObject<T extends z.ZodRawShape>(schema: T): z.ZodObject<{
    data: z.ZodPipeline<z.ZodEffects<z.ZodString, any, string>, z.ZodObject<T, "strip", z.ZodTypeAny, z.objectUtil.addQuestionMarks<z.baseObjectOutputType<T>, any> extends infer T_1 ? { [k in keyof T_1]: z.objectUtil.addQuestionMarks<z.baseObjectOutputType<T>, any>[k]; } : never, z.baseObjectInputType<T> extends infer T_2 ? { [k_1 in keyof T_2]: z.baseObjectInputType<T>[k_1]; } : never>>;
}, "strip", z.ZodTypeAny, z.objectUtil.addQuestionMarks<z.baseObjectOutputType<{
    data: z.ZodPipeline<z.ZodEffects<z.ZodString, any, string>, z.ZodObject<T, "strip", z.ZodTypeAny, z.objectUtil.addQuestionMarks<z.baseObjectOutputType<T>, any> extends infer T_6 ? { [k in keyof T_6]: z.objectUtil.addQuestionMarks<z.baseObjectOutputType<T>, any>[k]; } : never, z.baseObjectInputType<T> extends infer T_7 ? { [k_1 in keyof T_7]: z.baseObjectInputType<T>[k_1]; } : never>>;
}>, any> extends infer T_3 ? { [k_2 in keyof T_3]: z.objectUtil.addQuestionMarks<z.baseObjectOutputType<{
    data: z.ZodPipeline<z.ZodEffects<z.ZodString, any, string>, z.ZodObject<T, "strip", z.ZodTypeAny, z.objectUtil.addQuestionMarks<z.baseObjectOutputType<T>, any> extends infer T_4 ? { [k in keyof T_4]: z.objectUtil.addQuestionMarks<z.baseObjectOutputType<T>, any>[k]; } : never, z.baseObjectInputType<T> extends infer T_5 ? { [k_1 in keyof T_5]: z.baseObjectInputType<T>[k_1]; } : never>>;
}>, any>[k_2]; } : never, {
    data: string;
}>;
export type Locale = z.infer<typeof Locale>;
export declare const Locale: z.ZodEnum<["en", "ru"]>;
export declare const Locales: z.ZodArray<z.ZodEnum<["en", "ru"]>, "many">;
export type Localization = z.infer<typeof Localization>;
export declare const Localization: z.ZodObject<{
    locale: z.ZodEnum<["en", "ru"]>;
    value: z.ZodString;
}, "strip", z.ZodTypeAny, {
    value: string;
    locale: "en" | "ru";
}, {
    value: string;
    locale: "en" | "ru";
}>;
export declare const Localizations: z.ZodArray<z.ZodObject<{
    locale: z.ZodEnum<["en", "ru"]>;
    value: z.ZodString;
}, "strip", z.ZodTypeAny, {
    value: string;
    locale: "en" | "ru";
}, {
    value: string;
    locale: "en" | "ru";
}>, "many">;
export type Uuid = z.infer<typeof Uuid>;
export declare const Uuid: z.ZodString;
export type Email = z.infer<typeof Email>;
export declare const Email: z.ZodString;
export type Image = Infer<typeof Image>;
export declare const Image: z.ZodObject<{
    id: z.ZodString;
    url: z.ZodString;
    createdAt: z.ZodPipeline<z.ZodUnion<[z.ZodNumber, z.ZodString, z.ZodDate]>, z.ZodDate>;
    updatedAt: z.ZodPipeline<z.ZodUnion<[z.ZodNumber, z.ZodString, z.ZodDate]>, z.ZodDate>;
}, "strip", z.ZodTypeAny, {
    id: string;
    url: string;
    createdAt: Date;
    updatedAt: Date;
}, {
    id: string;
    url: string;
    createdAt: string | number | Date;
    updatedAt: string | number | Date;
}>;
export declare const intSchema: z.ZodNumber;
export declare const positiveIntSchema: z.ZodNumber;
export declare const pagination: {
    offset: z.ZodDefault<z.ZodNumber>;
    limit: z.ZodDefault<z.ZodNumber>;
    page: z.ZodDefault<z.ZodNumber>;
    size: z.ZodDefault<z.ZodNumber>;
};
export type Pagination = z.infer<typeof Pagination>;
export declare const Pagination: z.ZodOptional<z.ZodUnion<[z.ZodObject<{
    page: z.ZodDefault<z.ZodNumber>;
    size: z.ZodDefault<z.ZodNumber>;
}, "strip", z.ZodTypeAny, {
    page: number;
    size: number;
}, {
    page?: number | undefined;
    size?: number | undefined;
}>, z.ZodObject<{
    limit: z.ZodDefault<z.ZodNumber>;
    offset: z.ZodDefault<z.ZodNumber>;
}, "strip", z.ZodTypeAny, {
    limit: number;
    offset: number;
}, {
    limit?: number | undefined;
    offset?: number | undefined;
}>]>>;
export declare function parseInput<T extends z.ZodTypeAny>(schema: T, value: z.input<T>): z.output<T>;
export declare function parseUnknown<T extends z.ZodTypeAny>(schema: T, value: unknown): z.output<T>;

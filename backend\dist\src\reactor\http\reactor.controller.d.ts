import { CurrentUser } from "src/auth/types";
import { ReactorService } from "../reactor.service";
import * as Dto from "./dto";
export declare class ReactorController {
    private readonly reactorService;
    constructor(reactorService: ReactorService);
    getPosts(page: number, size: number, user: CurrentUser): Promise<{
        items: {
            id: string;
            createdAt: Date;
            updatedAt: Date;
            tags: string[];
            title: {
                value: string;
                locale: "en" | "ru";
            }[];
            author: {
                id: string;
                name: {
                    value: string;
                    locale: "en" | "ru";
                }[];
                avatar: string | null;
            };
            body: {
                value: string;
                locale: "en" | "ru";
            }[];
            rating: {
                status: "like" | "dislike" | null;
                likes: number;
                dislikes: number;
            };
            usefulness: {
                value: number | null;
                count: number;
                totalValue: number | null;
            };
        }[];
        total: number;
    }>;
    getPost(id: string, user: CurrentUser): Promise<{
        id: string;
        createdAt: Date;
        updatedAt: Date;
        tags: string[];
        title: {
            value: string;
            locale: "en" | "ru";
        }[];
        author: {
            id: string;
            name: {
                value: string;
                locale: "en" | "ru";
            }[];
            avatar: string | null;
        };
        body: {
            value: string;
            locale: "en" | "ru";
        }[];
        rating: {
            status: "like" | "dislike" | null;
            likes: number;
            dislikes: number;
        };
        usefulness: {
            value: number | null;
            count: number;
            totalValue: number | null;
        };
    }>;
    createPost(body: Dto.CreatePost, user: CurrentUser): Promise<{
        id: string;
    }>;
    updatePost(id: string, body: Dto.UpdatePost, user: CurrentUser): Promise<boolean>;
    updatePostRating(id: string, body: Dto.UpdatePostRating, user: CurrentUser): Promise<{
        likes: number;
        dislikes: number;
        status: import("@prisma/client").$Enums.ReactorRatingType | null;
    }>;
    updatePostUsefulness(id: string, body: Dto.UpdatePostUsefulness, user: CurrentUser): Promise<{
        count: number;
        totalValue: number | null;
        value: number | null;
    }>;
    deletePost(id: string, body: Dto.DeletePost, user: CurrentUser): Promise<boolean>;
    getComments(entityType: Dto.GetCommentsEntityType, entityId: string, user: CurrentUser): Promise<{
        items: {
            path: string;
            id: string;
            createdAt: Date;
            updatedAt: Date;
            deletedAt: Date | null;
            isAnonymous: boolean;
            anonimityReason: string | null;
            deleteReason: string | null;
            author: {
                id: string;
                name: {
                    value: string;
                    locale: "en" | "ru";
                }[];
                avatar: string | null;
            } | null;
            body: {
                value: string;
                locale: "en" | "ru";
            }[] | null;
            rating: {
                status: "like" | "dislike" | null;
                likes: number;
                dislikes: number;
            };
            childrenCount: number;
        }[];
        total: number;
    }>;
    getComment(id: string, user: CurrentUser): Promise<{
        path: string;
        id: string;
        createdAt: Date;
        updatedAt: Date;
        deletedAt: Date | null;
        isAnonymous: boolean;
        anonimityReason: string | null;
        deleteReason: string | null;
        author: {
            id: string;
            name: {
                value: string;
                locale: "en" | "ru";
            }[];
            avatar: string | null;
        } | null;
        body: {
            value: string;
            locale: "en" | "ru";
        }[] | null;
        rating: {
            status: "like" | "dislike" | null;
            likes: number;
            dislikes: number;
        };
        childrenCount: number;
    }>;
    createComment(body: Dto.CreateComment, user: CurrentUser): Promise<{
        id: string;
    }>;
    updateComment(id: string, body: Dto.UpdateComment, user: CurrentUser): Promise<boolean>;
    updateCommentRating(id: string, body: Dto.UpdateCommentRating, user: CurrentUser): Promise<{
        likes: number;
        dislikes: number;
        status: import("@prisma/client").$Enums.ReactorRatingType | null;
    }>;
    anonimifyComment(id: string, body: Dto.AnonimifyComment, user: CurrentUser): Promise<boolean>;
    deleteComment(id: string, body: Dto.DeleteComment, user: CurrentUser): Promise<boolean>;
    getLenses(user: CurrentUser): Promise<{
        code: string;
        id: string;
        createdAt: Date;
        updatedAt: Date;
        deletedAt: Date | null;
        name: string;
        userId: string;
        sql: string;
    }[]>;
    createLens(body: Dto.CreateLens, user: CurrentUser): Promise<{
        id: string;
    }>;
    updateLens(id: string, body: Dto.UpdateLens, user: CurrentUser): Promise<boolean>;
    deleteLens(id: string, user: CurrentUser): Promise<void>;
}

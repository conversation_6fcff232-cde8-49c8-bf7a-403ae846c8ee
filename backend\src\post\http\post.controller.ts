import {
    Body,
    Controller,
    Get,
    NotFoundException,
    Param,
    Post,
    Put,
    Query,
    UseInterceptors,
    BadRequestException,
    ParseFilePipe,
    MaxFileSizeValidator,
    FileTypeValidator,
    UploadedFiles,
    UseGuards,
} from "@nestjs/common";
import { FilesInterceptor } from "@nestjs/platform-express";
import { z, <PERSON>od<PERSON><PERSON><PERSON>, ZodHelper } from "src/zod";
import { CurrentUser } from "src/auth/types";
import { HttpCurrentUser } from "src/auth/http/current-user.decorator";
import { HttpSessionAuthGuard } from "src/auth/http/session-auth.guard";
import { getError } from "src/common/errors";
import { PostService } from "../post.service";
import * as Dto from "./dto";

// Constants for file upload
const MAX_FILES_COUNT = 10;
const MAX_FILE_SIZE = 5 * 1024 * 1024; // 5MB
const ALLOWED_FILE_TYPES = ["image/jpeg", "image/png", "image/webp"];

@Controller("post")
@UseGuards(HttpSessionAuthGuard)
export class PostController {
    constructor(private readonly postService: PostService) {}

    @Get()
    async getPosts(
        @Query("page", new ZodPipe(ZodHelper.pagination.page)) page: number,
        @Query("size", new ZodPipe(ZodHelper.pagination.size)) size: number,
        @HttpCurrentUser() user: CurrentUser,
    ): Promise<Dto.Post[]> {
        const posts = await this.postService.getMany(
            {
                deletedAt: null,
            },
            { page, size },
            user,
        );

        return ZodHelper.parseInput(Dto.Posts, posts);
    }

    @Get(":id")
    async getPost(
        @Param("id", new ZodPipe(ZodHelper.Uuid)) id: string,
        @HttpCurrentUser() user: CurrentUser,
    ): Promise<Dto.Post> {
        const post = await this.postService.getOneWithRole(id, user);

        if (!post) {
            throw new NotFoundException(getError("post_not_found"));
        }

        return ZodHelper.parseInput(Dto.Post, post);
    }

    @Post()
    @UseInterceptors(FilesInterceptor("images", MAX_FILES_COUNT))
    async createPost(
        @Body("data", new ZodPipe(Dto.CreatePostInput))
        body: Dto.CreatePostInput,
        @HttpCurrentUser() user: CurrentUser,
        @UploadedFiles(
            new ParseFilePipe({
                validators: [
                    new MaxFileSizeValidator({ maxSize: MAX_FILE_SIZE }),
                    new FileTypeValidator({
                        fileType: ALLOWED_FILE_TYPES.join("|"),
                    }),
                ],
                fileIsRequired: false,
            }),
        )
        files?: Array<Express.Multer.File>,
    ): Promise<Dto.Post> {
        try {
            // Only admin can create posts
            if (user.role !== "admin") {
                throw new BadRequestException(getError("must_be_admin"));
            }

            // Create the post
            const post = await this.postService.create(
                {
                    title: body.title,
                    description: body.description,
                    status: body.status,
                    publishedAt: body.publishedAt,
                },
                user,
            );

            // Upload images if provided
            if (files && files.length > 0) {
                try {
                    await this.postService.uploadPostImages(post.id, files);
                } catch (error) {
                    console.error("Failed to upload images:", error);
                    // Continue even if image upload fails
                }
            }

            return ZodHelper.parseInput(Dto.Post, post);
        } catch (error) {
            console.error("Error processing form data:", error);

            if (error instanceof z.ZodError) {
                throw new BadRequestException({
                    message: "Invalid form data",
                    errors: error.errors,
                });
            }

            throw new BadRequestException("Failed to process form data");
        }
    }

    @Put(":id")
    async updatePost(
        @Param("id", new ZodPipe(ZodHelper.Uuid)) id: string,
        @Body(new ZodPipe(Dto.UpdatePostInput)) body: Dto.UpdatePostInput,
        @HttpCurrentUser() user: CurrentUser,
    ): Promise<Dto.Post> {
        try {
            // Check if the post exists
            const post = await this.postService.getOne(id);

            if (!post) {
                throw new NotFoundException(getError("post_not_found"));
            }

            // Only admin can update posts
            if (user.role !== "admin") {
                throw new BadRequestException(getError("must_be_admin"));
            }

            // Update the post
            const updatedPost = await this.postService.update(
                id,
                {
                    title: body.title && {
                        deleteMany: {},
                        create: body.title.map((item) => ({
                            key: "title",
                            locale: item.locale,
                            value: item.value,
                        })),
                    },
                    description: body.description && {
                        deleteMany: {},
                        create: body.description.map((item) => ({
                            key: "description",
                            locale: item.locale,
                            value: item.value,
                        })),
                    },
                    status: body.status,
                },
                user,
            );

            return ZodHelper.parseInput(Dto.Post, updatedPost);
        } catch (error) {
            console.error("Error updating post:", error);

            if (error instanceof z.ZodError) {
                throw new BadRequestException({
                    message: "Invalid form data",
                    errors: error.errors,
                });
            }

            throw new BadRequestException("Failed to update post");
        }
    }

    @Post(":id/images")
    @UseInterceptors(FilesInterceptor("images", MAX_FILES_COUNT))
    async uploadPostImages(
        @Param("id", new ZodPipe(ZodHelper.Uuid)) id: string,
        @HttpCurrentUser() user: CurrentUser,
        @UploadedFiles(
            new ParseFilePipe({
                validators: [
                    new MaxFileSizeValidator({ maxSize: MAX_FILE_SIZE }),
                    new FileTypeValidator({
                        fileType: ALLOWED_FILE_TYPES.join("|"),
                    }),
                ],
            }),
        )
        files: Array<Express.Multer.File>,
    ): Promise<ZodHelper.Image[]> {
        try {
            // Check permissions
            await this.postService.canChange(id, user);

            if (!files || files.length === 0) {
                throw new BadRequestException("No files uploaded");
            }

            const images = await this.postService.uploadPostImages(id, files);

            return ZodHelper.parseInput(z.array(ZodHelper.Image), images);
        } catch (error) {
            console.error("Error uploading images:", error);

            if (error instanceof BadRequestException) {
                throw error;
            }

            throw new BadRequestException("Failed to upload images");
        }
    }
}

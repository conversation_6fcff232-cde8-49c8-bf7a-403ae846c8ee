"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
Object.defineProperty(exports, "__esModule", { value: true });
exports.UpdatePostInput = exports.CreatePostInput = exports.Posts = exports.Post = exports.PostStatus = void 0;
const prisma = __importStar(require("@prisma/client"));
const zod_1 = require("../../zod");
exports.PostStatus = zod_1.z.nativeEnum(prisma.PostStatus);
exports.Post = zod_1.z.object({
    id: zod_1.ZodHelper.Uuid,
    title: zod_1.ZodHelper.Localizations,
    description: zod_1.ZodHelper.Localizations,
    status: exports.PostStatus,
    publishedAt: zod_1.ZodHelper.ToDateTime.nullable(),
    images: zod_1.z.array(zod_1.ZodHelper.Image).optional(),
    createdAt: zod_1.ZodHelper.ToDateTime,
    updatedAt: zod_1.ZodHelper.ToDateTime,
});
exports.Posts = zod_1.z.array(exports.Post);
exports.CreatePostInput = zod_1.ZodHelper.JsonToObject({
    title: zod_1.ZodHelper.Localizations,
    description: zod_1.ZodHelper.Localizations,
    status: exports.PostStatus,
    publishedAt: zod_1.ZodHelper.ToDateTime.nullable(),
});
exports.UpdatePostInput = zod_1.z
    .object({
    title: zod_1.ZodHelper.Localizations,
    description: zod_1.ZodHelper.Localizations,
    status: exports.PostStatus,
})
    .partial();
//# sourceMappingURL=dto.js.map
"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.ReactorLensService = void 0;
const common_1 = require("@nestjs/common");
const functions_1 = require("./functions");
let ReactorLensService = class ReactorLensService {
    generateSql(code) {
        const { tokens } = (0, functions_1.tokenize)(code);
        const ast = (0, functions_1.createAst)(tokens);
        const statement = (0, functions_1.validate)(ast);
        const sql = (0, functions_1.generateSql)(statement);
        return sql;
    }
};
exports.ReactorLensService = ReactorLensService;
exports.ReactorLensService = ReactorLensService = __decorate([
    (0, common_1.Injectable)()
], ReactorLensService);
//# sourceMappingURL=lens.service.js.map
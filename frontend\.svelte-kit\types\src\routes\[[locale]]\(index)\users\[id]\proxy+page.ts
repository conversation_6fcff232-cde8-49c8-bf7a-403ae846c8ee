// @ts-nocheck
import type { Localization } from "$lib";
import type { PageLoad } from "./$types";

import { error } from "@sveltejs/kit";
import { handleUnauthorized } from "$lib";

export interface User {
  id: string;
  email: string;
  role: "user" | "admin" | "moderator";
  name: Localization[];
  description: Localization[];
  images?: {
    id: string;
    url: string;
    source: string;
  }[];
  createdAt: string;
  updatedAt: string;
}

export const load = async ({ fetch, params }: Parameters<PageLoad>[0]) => {
  const response = await fetch(`/api/user/${params.id}`);

  handleUnauthorized(response);

  if (!response.ok) {
    throw error(500, {
      message: `Failed to fetch user: ${response.statusText}`,
    });
  }

  const user: User = await response.json();

  return {
    user,
  };
};

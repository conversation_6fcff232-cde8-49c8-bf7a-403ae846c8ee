import type { LayoutLoad, LayoutLoadEvent } from "./$types";

import { getCurrentUser, Locale } from "$lib";

export const load: LayoutLoad = (event) => {
  const routeLocale = getRouteLocale(event);

  const hrefLocale = routeLocale ? `/${routeLocale}` : "";

  return {
    routeLocale,
    preferredLocale: event.data.preferredLocale,
    locale: routeLocale ?? event.data.preferredLocale ?? "en",
    user: getCurrentUser(),
    toLocaleHref(href: string) {
      return `${hrefLocale}${href}`;
    },
  };
};

function getRouteLocale(event: LayoutLoadEvent) {
  const parsedLocale = Locale.safeParse(event.params.locale);

  if (parsedLocale.success) {
    return parsedLocale.data;
  }

  return null;
}

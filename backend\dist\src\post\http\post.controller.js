"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.PostController = void 0;
const common_1 = require("@nestjs/common");
const platform_express_1 = require("@nestjs/platform-express");
const zod_1 = require("../../zod");
const current_user_decorator_1 = require("../../auth/http/current-user.decorator");
const session_auth_guard_1 = require("../../auth/http/session-auth.guard");
const errors_1 = require("../../common/errors");
const post_service_1 = require("../post.service");
const Dto = __importStar(require("./dto"));
const MAX_FILES_COUNT = 10;
const MAX_FILE_SIZE = 5 * 1024 * 1024;
const ALLOWED_FILE_TYPES = ["image/jpeg", "image/png", "image/webp"];
let PostController = class PostController {
    constructor(postService) {
        this.postService = postService;
    }
    async getPosts(page, size, user) {
        const posts = await this.postService.getMany({
            deletedAt: null,
        }, { page, size }, user);
        return zod_1.ZodHelper.parseInput(Dto.Posts, posts);
    }
    async getPost(id, user) {
        const post = await this.postService.getOneWithRole(id, user);
        if (!post) {
            throw new common_1.NotFoundException((0, errors_1.getError)("post_not_found"));
        }
        return zod_1.ZodHelper.parseInput(Dto.Post, post);
    }
    async createPost(body, user, files) {
        try {
            if (user.role !== "admin") {
                throw new common_1.BadRequestException((0, errors_1.getError)("must_be_admin"));
            }
            const post = await this.postService.create({
                title: body.title,
                description: body.description,
                status: body.status,
                publishedAt: body.publishedAt,
            }, user);
            if (files && files.length > 0) {
                try {
                    await this.postService.uploadPostImages(post.id, files);
                }
                catch (error) {
                    console.error("Failed to upload images:", error);
                }
            }
            return zod_1.ZodHelper.parseInput(Dto.Post, post);
        }
        catch (error) {
            console.error("Error processing form data:", error);
            if (error instanceof zod_1.z.ZodError) {
                throw new common_1.BadRequestException({
                    message: "Invalid form data",
                    errors: error.errors,
                });
            }
            throw new common_1.BadRequestException("Failed to process form data");
        }
    }
    async updatePost(id, body, user) {
        try {
            const post = await this.postService.getOne(id);
            if (!post) {
                throw new common_1.NotFoundException((0, errors_1.getError)("post_not_found"));
            }
            if (user.role !== "admin") {
                throw new common_1.BadRequestException((0, errors_1.getError)("must_be_admin"));
            }
            const updatedPost = await this.postService.update(id, {
                title: body.title && {
                    deleteMany: {},
                    create: body.title.map((item) => ({
                        key: "title",
                        locale: item.locale,
                        value: item.value,
                    })),
                },
                description: body.description && {
                    deleteMany: {},
                    create: body.description.map((item) => ({
                        key: "description",
                        locale: item.locale,
                        value: item.value,
                    })),
                },
                status: body.status,
            }, user);
            return zod_1.ZodHelper.parseInput(Dto.Post, updatedPost);
        }
        catch (error) {
            console.error("Error updating post:", error);
            if (error instanceof zod_1.z.ZodError) {
                throw new common_1.BadRequestException({
                    message: "Invalid form data",
                    errors: error.errors,
                });
            }
            throw new common_1.BadRequestException("Failed to update post");
        }
    }
    async uploadPostImages(id, user, files) {
        try {
            await this.postService.canChange(id, user);
            if (!files || files.length === 0) {
                throw new common_1.BadRequestException("No files uploaded");
            }
            const images = await this.postService.uploadPostImages(id, files);
            return zod_1.ZodHelper.parseInput(zod_1.z.array(zod_1.ZodHelper.Image), images);
        }
        catch (error) {
            console.error("Error uploading images:", error);
            if (error instanceof common_1.BadRequestException) {
                throw error;
            }
            throw new common_1.BadRequestException("Failed to upload images");
        }
    }
};
exports.PostController = PostController;
__decorate([
    (0, common_1.Get)(),
    __param(0, (0, common_1.Query)("page", new zod_1.ZodPipe(zod_1.ZodHelper.pagination.page))),
    __param(1, (0, common_1.Query)("size", new zod_1.ZodPipe(zod_1.ZodHelper.pagination.size))),
    __param(2, (0, current_user_decorator_1.HttpCurrentUser)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Number, Number, Object]),
    __metadata("design:returntype", Promise)
], PostController.prototype, "getPosts", null);
__decorate([
    (0, common_1.Get)(":id"),
    __param(0, (0, common_1.Param)("id", new zod_1.ZodPipe(zod_1.ZodHelper.Uuid))),
    __param(1, (0, current_user_decorator_1.HttpCurrentUser)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, Object]),
    __metadata("design:returntype", Promise)
], PostController.prototype, "getPost", null);
__decorate([
    (0, common_1.Post)(),
    (0, common_1.UseInterceptors)((0, platform_express_1.FilesInterceptor)("images", MAX_FILES_COUNT)),
    __param(0, (0, common_1.Body)("data", new zod_1.ZodPipe(Dto.CreatePostInput))),
    __param(1, (0, current_user_decorator_1.HttpCurrentUser)()),
    __param(2, (0, common_1.UploadedFiles)(new common_1.ParseFilePipe({
        validators: [
            new common_1.MaxFileSizeValidator({ maxSize: MAX_FILE_SIZE }),
            new common_1.FileTypeValidator({
                fileType: ALLOWED_FILE_TYPES.join("|"),
            }),
        ],
        fileIsRequired: false,
    }))),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, Object, Array]),
    __metadata("design:returntype", Promise)
], PostController.prototype, "createPost", null);
__decorate([
    (0, common_1.Put)(":id"),
    __param(0, (0, common_1.Param)("id", new zod_1.ZodPipe(zod_1.ZodHelper.Uuid))),
    __param(1, (0, common_1.Body)(new zod_1.ZodPipe(Dto.UpdatePostInput))),
    __param(2, (0, current_user_decorator_1.HttpCurrentUser)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, Object, Object]),
    __metadata("design:returntype", Promise)
], PostController.prototype, "updatePost", null);
__decorate([
    (0, common_1.Post)(":id/images"),
    (0, common_1.UseInterceptors)((0, platform_express_1.FilesInterceptor)("images", MAX_FILES_COUNT)),
    __param(0, (0, common_1.Param)("id", new zod_1.ZodPipe(zod_1.ZodHelper.Uuid))),
    __param(1, (0, current_user_decorator_1.HttpCurrentUser)()),
    __param(2, (0, common_1.UploadedFiles)(new common_1.ParseFilePipe({
        validators: [
            new common_1.MaxFileSizeValidator({ maxSize: MAX_FILE_SIZE }),
            new common_1.FileTypeValidator({
                fileType: ALLOWED_FILE_TYPES.join("|"),
            }),
        ],
    }))),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, Object, Array]),
    __metadata("design:returntype", Promise)
], PostController.prototype, "uploadPostImages", null);
exports.PostController = PostController = __decorate([
    (0, common_1.Controller)("post"),
    (0, common_1.UseGuards)(session_auth_guard_1.HttpSessionAuthGuard),
    __metadata("design:paramtypes", [post_service_1.PostService])
], PostController);
//# sourceMappingURL=post.controller.js.map
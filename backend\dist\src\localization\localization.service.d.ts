import { PrismaService } from "src/prisma/prisma.service";
import { Prisma, Localization } from "@prisma/client";
import { Zod<PERSON><PERSON>per } from "src/zod";
export declare class LocalizationService {
    private readonly prisma;
    constructor(prisma: PrismaService);
    protected getFirstNonNullValue(localizations: Normalize<Pick<Localization, "locale" | "value">>[], locales: ZodHelper.Locale[]): string | null;
    getOne(id: string): Promise<{
        value: string;
        locale: import("@prisma/client").$Enums.Locale;
        id: string;
        createdAt: Date;
        updatedAt: Date;
        deletedAt: Date | null;
        key: string;
    } | null>;
    getMany(where: Prisma.LocalizationWhereInput, pagination?: {
        page: number;
        size: number;
    }): Promise<{
        value: string;
        locale: import("@prisma/client").$Enums.Locale;
        id: string;
        createdAt: Date;
        updatedAt: Date;
        deletedAt: Date | null;
        key: string;
    }[]>;
    createOne(data: Prisma.LocalizationCreateInput): Promise<{
        value: string;
        locale: import("@prisma/client").$Enums.Locale;
        id: string;
        createdAt: Date;
        updatedAt: Date;
        deletedAt: Date | null;
        key: string;
    }>;
    createMany(data: Prisma.LocalizationCreateManyInput[]): Promise<Prisma.BatchPayload>;
    updateOne(id: string, data: Prisma.LocalizationUpdateInput): Promise<{
        value: string;
        locale: import("@prisma/client").$Enums.Locale;
        id: string;
        createdAt: Date;
        updatedAt: Date;
        deletedAt: Date | null;
        key: string;
    }>;
    updateMany(where: Prisma.LocalizationWhereInput, data: Prisma.LocalizationUpdateInput): Promise<Prisma.BatchPayload>;
    softDeleteOne(id: string): Promise<{
        value: string;
        locale: import("@prisma/client").$Enums.Locale;
        id: string;
        createdAt: Date;
        updatedAt: Date;
        deletedAt: Date | null;
        key: string;
    }>;
    softDeleteMany(where: Prisma.LocalizationWhereInput): Promise<Prisma.BatchPayload>;
    deleteOne(id: string): Promise<{
        value: string;
        locale: import("@prisma/client").$Enums.Locale;
        id: string;
        createdAt: Date;
        updatedAt: Date;
        deletedAt: Date | null;
        key: string;
    }>;
    deleteMany(where: Prisma.LocalizationWhereInput): Promise<Prisma.BatchPayload>;
}

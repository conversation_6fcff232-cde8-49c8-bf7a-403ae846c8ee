"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
Object.defineProperty(exports, "__esModule", { value: true });
exports.UpdateCommuneMemberInput = exports.CreateCommuneMemberInput = exports.UpdateCommuneInput = exports.CreateCommuneInput = exports.Communes = exports.Commune = exports.CommuneMembers = exports.CommuneMember = exports.CommuneMemberType = void 0;
const prisma = __importStar(require("@prisma/client"));
const zod_1 = require("../../zod");
exports.CommuneMemberType = zod_1.z.nativeEnum(prisma.CommuneMemberType);
exports.CommuneMember = zod_1.z.object({
    id: zod_1.ZodHelper.Uuid,
    communeId: zod_1.ZodHelper.Uuid,
    actorType: exports.CommuneMemberType,
    actorId: zod_1.ZodHelper.Uuid,
    name: zod_1.ZodHelper.Localizations,
    images: zod_1.z.array(zod_1.ZodHelper.Image),
    joinedAt: zod_1.ZodHelper.ToDateTime,
    leftAt: zod_1.ZodHelper.ToDateTime.nullable(),
});
exports.CommuneMembers = zod_1.z.array(exports.CommuneMember);
exports.Commune = zod_1.z.object({
    id: zod_1.ZodHelper.Uuid,
    name: zod_1.ZodHelper.Localizations,
    description: zod_1.ZodHelper.Localizations,
    headMember: zod_1.z.object({
        actorType: exports.CommuneMemberType,
        actorId: zod_1.ZodHelper.Uuid,
        name: zod_1.ZodHelper.Localizations,
    }),
    memberCount: zod_1.ZodHelper.positiveIntSchema,
    images: zod_1.z.array(zod_1.ZodHelper.Image).optional(),
    createdAt: zod_1.ZodHelper.ToDateTime,
    updatedAt: zod_1.ZodHelper.ToDateTime,
});
exports.Communes = zod_1.z.array(exports.Commune);
exports.CreateCommuneInput = zod_1.ZodHelper.JsonToObject({
    headUserId: zod_1.ZodHelper.Uuid.optional(),
    name: zod_1.ZodHelper.Localizations,
    description: zod_1.ZodHelper.Localizations,
});
exports.UpdateCommuneInput = zod_1.z.object({
    name: zod_1.ZodHelper.Localizations,
    description: zod_1.ZodHelper.Localizations,
});
exports.CreateCommuneMemberInput = zod_1.z.object({
    actorType: exports.CommuneMemberType,
    actorId: zod_1.ZodHelper.Uuid,
});
exports.UpdateCommuneMemberInput = zod_1.z.object({});
//# sourceMappingURL=dto.js.map
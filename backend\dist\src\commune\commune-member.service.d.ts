import prisma, { Prisma } from "@prisma/client";
import { CurrentUser } from "src/auth/types";
import { BaseService } from "src/common/base-service";
import { PrismaService } from "src/prisma/prisma.service";
export declare class CommuneMemberService extends BaseService {
    private readonly prisma;
    constructor(prisma: PrismaService);
    canGet(id: string, user: CurrentUser): Promise<true>;
    check(ids: string[]): Promise<{
        id: string;
        createdAt: Date;
        updatedAt: Date;
        deletedAt: Date | null;
        communeId: string;
        actorType: prisma.$Enums.CommuneMemberType;
        actorId: string;
        isHead: boolean;
        joinedAt: Date;
        leftAt: Date | null;
    }[]>;
    private getFullMember;
    getOne(id: string): Promise<{
        name: {
            value: string;
            locale: prisma.$Enums.Locale;
            id: string;
            createdAt: Date;
            updatedAt: Date;
            deletedAt: Date | null;
            key: string;
        }[];
        images: {
            id: string;
            url: string;
            createdAt: Date;
            updatedAt: Date;
            deletedAt: Date | null;
        }[];
        id: string;
        createdAt: Date;
        updatedAt: Date;
        deletedAt: Date | null;
        communeId: string;
        actorType: prisma.$Enums.CommuneMemberType;
        actorId: string;
        isHead: boolean;
        joinedAt: Date;
        leftAt: Date | null;
    } | null>;
    getOneOrThrow(id: string): Promise<{
        name: {
            value: string;
            locale: prisma.$Enums.Locale;
            id: string;
            createdAt: Date;
            updatedAt: Date;
            deletedAt: Date | null;
            key: string;
        }[];
        images: {
            id: string;
            url: string;
            createdAt: Date;
            updatedAt: Date;
            deletedAt: Date | null;
        }[];
        id: string;
        createdAt: Date;
        updatedAt: Date;
        deletedAt: Date | null;
        communeId: string;
        actorType: prisma.$Enums.CommuneMemberType;
        actorId: string;
        isHead: boolean;
        joinedAt: Date;
        leftAt: Date | null;
    }>;
    getMany(where: Prisma.CommuneMemberWhereInput, pagination?: {
        page: number;
        size: number;
    }): Promise<{
        name: {
            value: string;
            locale: prisma.$Enums.Locale;
            id: string;
            createdAt: Date;
            updatedAt: Date;
            deletedAt: Date | null;
            key: string;
        }[];
        images: {
            id: string;
            url: string;
            createdAt: Date;
            updatedAt: Date;
            deletedAt: Date | null;
        }[];
        id: string;
        createdAt: Date;
        updatedAt: Date;
        deletedAt: Date | null;
        communeId: string;
        actorType: prisma.$Enums.CommuneMemberType;
        actorId: string;
        isHead: boolean;
        joinedAt: Date;
        leftAt: Date | null;
    }[]>;
    createOne(data: Prisma.CommuneMemberCreateInput): Promise<{
        name: {
            value: string;
            locale: prisma.$Enums.Locale;
            id: string;
            createdAt: Date;
            updatedAt: Date;
            deletedAt: Date | null;
            key: string;
        }[];
        images: {
            id: string;
            url: string;
            createdAt: Date;
            updatedAt: Date;
            deletedAt: Date | null;
        }[];
        id: string;
        createdAt: Date;
        updatedAt: Date;
        deletedAt: Date | null;
        communeId: string;
        actorType: prisma.$Enums.CommuneMemberType;
        actorId: string;
        isHead: boolean;
        joinedAt: Date;
        leftAt: Date | null;
    }>;
    createMany(data: Prisma.CommuneMemberCreateManyInput[]): Promise<Prisma.BatchPayload>;
    updateOne(id: string, data: Prisma.CommuneMemberUpdateInput): Promise<{
        name: {
            value: string;
            locale: prisma.$Enums.Locale;
            id: string;
            createdAt: Date;
            updatedAt: Date;
            deletedAt: Date | null;
            key: string;
        }[];
        images: {
            id: string;
            url: string;
            createdAt: Date;
            updatedAt: Date;
            deletedAt: Date | null;
        }[];
        id: string;
        createdAt: Date;
        updatedAt: Date;
        deletedAt: Date | null;
        communeId: string;
        actorType: prisma.$Enums.CommuneMemberType;
        actorId: string;
        isHead: boolean;
        joinedAt: Date;
        leftAt: Date | null;
    }>;
    updateMany(where: Prisma.CommuneMemberWhereInput, data: Prisma.CommuneMemberUpdateInput): Promise<Prisma.BatchPayload>;
    softDeleteOne(id: string): Promise<{
        name: {
            value: string;
            locale: prisma.$Enums.Locale;
            id: string;
            createdAt: Date;
            updatedAt: Date;
            deletedAt: Date | null;
            key: string;
        }[];
        images: {
            id: string;
            url: string;
            createdAt: Date;
            updatedAt: Date;
            deletedAt: Date | null;
        }[];
        id: string;
        createdAt: Date;
        updatedAt: Date;
        deletedAt: Date | null;
        communeId: string;
        actorType: prisma.$Enums.CommuneMemberType;
        actorId: string;
        isHead: boolean;
        joinedAt: Date;
        leftAt: Date | null;
    }>;
    softDeleteMany(where: Prisma.CommuneMemberWhereInput): Promise<Prisma.BatchPayload>;
    deleteOne(id: string): Promise<{
        id: string;
        createdAt: Date;
        updatedAt: Date;
        deletedAt: Date | null;
        communeId: string;
        actorType: prisma.$Enums.CommuneMemberType;
        actorId: string;
        isHead: boolean;
        joinedAt: Date;
        leftAt: Date | null;
    }>;
    deleteMany(where: Prisma.CommuneMemberWhereInput): Promise<Prisma.BatchPayload>;
}

import { z } from "zod";
import { <PERSON>od<PERSON><PERSON><PERSON> } from "src/zod";
export declare const votesRequired: z.<PERSON>;
export type Voting = ZodHelper.Infer<typeof Voting>;
export declare const Voting: z.ZodObject<{
    id: z.ZodString;
    votesRequired: z.<PERSON>;
    endsAt: z.<PERSON><z.ZodU<PERSON><[z.<PERSON>, z.ZodString, z.ZodDate]>, z.Zod<PERSON>ate>;
    title: z.<PERSON><z.ZodObject<{
        locale: z.ZodEnum<["en", "ru"]>;
        value: z.ZodString;
    }, "strip", z.ZodTypeAny, {
        value: string;
        locale: "en" | "ru";
    }, {
        value: string;
        locale: "en" | "ru";
    }>, "many">;
    description: z.<PERSON><z.ZodObject<{
        locale: z.ZodEnum<["en", "ru"]>;
        value: z.ZodString;
    }, "strip", z.<PERSON><PERSON><PERSON>, {
        value: string;
        locale: "en" | "ru";
    }, {
        value: string;
        locale: "en" | "ru";
    }>, "many">;
    options: z.<PERSON>y<z.ZodObject<{
        id: z.ZodString;
        title: z.ZodArray<z.ZodObject<{
            locale: z.ZodEnum<["en", "ru"]>;
            value: z.ZodString;
        }, "strip", z.ZodTypeAny, {
            value: string;
            locale: "en" | "ru";
        }, {
            value: string;
            locale: "en" | "ru";
        }>, "many">;
    }, "strip", z.ZodTypeAny, {
        id: string;
        title: {
            value: string;
            locale: "en" | "ru";
        }[];
    }, {
        id: string;
        title: {
            value: string;
            locale: "en" | "ru";
        }[];
    }>, "many">;
    createdAt: z.ZodPipeline<z.ZodUnion<[z.ZodNumber, z.ZodString, z.ZodDate]>, z.ZodDate>;
    updatedAt: z.ZodPipeline<z.ZodUnion<[z.ZodNumber, z.ZodString, z.ZodDate]>, z.ZodDate>;
}, "strip", z.ZodTypeAny, {
    options: {
        id: string;
        title: {
            value: string;
            locale: "en" | "ru";
        }[];
    }[];
    id: string;
    createdAt: Date;
    updatedAt: Date;
    description: {
        value: string;
        locale: "en" | "ru";
    }[];
    votesRequired: number;
    endsAt: Date;
    title: {
        value: string;
        locale: "en" | "ru";
    }[];
}, {
    options: {
        id: string;
        title: {
            value: string;
            locale: "en" | "ru";
        }[];
    }[];
    id: string;
    createdAt: string | number | Date;
    updatedAt: string | number | Date;
    description: {
        value: string;
        locale: "en" | "ru";
    }[];
    votesRequired: number;
    endsAt: string | number | Date;
    title: {
        value: string;
        locale: "en" | "ru";
    }[];
}>;
export declare const Votings: z.ZodArray<z.ZodObject<{
    id: z.ZodString;
    votesRequired: z.ZodNumber;
    endsAt: z.ZodPipeline<z.ZodUnion<[z.ZodNumber, z.ZodString, z.ZodDate]>, z.ZodDate>;
    title: z.ZodArray<z.ZodObject<{
        locale: z.ZodEnum<["en", "ru"]>;
        value: z.ZodString;
    }, "strip", z.ZodTypeAny, {
        value: string;
        locale: "en" | "ru";
    }, {
        value: string;
        locale: "en" | "ru";
    }>, "many">;
    description: z.ZodArray<z.ZodObject<{
        locale: z.ZodEnum<["en", "ru"]>;
        value: z.ZodString;
    }, "strip", z.ZodTypeAny, {
        value: string;
        locale: "en" | "ru";
    }, {
        value: string;
        locale: "en" | "ru";
    }>, "many">;
    options: z.ZodArray<z.ZodObject<{
        id: z.ZodString;
        title: z.ZodArray<z.ZodObject<{
            locale: z.ZodEnum<["en", "ru"]>;
            value: z.ZodString;
        }, "strip", z.ZodTypeAny, {
            value: string;
            locale: "en" | "ru";
        }, {
            value: string;
            locale: "en" | "ru";
        }>, "many">;
    }, "strip", z.ZodTypeAny, {
        id: string;
        title: {
            value: string;
            locale: "en" | "ru";
        }[];
    }, {
        id: string;
        title: {
            value: string;
            locale: "en" | "ru";
        }[];
    }>, "many">;
    createdAt: z.ZodPipeline<z.ZodUnion<[z.ZodNumber, z.ZodString, z.ZodDate]>, z.ZodDate>;
    updatedAt: z.ZodPipeline<z.ZodUnion<[z.ZodNumber, z.ZodString, z.ZodDate]>, z.ZodDate>;
}, "strip", z.ZodTypeAny, {
    options: {
        id: string;
        title: {
            value: string;
            locale: "en" | "ru";
        }[];
    }[];
    id: string;
    createdAt: Date;
    updatedAt: Date;
    description: {
        value: string;
        locale: "en" | "ru";
    }[];
    votesRequired: number;
    endsAt: Date;
    title: {
        value: string;
        locale: "en" | "ru";
    }[];
}, {
    options: {
        id: string;
        title: {
            value: string;
            locale: "en" | "ru";
        }[];
    }[];
    id: string;
    createdAt: string | number | Date;
    updatedAt: string | number | Date;
    description: {
        value: string;
        locale: "en" | "ru";
    }[];
    votesRequired: number;
    endsAt: string | number | Date;
    title: {
        value: string;
        locale: "en" | "ru";
    }[];
}>, "many">;
export type CreateVoting = ZodHelper.Infer<typeof CreateVoting>;
export declare const CreateVoting: z.ZodObject<{
    votesRequired: z.ZodNumber;
    endsAt: z.ZodPipeline<z.ZodUnion<[z.ZodNumber, z.ZodString, z.ZodDate]>, z.ZodDate>;
    title: z.ZodArray<z.ZodObject<{
        locale: z.ZodEnum<["en", "ru"]>;
        value: z.ZodString;
    }, "strip", z.ZodTypeAny, {
        value: string;
        locale: "en" | "ru";
    }, {
        value: string;
        locale: "en" | "ru";
    }>, "many">;
    description: z.ZodArray<z.ZodObject<{
        locale: z.ZodEnum<["en", "ru"]>;
        value: z.ZodString;
    }, "strip", z.ZodTypeAny, {
        value: string;
        locale: "en" | "ru";
    }, {
        value: string;
        locale: "en" | "ru";
    }>, "many">;
    options: z.ZodArray<z.ZodObject<{
        title: z.ZodArray<z.ZodObject<{
            locale: z.ZodEnum<["en", "ru"]>;
            value: z.ZodString;
        }, "strip", z.ZodTypeAny, {
            value: string;
            locale: "en" | "ru";
        }, {
            value: string;
            locale: "en" | "ru";
        }>, "many">;
    }, "strip", z.ZodTypeAny, {
        title: {
            value: string;
            locale: "en" | "ru";
        }[];
    }, {
        title: {
            value: string;
            locale: "en" | "ru";
        }[];
    }>, "many">;
}, "strip", z.ZodTypeAny, {
    options: {
        title: {
            value: string;
            locale: "en" | "ru";
        }[];
    }[];
    description: {
        value: string;
        locale: "en" | "ru";
    }[];
    votesRequired: number;
    endsAt: Date;
    title: {
        value: string;
        locale: "en" | "ru";
    }[];
}, {
    options: {
        title: {
            value: string;
            locale: "en" | "ru";
        }[];
    }[];
    description: {
        value: string;
        locale: "en" | "ru";
    }[];
    votesRequired: number;
    endsAt: string | number | Date;
    title: {
        value: string;
        locale: "en" | "ru";
    }[];
}>;

// user-role
enum UserRole {
    @@map("user_role")

    admin
    moderator
    user
}

// user
model User {
    @@map("users")

    id String @id @db.Uuid @default(uuid(7))

    referrerId String? @map("referrer_id") @db.Uuid
    referrer   User?   @relation("referrer", fields: [referrerId], references: [id])
    referrals  User[]  @relation("referrer")

    email String @unique

    role UserRole @default(user)

    images Image[] @relation("user_images")

    name        Localization[] @relation("user_name")
    description Localization[] @relation("user_description")

    titles UserTitle[] @relation("user_titles")



    reactorPosts      ReactorPost[] @relation("user_reactor_posts")
    reactorComments   ReactorComment[] @relation("user_reactor_comments")
    reactorRatings    ReactorRating[] @relation("user_reactor_ratings")
    reactorUsefulness ReactorUsefulness[] @relation("user_reactor_usefulnesses")
    reactorHubs ReactorHub[] @relation("user_reactor_hubs")
    reactorGroups ReactorGroup[] @relation("user_reactor_groups")
    reactorLenses ReactorLens[] @relation("user_reactor_lenses")

    createdAt DateTime  @map("created_at") @db.Timestamptz(3) @default(now())
    updatedAt DateTime  @map("updated_at") @db.Timestamptz(3) @updatedAt
    deletedAt DateTime? @map("deleted_at") @db.Timestamptz(3)
}

// user-title
model UserTitle {
    @@map("user_titles")

    id String @id @db.Uuid @default(uuid(7))

    ownerId String? @map("owner_id") @db.Uuid
    owner   User?   @relation("user_titles", fields: [ownerId], references: [id])

    isActive Boolean @map("is_active") @default(false)

    color String?

    createdAt DateTime  @map("created_at") @db.Timestamptz(3) @default(now())
    updatedAt DateTime  @map("updated_at") @db.Timestamptz(3) @updatedAt
    deletedAt DateTime? @map("deleted_at") @db.Timestamptz(3)
}



// user-otp
model UserOtp {
    @@map("user_otps")

    id String @id @db.Uuid @default(uuid(7))

    email String @map("email")

    otp String

    ipAddress String? @map("ip_address")
    userAgent String? @map("user_agent")

    expiresAt DateTime @map("expires_at") @db.Timestamptz(3)

    createdAt DateTime  @map("created_at") @db.Timestamptz(3) @default(now())
    updatedAt DateTime  @map("updated_at") @db.Timestamptz(3) @updatedAt
    deletedAt DateTime? @map("deleted_at") @db.Timestamptz(3)
}

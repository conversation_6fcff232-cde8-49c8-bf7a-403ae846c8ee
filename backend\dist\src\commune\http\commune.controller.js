"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.CommuneController = void 0;
const common_1 = require("@nestjs/common");
const platform_express_1 = require("@nestjs/platform-express");
const zod_1 = require("../../zod");
const errors_1 = require("../../common/errors");
const session_auth_guard_1 = require("../../auth/http/session-auth.guard");
const current_user_decorator_1 = require("../../auth/http/current-user.decorator");
const commune_service_1 = require("../commune.service");
const Dto = __importStar(require("./dto"));
const file_upload_dto_1 = require("./file-upload.dto");
const commune_member_service_1 = require("../commune-member.service");
const user_service_1 = require("../../user/user.service");
let CommuneController = class CommuneController {
    constructor(communeService, communeMemberService, userService) {
        this.communeService = communeService;
        this.communeMemberService = communeMemberService;
        this.userService = userService;
    }
    async getCommuneToReturn(commune) {
        const headMember = commune.members.find((member) => member.isHead);
        if (!headMember) {
            throw new common_1.InternalServerErrorException((0, errors_1.getError)("commune_head_member_not_found"));
        }
        const headMemberEntity = headMember.actorType === "user"
            ? await this.userService.getOne(headMember.actorId)
            : await this.communeService.getOne(headMember.actorId);
        if (!headMemberEntity) {
            throw new common_1.InternalServerErrorException((0, errors_1.getError)("commune_head_member_entity_not_found"));
        }
        const memberCount = commune.members.length;
        return {
            ...commune,
            headMember: {
                actorType: headMember.actorType,
                actorId: headMember.actorId,
                name: headMemberEntity.name,
            },
            memberCount,
            images: commune.images || [],
        };
    }
    async getCommunes(page, size) {
        const communes = await this.communeService.getMany({
            deletedAt: null,
        }, { page, size });
        const communesToReturn = await Promise.all(communes.map((commune) => this.getCommuneToReturn(commune)));
        return zod_1.ZodHelper.parseInput(Dto.Communes, communesToReturn);
    }
    async createCommune(body, user, files) {
        try {
            const commune = await this.communeService.create({
                headUserId: body.headUserId,
                name: body.name,
                description: body.description,
            }, user);
            if (files && files.length > 0) {
                try {
                    await this.communeService.uploadCommuneImages(commune.id, files);
                }
                catch (error) {
                    console.error("Failed to upload images:", error);
                }
            }
            return zod_1.ZodHelper.parseInput(Dto.Commune, await this.getCommuneToReturn(commune));
        }
        catch (error) {
            console.error("Error processing form data:", error);
            if (error instanceof zod_1.z.ZodError) {
                throw new common_1.BadRequestException({
                    message: "Invalid form data",
                    errors: error.errors,
                });
            }
            throw new common_1.BadRequestException("Failed to process form data");
        }
    }
    async uploadCommuneImages(id, user, files) {
        try {
            await this.communeService.canChange(id, user);
            if (!files || files.length === 0) {
                throw new common_1.BadRequestException("No files uploaded");
            }
            const images = await this.communeService.uploadCommuneImages(id, files);
            return zod_1.ZodHelper.parseInput(zod_1.z.array(zod_1.ZodHelper.Image), images);
        }
        catch (error) {
            console.error("Error uploading images:", error);
            if (error instanceof common_1.BadRequestException) {
                throw error;
            }
            throw new common_1.BadRequestException("Failed to upload images");
        }
    }
    async getCommune(id) {
        const commune = await this.communeService.getOne(id);
        if (!commune) {
            throw new common_1.NotFoundException((0, errors_1.getError)("commune_not_found"));
        }
        return zod_1.ZodHelper.parseInput(Dto.Commune, await this.getCommuneToReturn(commune));
    }
    async updateCommune(id, body, user) {
        try {
            const commune = await this.communeService.getOne(id);
            if (!commune) {
                throw new common_1.NotFoundException((0, errors_1.getError)("commune_not_found"));
            }
            const updatedCommune = await this.communeService.update(id, {
                name: {
                    deleteMany: {},
                    create: body.name.map((item) => ({
                        key: "name",
                        locale: item.locale,
                        value: item.value,
                    })),
                },
                description: {
                    deleteMany: {},
                    create: body.description.map((item) => ({
                        key: "description",
                        locale: item.locale,
                        value: item.value,
                    })),
                },
            }, user);
            return zod_1.ZodHelper.parseInput(Dto.Commune, await this.getCommuneToReturn(updatedCommune));
        }
        catch (error) {
            console.error("Error updating commune:", error);
            if (error instanceof zod_1.z.ZodError) {
                throw new common_1.BadRequestException({
                    message: "Invalid form data",
                    errors: error.errors,
                });
            }
            if (error instanceof common_1.NotFoundException ||
                error instanceof common_1.BadRequestException) {
                throw error;
            }
            throw new common_1.BadRequestException("Failed to update commune");
        }
    }
    async getCommuneMembers(page, size, id) {
        const communeMembers = await this.communeMemberService.getMany({
            communeId: id,
            deletedAt: null,
        }, {
            page,
            size,
        });
        return zod_1.ZodHelper.parseInput(Dto.CommuneMembers, communeMembers);
    }
    async getCommuneMember(memberId) {
        const communeMember = await this.communeMemberService.getOne(memberId);
        if (!communeMember) {
            throw new common_1.NotFoundException((0, errors_1.getError)("commune_member_not_found"));
        }
        return zod_1.ZodHelper.parseInput(Dto.CommuneMember, communeMember);
    }
    async createCommuneMember(id, createCommuneMemberInput) {
        const commune = await this.communeMemberService.createOne({
            commune: {
                connect: {
                    id,
                    deletedAt: null,
                },
            },
            actorType: createCommuneMemberInput.actorType,
            actorId: createCommuneMemberInput.actorId,
        });
        return zod_1.ZodHelper.parseInput(Dto.CommuneMember, commune);
    }
    async updateCommuneMember(id, memberId, updateCommuneMemberInput) {
        const communeMember = await this.communeMemberService.getOne(memberId);
        if (!communeMember) {
            throw new common_1.NotFoundException((0, errors_1.getError)("commune_member_not_found"));
        }
        if (communeMember.communeId !== id) {
            throw new common_1.BadRequestException("Member does not belong to the specified commune");
        }
        const updatedMember = await this.communeMemberService.updateOne(memberId, updateCommuneMemberInput);
        return zod_1.ZodHelper.parseInput(Dto.CommuneMember, updatedMember);
    }
    async deleteCommuneMember(id) {
        await this.communeMemberService.softDeleteOne(id);
        return true;
    }
};
exports.CommuneController = CommuneController;
__decorate([
    (0, common_1.Get)(),
    __param(0, (0, common_1.Query)("page", new zod_1.ZodPipe(zod_1.ZodHelper.pagination.page))),
    __param(1, (0, common_1.Query)("size", new zod_1.ZodPipe(zod_1.ZodHelper.pagination.size))),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Number, Number]),
    __metadata("design:returntype", Promise)
], CommuneController.prototype, "getCommunes", null);
__decorate([
    (0, common_1.Post)(),
    (0, common_1.UseInterceptors)((0, platform_express_1.FilesInterceptor)("images", file_upload_dto_1.MAX_FILES_COUNT)),
    __param(0, (0, common_1.Body)("data", new zod_1.ZodPipe(Dto.CreateCommuneInput))),
    __param(1, (0, current_user_decorator_1.HttpCurrentUser)()),
    __param(2, (0, common_1.UploadedFiles)(new common_1.ParseFilePipe({
        validators: [
            new common_1.MaxFileSizeValidator({ maxSize: file_upload_dto_1.MAX_FILE_SIZE }),
            new common_1.FileTypeValidator({
                fileType: file_upload_dto_1.ALLOWED_FILE_TYPES.join("|"),
            }),
        ],
        fileIsRequired: false,
    }))),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, Object, Array]),
    __metadata("design:returntype", Promise)
], CommuneController.prototype, "createCommune", null);
__decorate([
    (0, common_1.Post)(":id/images"),
    (0, common_1.UseInterceptors)((0, platform_express_1.FilesInterceptor)("images", file_upload_dto_1.MAX_FILES_COUNT)),
    __param(0, (0, common_1.Param)("id", new zod_1.ZodPipe(zod_1.ZodHelper.Uuid))),
    __param(1, (0, current_user_decorator_1.HttpCurrentUser)()),
    __param(2, (0, common_1.UploadedFiles)(new common_1.ParseFilePipe({
        validators: [
            new common_1.MaxFileSizeValidator({ maxSize: file_upload_dto_1.MAX_FILE_SIZE }),
            new common_1.FileTypeValidator({
                fileType: file_upload_dto_1.ALLOWED_FILE_TYPES.join("|"),
            }),
        ],
    }))),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, Object, Array]),
    __metadata("design:returntype", Promise)
], CommuneController.prototype, "uploadCommuneImages", null);
__decorate([
    (0, common_1.Get)(":id"),
    __param(0, (0, common_1.Param)("id", new zod_1.ZodPipe(zod_1.ZodHelper.Uuid))),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", Promise)
], CommuneController.prototype, "getCommune", null);
__decorate([
    (0, common_1.Put)(":id"),
    __param(0, (0, common_1.Param)("id", new zod_1.ZodPipe(zod_1.ZodHelper.Uuid))),
    __param(1, (0, common_1.Body)(new zod_1.ZodPipe(Dto.UpdateCommuneInput))),
    __param(2, (0, current_user_decorator_1.HttpCurrentUser)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, Object, Object]),
    __metadata("design:returntype", Promise)
], CommuneController.prototype, "updateCommune", null);
__decorate([
    (0, common_1.Get)(":id/member"),
    __param(0, (0, common_1.Query)("page", new zod_1.ZodPipe(zod_1.ZodHelper.pagination.page))),
    __param(1, (0, common_1.Query)("size", new zod_1.ZodPipe(zod_1.ZodHelper.pagination.size))),
    __param(2, (0, common_1.Param)("id", new zod_1.ZodPipe(zod_1.ZodHelper.Uuid))),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Number, Number, String]),
    __metadata("design:returntype", Promise)
], CommuneController.prototype, "getCommuneMembers", null);
__decorate([
    (0, common_1.Get)(":id/member/:memberId"),
    __param(0, (0, common_1.Param)("memberId", new zod_1.ZodPipe(zod_1.ZodHelper.Uuid))),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", Promise)
], CommuneController.prototype, "getCommuneMember", null);
__decorate([
    (0, common_1.Post)(":id/member"),
    __param(0, (0, common_1.Param)("id", new zod_1.ZodPipe(zod_1.ZodHelper.Uuid))),
    __param(1, (0, common_1.Body)(new zod_1.ZodPipe(Dto.CreateCommuneMemberInput))),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, Object]),
    __metadata("design:returntype", Promise)
], CommuneController.prototype, "createCommuneMember", null);
__decorate([
    (0, common_1.Put)(":id/member/:memberId"),
    __param(0, (0, common_1.Param)("id", new zod_1.ZodPipe(zod_1.ZodHelper.Uuid))),
    __param(1, (0, common_1.Param)("memberId", new zod_1.ZodPipe(zod_1.ZodHelper.Uuid))),
    __param(2, (0, common_1.Body)(new zod_1.ZodPipe(Dto.UpdateCommuneMemberInput))),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, String, Object]),
    __metadata("design:returntype", Promise)
], CommuneController.prototype, "updateCommuneMember", null);
__decorate([
    (0, common_1.Delete)(),
    __param(0, (0, common_1.Query)("id", new zod_1.ZodPipe(zod_1.ZodHelper.Uuid))),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", Promise)
], CommuneController.prototype, "deleteCommuneMember", null);
exports.CommuneController = CommuneController = __decorate([
    (0, common_1.Controller)("commune"),
    (0, common_1.UseGuards)(session_auth_guard_1.HttpSessionAuthGuard),
    __metadata("design:paramtypes", [commune_service_1.CommuneService,
        commune_member_service_1.CommuneMemberService,
        user_service_1.UserService])
], CommuneController);
//# sourceMappingURL=commune.controller.js.map
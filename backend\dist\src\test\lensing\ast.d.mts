export type And = {
    type: "and";
    statements: Statement[];
};
export type Or = {
    type: "or";
    statements: Statement[];
};
export type ComparisonOperator = "=" | "!=" | ">" | ">=" | "<" | "<=" | "~";
export type Comparison = {
    type: "comparison";
    identifier: string;
    operator: ComparisonOperator;
    value: string | number | (string | number)[];
};
export type Statement = And | Or | Comparison;
export declare const ast: Statement;

import { z, <PERSON><PERSON><PERSON><PERSON><PERSON> } from "src/zod";
export type CommuneMemberType = ZodHelper.Infer<typeof CommuneMemberType>;
export declare const CommuneMemberType: z.ZodNativeEnum<{
    commune: "commune";
    user: "user";
}>;
export type CommuneMember = ZodHelper.Infer<typeof CommuneMember>;
export declare const CommuneMember: z.ZodObject<{
    id: z.ZodString;
    communeId: z.ZodString;
    actorType: z.ZodNativeEnum<{
        commune: "commune";
        user: "user";
    }>;
    actorId: z.ZodString;
    name: z.<PERSON><PERSON><PERSON><z.ZodObject<{
        locale: z.ZodEnum<["en", "ru"]>;
        value: z.ZodString;
    }, "strip", z.ZodTypeAny, {
        value: string;
        locale: "en" | "ru";
    }, {
        value: string;
        locale: "en" | "ru";
    }>, "many">;
    images: z.<PERSON><z.ZodObject<{
        id: z.Zod<PERSON>;
        url: z.ZodString;
        createdAt: z.Z<PERSON><z.ZodU<PERSON>n<[z.ZodNumber, z.Zod<PERSON>, z.Zod<PERSON>ate]>, z.ZodDate>;
        updatedAt: z.ZodPipeline<z.ZodUnion<[z.ZodNumber, z.ZodString, z.ZodDate]>, z.ZodDate>;
    }, "strip", z.ZodTypeAny, {
        id: string;
        url: string;
        createdAt: Date;
        updatedAt: Date;
    }, {
        id: string;
        url: string;
        createdAt: string | number | Date;
        updatedAt: string | number | Date;
    }>, "many">;
    joinedAt: z.ZodPipeline<z.ZodUnion<[z.ZodNumber, z.ZodString, z.ZodDate]>, z.ZodDate>;
    leftAt: z.ZodNullable<z.ZodPipeline<z.ZodUnion<[z.ZodNumber, z.ZodString, z.ZodDate]>, z.ZodDate>>;
}, "strip", z.ZodTypeAny, {
    id: string;
    images: {
        id: string;
        url: string;
        createdAt: Date;
        updatedAt: Date;
    }[];
    name: {
        value: string;
        locale: "en" | "ru";
    }[];
    communeId: string;
    actorType: "commune" | "user";
    actorId: string;
    joinedAt: Date;
    leftAt: Date | null;
}, {
    id: string;
    images: {
        id: string;
        url: string;
        createdAt: string | number | Date;
        updatedAt: string | number | Date;
    }[];
    name: {
        value: string;
        locale: "en" | "ru";
    }[];
    communeId: string;
    actorType: "commune" | "user";
    actorId: string;
    joinedAt: string | number | Date;
    leftAt: string | number | Date | null;
}>;
export declare const CommuneMembers: z.ZodArray<z.ZodObject<{
    id: z.ZodString;
    communeId: z.ZodString;
    actorType: z.ZodNativeEnum<{
        commune: "commune";
        user: "user";
    }>;
    actorId: z.ZodString;
    name: z.ZodArray<z.ZodObject<{
        locale: z.ZodEnum<["en", "ru"]>;
        value: z.ZodString;
    }, "strip", z.ZodTypeAny, {
        value: string;
        locale: "en" | "ru";
    }, {
        value: string;
        locale: "en" | "ru";
    }>, "many">;
    images: z.ZodArray<z.ZodObject<{
        id: z.ZodString;
        url: z.ZodString;
        createdAt: z.ZodPipeline<z.ZodUnion<[z.ZodNumber, z.ZodString, z.ZodDate]>, z.ZodDate>;
        updatedAt: z.ZodPipeline<z.ZodUnion<[z.ZodNumber, z.ZodString, z.ZodDate]>, z.ZodDate>;
    }, "strip", z.ZodTypeAny, {
        id: string;
        url: string;
        createdAt: Date;
        updatedAt: Date;
    }, {
        id: string;
        url: string;
        createdAt: string | number | Date;
        updatedAt: string | number | Date;
    }>, "many">;
    joinedAt: z.ZodPipeline<z.ZodUnion<[z.ZodNumber, z.ZodString, z.ZodDate]>, z.ZodDate>;
    leftAt: z.ZodNullable<z.ZodPipeline<z.ZodUnion<[z.ZodNumber, z.ZodString, z.ZodDate]>, z.ZodDate>>;
}, "strip", z.ZodTypeAny, {
    id: string;
    images: {
        id: string;
        url: string;
        createdAt: Date;
        updatedAt: Date;
    }[];
    name: {
        value: string;
        locale: "en" | "ru";
    }[];
    communeId: string;
    actorType: "commune" | "user";
    actorId: string;
    joinedAt: Date;
    leftAt: Date | null;
}, {
    id: string;
    images: {
        id: string;
        url: string;
        createdAt: string | number | Date;
        updatedAt: string | number | Date;
    }[];
    name: {
        value: string;
        locale: "en" | "ru";
    }[];
    communeId: string;
    actorType: "commune" | "user";
    actorId: string;
    joinedAt: string | number | Date;
    leftAt: string | number | Date | null;
}>, "many">;
export type Commune = ZodHelper.Infer<typeof Commune>;
export declare const Commune: z.ZodObject<{
    id: z.ZodString;
    name: z.ZodArray<z.ZodObject<{
        locale: z.ZodEnum<["en", "ru"]>;
        value: z.ZodString;
    }, "strip", z.ZodTypeAny, {
        value: string;
        locale: "en" | "ru";
    }, {
        value: string;
        locale: "en" | "ru";
    }>, "many">;
    description: z.ZodArray<z.ZodObject<{
        locale: z.ZodEnum<["en", "ru"]>;
        value: z.ZodString;
    }, "strip", z.ZodTypeAny, {
        value: string;
        locale: "en" | "ru";
    }, {
        value: string;
        locale: "en" | "ru";
    }>, "many">;
    headMember: z.ZodObject<{
        actorType: z.ZodNativeEnum<{
            commune: "commune";
            user: "user";
        }>;
        actorId: z.ZodString;
        name: z.ZodArray<z.ZodObject<{
            locale: z.ZodEnum<["en", "ru"]>;
            value: z.ZodString;
        }, "strip", z.ZodTypeAny, {
            value: string;
            locale: "en" | "ru";
        }, {
            value: string;
            locale: "en" | "ru";
        }>, "many">;
    }, "strip", z.ZodTypeAny, {
        name: {
            value: string;
            locale: "en" | "ru";
        }[];
        actorType: "commune" | "user";
        actorId: string;
    }, {
        name: {
            value: string;
            locale: "en" | "ru";
        }[];
        actorType: "commune" | "user";
        actorId: string;
    }>;
    memberCount: z.ZodNumber;
    images: z.ZodOptional<z.ZodArray<z.ZodObject<{
        id: z.ZodString;
        url: z.ZodString;
        createdAt: z.ZodPipeline<z.ZodUnion<[z.ZodNumber, z.ZodString, z.ZodDate]>, z.ZodDate>;
        updatedAt: z.ZodPipeline<z.ZodUnion<[z.ZodNumber, z.ZodString, z.ZodDate]>, z.ZodDate>;
    }, "strip", z.ZodTypeAny, {
        id: string;
        url: string;
        createdAt: Date;
        updatedAt: Date;
    }, {
        id: string;
        url: string;
        createdAt: string | number | Date;
        updatedAt: string | number | Date;
    }>, "many">>;
    createdAt: z.ZodPipeline<z.ZodUnion<[z.ZodNumber, z.ZodString, z.ZodDate]>, z.ZodDate>;
    updatedAt: z.ZodPipeline<z.ZodUnion<[z.ZodNumber, z.ZodString, z.ZodDate]>, z.ZodDate>;
}, "strip", z.ZodTypeAny, {
    id: string;
    createdAt: Date;
    updatedAt: Date;
    name: {
        value: string;
        locale: "en" | "ru";
    }[];
    description: {
        value: string;
        locale: "en" | "ru";
    }[];
    headMember: {
        name: {
            value: string;
            locale: "en" | "ru";
        }[];
        actorType: "commune" | "user";
        actorId: string;
    };
    memberCount: number;
    images?: {
        id: string;
        url: string;
        createdAt: Date;
        updatedAt: Date;
    }[] | undefined;
}, {
    id: string;
    createdAt: string | number | Date;
    updatedAt: string | number | Date;
    name: {
        value: string;
        locale: "en" | "ru";
    }[];
    description: {
        value: string;
        locale: "en" | "ru";
    }[];
    headMember: {
        name: {
            value: string;
            locale: "en" | "ru";
        }[];
        actorType: "commune" | "user";
        actorId: string;
    };
    memberCount: number;
    images?: {
        id: string;
        url: string;
        createdAt: string | number | Date;
        updatedAt: string | number | Date;
    }[] | undefined;
}>;
export declare const Communes: z.ZodArray<z.ZodObject<{
    id: z.ZodString;
    name: z.ZodArray<z.ZodObject<{
        locale: z.ZodEnum<["en", "ru"]>;
        value: z.ZodString;
    }, "strip", z.ZodTypeAny, {
        value: string;
        locale: "en" | "ru";
    }, {
        value: string;
        locale: "en" | "ru";
    }>, "many">;
    description: z.ZodArray<z.ZodObject<{
        locale: z.ZodEnum<["en", "ru"]>;
        value: z.ZodString;
    }, "strip", z.ZodTypeAny, {
        value: string;
        locale: "en" | "ru";
    }, {
        value: string;
        locale: "en" | "ru";
    }>, "many">;
    headMember: z.ZodObject<{
        actorType: z.ZodNativeEnum<{
            commune: "commune";
            user: "user";
        }>;
        actorId: z.ZodString;
        name: z.ZodArray<z.ZodObject<{
            locale: z.ZodEnum<["en", "ru"]>;
            value: z.ZodString;
        }, "strip", z.ZodTypeAny, {
            value: string;
            locale: "en" | "ru";
        }, {
            value: string;
            locale: "en" | "ru";
        }>, "many">;
    }, "strip", z.ZodTypeAny, {
        name: {
            value: string;
            locale: "en" | "ru";
        }[];
        actorType: "commune" | "user";
        actorId: string;
    }, {
        name: {
            value: string;
            locale: "en" | "ru";
        }[];
        actorType: "commune" | "user";
        actorId: string;
    }>;
    memberCount: z.ZodNumber;
    images: z.ZodOptional<z.ZodArray<z.ZodObject<{
        id: z.ZodString;
        url: z.ZodString;
        createdAt: z.ZodPipeline<z.ZodUnion<[z.ZodNumber, z.ZodString, z.ZodDate]>, z.ZodDate>;
        updatedAt: z.ZodPipeline<z.ZodUnion<[z.ZodNumber, z.ZodString, z.ZodDate]>, z.ZodDate>;
    }, "strip", z.ZodTypeAny, {
        id: string;
        url: string;
        createdAt: Date;
        updatedAt: Date;
    }, {
        id: string;
        url: string;
        createdAt: string | number | Date;
        updatedAt: string | number | Date;
    }>, "many">>;
    createdAt: z.ZodPipeline<z.ZodUnion<[z.ZodNumber, z.ZodString, z.ZodDate]>, z.ZodDate>;
    updatedAt: z.ZodPipeline<z.ZodUnion<[z.ZodNumber, z.ZodString, z.ZodDate]>, z.ZodDate>;
}, "strip", z.ZodTypeAny, {
    id: string;
    createdAt: Date;
    updatedAt: Date;
    name: {
        value: string;
        locale: "en" | "ru";
    }[];
    description: {
        value: string;
        locale: "en" | "ru";
    }[];
    headMember: {
        name: {
            value: string;
            locale: "en" | "ru";
        }[];
        actorType: "commune" | "user";
        actorId: string;
    };
    memberCount: number;
    images?: {
        id: string;
        url: string;
        createdAt: Date;
        updatedAt: Date;
    }[] | undefined;
}, {
    id: string;
    createdAt: string | number | Date;
    updatedAt: string | number | Date;
    name: {
        value: string;
        locale: "en" | "ru";
    }[];
    description: {
        value: string;
        locale: "en" | "ru";
    }[];
    headMember: {
        name: {
            value: string;
            locale: "en" | "ru";
        }[];
        actorType: "commune" | "user";
        actorId: string;
    };
    memberCount: number;
    images?: {
        id: string;
        url: string;
        createdAt: string | number | Date;
        updatedAt: string | number | Date;
    }[] | undefined;
}>, "many">;
export type CreateCommuneInput = ZodHelper.Infer<typeof CreateCommuneInput>;
export declare const CreateCommuneInput: z.ZodPipeline<z.ZodEffects<z.ZodString, any, string>, z.ZodObject<{
    headUserId: z.ZodOptional<z.ZodString>;
    name: z.ZodArray<z.ZodObject<{
        locale: z.ZodEnum<["en", "ru"]>;
        value: z.ZodString;
    }, "strip", z.ZodTypeAny, {
        value: string;
        locale: "en" | "ru";
    }, {
        value: string;
        locale: "en" | "ru";
    }>, "many">;
    description: z.ZodArray<z.ZodObject<{
        locale: z.ZodEnum<["en", "ru"]>;
        value: z.ZodString;
    }, "strip", z.ZodTypeAny, {
        value: string;
        locale: "en" | "ru";
    }, {
        value: string;
        locale: "en" | "ru";
    }>, "many">;
}, "strip", z.ZodTypeAny, {
    name: {
        value: string;
        locale: "en" | "ru";
    }[];
    description: {
        value: string;
        locale: "en" | "ru";
    }[];
    headUserId?: string | undefined;
}, {
    name: {
        value: string;
        locale: "en" | "ru";
    }[];
    description: {
        value: string;
        locale: "en" | "ru";
    }[];
    headUserId?: string | undefined;
}>>;
export type UpdateCommuneInput = ZodHelper.Infer<typeof UpdateCommuneInput>;
export declare const UpdateCommuneInput: z.ZodObject<{
    name: z.ZodArray<z.ZodObject<{
        locale: z.ZodEnum<["en", "ru"]>;
        value: z.ZodString;
    }, "strip", z.ZodTypeAny, {
        value: string;
        locale: "en" | "ru";
    }, {
        value: string;
        locale: "en" | "ru";
    }>, "many">;
    description: z.ZodArray<z.ZodObject<{
        locale: z.ZodEnum<["en", "ru"]>;
        value: z.ZodString;
    }, "strip", z.ZodTypeAny, {
        value: string;
        locale: "en" | "ru";
    }, {
        value: string;
        locale: "en" | "ru";
    }>, "many">;
}, "strip", z.ZodTypeAny, {
    name: {
        value: string;
        locale: "en" | "ru";
    }[];
    description: {
        value: string;
        locale: "en" | "ru";
    }[];
}, {
    name: {
        value: string;
        locale: "en" | "ru";
    }[];
    description: {
        value: string;
        locale: "en" | "ru";
    }[];
}>;
export type CreateCommuneMemberInput = ZodHelper.Infer<typeof CreateCommuneMemberInput>;
export declare const CreateCommuneMemberInput: z.ZodObject<{
    actorType: z.ZodNativeEnum<{
        commune: "commune";
        user: "user";
    }>;
    actorId: z.ZodString;
}, "strip", z.ZodTypeAny, {
    actorType: "commune" | "user";
    actorId: string;
}, {
    actorType: "commune" | "user";
    actorId: string;
}>;
export type UpdateCommuneMemberInput = ZodHelper.Infer<typeof UpdateCommuneMemberInput>;
export declare const UpdateCommuneMemberInput: z.ZodObject<{}, "strip", z.ZodTypeAny, {}, {}>;

import { ConfigService } from "@nestjs/config";
import { PrismaService } from "src/prisma/prisma.service";
type CheckEmailOtpDto = {
    email: string;
    otp: string;
};
type CreateEmailOtpDto = {
    email: string;
    otp: string;
    ipAddress: string | null;
    userAgent: string | null;
};
type DeleteEmailOtpDto = {
    id: string;
};
export declare class EmailOtpService {
    private readonly configService;
    private readonly prisma;
    constructor(configService: ConfigService, prisma: PrismaService);
    create(createUserOtpDto: CreateEmailOtpDto): Promise<{
        id: string;
        createdAt: Date;
        updatedAt: Date;
        email: string;
        deletedAt: Date | null;
        otp: string;
        ipAddress: string | null;
        userAgent: string | null;
        expiresAt: Date;
    }>;
    check(checkUserOtpDto: CheckEmailOtpDto): Promise<{
        id: string;
        createdAt: Date;
        updatedAt: Date;
        email: string;
        deletedAt: Date | null;
        otp: string;
        ipAddress: string | null;
        userAgent: string | null;
        expiresAt: Date;
    }>;
    softDelete(deleteUserOtpDto: DeleteEmailOtpDto): Promise<void>;
    delete(deleteUserOtpDto: DeleteEmailOtpDto): Promise<void>;
}
export {};

{"version": 3, "sources": ["../../../../node_modules/@formatjs/intl-localematcher/lib/abstract/CanonicalizeLocaleList.js", "../../../../node_modules/tslib/tslib.es6.mjs", "../../../../node_modules/@formatjs/intl-localematcher/lib/abstract/languageMatching.js", "../../../../node_modules/@formatjs/intl-localematcher/lib/abstract/regions.generated.js", "../../../../node_modules/@formatjs/intl-localematcher/lib/abstract/utils.js", "../../../../node_modules/@formatjs/intl-localematcher/lib/abstract/BestFitMatcher.js", "../../../../node_modules/@formatjs/intl-localematcher/lib/abstract/CanonicalizeUValue.js", "../../../../node_modules/@formatjs/intl-localematcher/lib/abstract/CanonicalizeUnicodeLocaleId.js", "../../../../node_modules/@formatjs/intl-localematcher/lib/abstract/InsertUnicodeExtensionAndCanonicalize.js", "../../../../node_modules/@formatjs/intl-localematcher/lib/abstract/BestAvailableLocale.js", "../../../../node_modules/@formatjs/intl-localematcher/lib/abstract/LookupMatcher.js", "../../../../node_modules/@formatjs/intl-localematcher/lib/abstract/UnicodeExtensionComponents.js", "../../../../node_modules/@formatjs/intl-localematcher/lib/abstract/ResolveLocale.js", "../../../../node_modules/@formatjs/intl-localematcher/lib/abstract/LookupSupportedLocales.js", "../../../../node_modules/@formatjs/intl-localematcher/lib/index.js"], "sourcesContent": ["/**\n * http://ecma-international.org/ecma-402/7.0/index.html#sec-canonicalizelocalelist\n * @param locales\n */\nexport function CanonicalizeLocaleList(locales) {\n    return Intl.getCanonicalLocales(locales);\n}\n", "/******************************************************************************\nCopyright (c) Microsoft Corporation.\n\nPermission to use, copy, modify, and/or distribute this software for any\npurpose with or without fee is hereby granted.\n\nTHE SOFTWARE IS PROVIDED \"AS IS\" AND THE AUTHOR DISCLAIMS ALL WARRANTIES WITH\nREGARD TO THIS SOFTWARE INCLUDING ALL IMPLIED WARRANTIES OF MERCHANTABILITY\nAND FITNESS. IN NO EVENT SHALL THE AUTHOR BE LIABLE FOR ANY SPECIAL, DIRECT,\nINDIRECT, OR CONSEQUENTIAL DAMAGES OR ANY DAMAGES WHATSOEVER RESULTING FROM\nLOSS OF USE, DATA OR PROFITS, WHETHER IN AN ACTION OF CONTRACT, NEGLIGENCE OR\nOTHER TORTIOUS ACTION, ARISING OUT OF OR IN CONNECTION WITH THE USE OR\nPERFORMANCE OF THIS SOFTWARE.\n***************************************************************************** */\n/* global Reflect, Promise, SuppressedError, Symbol, Iterator */\n\nvar extendStatics = function(d, b) {\n  extendStatics = Object.setPrototypeOf ||\n      ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||\n      function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };\n  return extendStatics(d, b);\n};\n\nexport function __extends(d, b) {\n  if (typeof b !== \"function\" && b !== null)\n      throw new TypeError(\"Class extends value \" + String(b) + \" is not a constructor or null\");\n  extendStatics(d, b);\n  function __() { this.constructor = d; }\n  d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());\n}\n\nexport var __assign = function() {\n  __assign = Object.assign || function __assign(t) {\n      for (var s, i = 1, n = arguments.length; i < n; i++) {\n          s = arguments[i];\n          for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p)) t[p] = s[p];\n      }\n      return t;\n  }\n  return __assign.apply(this, arguments);\n}\n\nexport function __rest(s, e) {\n  var t = {};\n  for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p) && e.indexOf(p) < 0)\n      t[p] = s[p];\n  if (s != null && typeof Object.getOwnPropertySymbols === \"function\")\n      for (var i = 0, p = Object.getOwnPropertySymbols(s); i < p.length; i++) {\n          if (e.indexOf(p[i]) < 0 && Object.prototype.propertyIsEnumerable.call(s, p[i]))\n              t[p[i]] = s[p[i]];\n      }\n  return t;\n}\n\nexport function __decorate(decorators, target, key, desc) {\n  var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;\n  if (typeof Reflect === \"object\" && typeof Reflect.decorate === \"function\") r = Reflect.decorate(decorators, target, key, desc);\n  else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;\n  return c > 3 && r && Object.defineProperty(target, key, r), r;\n}\n\nexport function __param(paramIndex, decorator) {\n  return function (target, key) { decorator(target, key, paramIndex); }\n}\n\nexport function __esDecorate(ctor, descriptorIn, decorators, contextIn, initializers, extraInitializers) {\n  function accept(f) { if (f !== void 0 && typeof f !== \"function\") throw new TypeError(\"Function expected\"); return f; }\n  var kind = contextIn.kind, key = kind === \"getter\" ? \"get\" : kind === \"setter\" ? \"set\" : \"value\";\n  var target = !descriptorIn && ctor ? contextIn[\"static\"] ? ctor : ctor.prototype : null;\n  var descriptor = descriptorIn || (target ? Object.getOwnPropertyDescriptor(target, contextIn.name) : {});\n  var _, done = false;\n  for (var i = decorators.length - 1; i >= 0; i--) {\n      var context = {};\n      for (var p in contextIn) context[p] = p === \"access\" ? {} : contextIn[p];\n      for (var p in contextIn.access) context.access[p] = contextIn.access[p];\n      context.addInitializer = function (f) { if (done) throw new TypeError(\"Cannot add initializers after decoration has completed\"); extraInitializers.push(accept(f || null)); };\n      var result = (0, decorators[i])(kind === \"accessor\" ? { get: descriptor.get, set: descriptor.set } : descriptor[key], context);\n      if (kind === \"accessor\") {\n          if (result === void 0) continue;\n          if (result === null || typeof result !== \"object\") throw new TypeError(\"Object expected\");\n          if (_ = accept(result.get)) descriptor.get = _;\n          if (_ = accept(result.set)) descriptor.set = _;\n          if (_ = accept(result.init)) initializers.unshift(_);\n      }\n      else if (_ = accept(result)) {\n          if (kind === \"field\") initializers.unshift(_);\n          else descriptor[key] = _;\n      }\n  }\n  if (target) Object.defineProperty(target, contextIn.name, descriptor);\n  done = true;\n};\n\nexport function __runInitializers(thisArg, initializers, value) {\n  var useValue = arguments.length > 2;\n  for (var i = 0; i < initializers.length; i++) {\n      value = useValue ? initializers[i].call(thisArg, value) : initializers[i].call(thisArg);\n  }\n  return useValue ? value : void 0;\n};\n\nexport function __propKey(x) {\n  return typeof x === \"symbol\" ? x : \"\".concat(x);\n};\n\nexport function __setFunctionName(f, name, prefix) {\n  if (typeof name === \"symbol\") name = name.description ? \"[\".concat(name.description, \"]\") : \"\";\n  return Object.defineProperty(f, \"name\", { configurable: true, value: prefix ? \"\".concat(prefix, \" \", name) : name });\n};\n\nexport function __metadata(metadataKey, metadataValue) {\n  if (typeof Reflect === \"object\" && typeof Reflect.metadata === \"function\") return Reflect.metadata(metadataKey, metadataValue);\n}\n\nexport function __awaiter(thisArg, _arguments, P, generator) {\n  function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }\n  return new (P || (P = Promise))(function (resolve, reject) {\n      function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }\n      function rejected(value) { try { step(generator[\"throw\"](value)); } catch (e) { reject(e); } }\n      function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }\n      step((generator = generator.apply(thisArg, _arguments || [])).next());\n  });\n}\n\nexport function __generator(thisArg, body) {\n  var _ = { label: 0, sent: function() { if (t[0] & 1) throw t[1]; return t[1]; }, trys: [], ops: [] }, f, y, t, g = Object.create((typeof Iterator === \"function\" ? Iterator : Object).prototype);\n  return g.next = verb(0), g[\"throw\"] = verb(1), g[\"return\"] = verb(2), typeof Symbol === \"function\" && (g[Symbol.iterator] = function() { return this; }), g;\n  function verb(n) { return function (v) { return step([n, v]); }; }\n  function step(op) {\n      if (f) throw new TypeError(\"Generator is already executing.\");\n      while (g && (g = 0, op[0] && (_ = 0)), _) try {\n          if (f = 1, y && (t = op[0] & 2 ? y[\"return\"] : op[0] ? y[\"throw\"] || ((t = y[\"return\"]) && t.call(y), 0) : y.next) && !(t = t.call(y, op[1])).done) return t;\n          if (y = 0, t) op = [op[0] & 2, t.value];\n          switch (op[0]) {\n              case 0: case 1: t = op; break;\n              case 4: _.label++; return { value: op[1], done: false };\n              case 5: _.label++; y = op[1]; op = [0]; continue;\n              case 7: op = _.ops.pop(); _.trys.pop(); continue;\n              default:\n                  if (!(t = _.trys, t = t.length > 0 && t[t.length - 1]) && (op[0] === 6 || op[0] === 2)) { _ = 0; continue; }\n                  if (op[0] === 3 && (!t || (op[1] > t[0] && op[1] < t[3]))) { _.label = op[1]; break; }\n                  if (op[0] === 6 && _.label < t[1]) { _.label = t[1]; t = op; break; }\n                  if (t && _.label < t[2]) { _.label = t[2]; _.ops.push(op); break; }\n                  if (t[2]) _.ops.pop();\n                  _.trys.pop(); continue;\n          }\n          op = body.call(thisArg, _);\n      } catch (e) { op = [6, e]; y = 0; } finally { f = t = 0; }\n      if (op[0] & 5) throw op[1]; return { value: op[0] ? op[1] : void 0, done: true };\n  }\n}\n\nexport var __createBinding = Object.create ? (function(o, m, k, k2) {\n  if (k2 === undefined) k2 = k;\n  var desc = Object.getOwnPropertyDescriptor(m, k);\n  if (!desc || (\"get\" in desc ? !m.__esModule : desc.writable || desc.configurable)) {\n      desc = { enumerable: true, get: function() { return m[k]; } };\n  }\n  Object.defineProperty(o, k2, desc);\n}) : (function(o, m, k, k2) {\n  if (k2 === undefined) k2 = k;\n  o[k2] = m[k];\n});\n\nexport function __exportStar(m, o) {\n  for (var p in m) if (p !== \"default\" && !Object.prototype.hasOwnProperty.call(o, p)) __createBinding(o, m, p);\n}\n\nexport function __values(o) {\n  var s = typeof Symbol === \"function\" && Symbol.iterator, m = s && o[s], i = 0;\n  if (m) return m.call(o);\n  if (o && typeof o.length === \"number\") return {\n      next: function () {\n          if (o && i >= o.length) o = void 0;\n          return { value: o && o[i++], done: !o };\n      }\n  };\n  throw new TypeError(s ? \"Object is not iterable.\" : \"Symbol.iterator is not defined.\");\n}\n\nexport function __read(o, n) {\n  var m = typeof Symbol === \"function\" && o[Symbol.iterator];\n  if (!m) return o;\n  var i = m.call(o), r, ar = [], e;\n  try {\n      while ((n === void 0 || n-- > 0) && !(r = i.next()).done) ar.push(r.value);\n  }\n  catch (error) { e = { error: error }; }\n  finally {\n      try {\n          if (r && !r.done && (m = i[\"return\"])) m.call(i);\n      }\n      finally { if (e) throw e.error; }\n  }\n  return ar;\n}\n\n/** @deprecated */\nexport function __spread() {\n  for (var ar = [], i = 0; i < arguments.length; i++)\n      ar = ar.concat(__read(arguments[i]));\n  return ar;\n}\n\n/** @deprecated */\nexport function __spreadArrays() {\n  for (var s = 0, i = 0, il = arguments.length; i < il; i++) s += arguments[i].length;\n  for (var r = Array(s), k = 0, i = 0; i < il; i++)\n      for (var a = arguments[i], j = 0, jl = a.length; j < jl; j++, k++)\n          r[k] = a[j];\n  return r;\n}\n\nexport function __spreadArray(to, from, pack) {\n  if (pack || arguments.length === 2) for (var i = 0, l = from.length, ar; i < l; i++) {\n      if (ar || !(i in from)) {\n          if (!ar) ar = Array.prototype.slice.call(from, 0, i);\n          ar[i] = from[i];\n      }\n  }\n  return to.concat(ar || Array.prototype.slice.call(from));\n}\n\nexport function __await(v) {\n  return this instanceof __await ? (this.v = v, this) : new __await(v);\n}\n\nexport function __asyncGenerator(thisArg, _arguments, generator) {\n  if (!Symbol.asyncIterator) throw new TypeError(\"Symbol.asyncIterator is not defined.\");\n  var g = generator.apply(thisArg, _arguments || []), i, q = [];\n  return i = Object.create((typeof AsyncIterator === \"function\" ? AsyncIterator : Object).prototype), verb(\"next\"), verb(\"throw\"), verb(\"return\", awaitReturn), i[Symbol.asyncIterator] = function () { return this; }, i;\n  function awaitReturn(f) { return function (v) { return Promise.resolve(v).then(f, reject); }; }\n  function verb(n, f) { if (g[n]) { i[n] = function (v) { return new Promise(function (a, b) { q.push([n, v, a, b]) > 1 || resume(n, v); }); }; if (f) i[n] = f(i[n]); } }\n  function resume(n, v) { try { step(g[n](v)); } catch (e) { settle(q[0][3], e); } }\n  function step(r) { r.value instanceof __await ? Promise.resolve(r.value.v).then(fulfill, reject) : settle(q[0][2], r); }\n  function fulfill(value) { resume(\"next\", value); }\n  function reject(value) { resume(\"throw\", value); }\n  function settle(f, v) { if (f(v), q.shift(), q.length) resume(q[0][0], q[0][1]); }\n}\n\nexport function __asyncDelegator(o) {\n  var i, p;\n  return i = {}, verb(\"next\"), verb(\"throw\", function (e) { throw e; }), verb(\"return\"), i[Symbol.iterator] = function () { return this; }, i;\n  function verb(n, f) { i[n] = o[n] ? function (v) { return (p = !p) ? { value: __await(o[n](v)), done: false } : f ? f(v) : v; } : f; }\n}\n\nexport function __asyncValues(o) {\n  if (!Symbol.asyncIterator) throw new TypeError(\"Symbol.asyncIterator is not defined.\");\n  var m = o[Symbol.asyncIterator], i;\n  return m ? m.call(o) : (o = typeof __values === \"function\" ? __values(o) : o[Symbol.iterator](), i = {}, verb(\"next\"), verb(\"throw\"), verb(\"return\"), i[Symbol.asyncIterator] = function () { return this; }, i);\n  function verb(n) { i[n] = o[n] && function (v) { return new Promise(function (resolve, reject) { v = o[n](v), settle(resolve, reject, v.done, v.value); }); }; }\n  function settle(resolve, reject, d, v) { Promise.resolve(v).then(function(v) { resolve({ value: v, done: d }); }, reject); }\n}\n\nexport function __makeTemplateObject(cooked, raw) {\n  if (Object.defineProperty) { Object.defineProperty(cooked, \"raw\", { value: raw }); } else { cooked.raw = raw; }\n  return cooked;\n};\n\nvar __setModuleDefault = Object.create ? (function(o, v) {\n  Object.defineProperty(o, \"default\", { enumerable: true, value: v });\n}) : function(o, v) {\n  o[\"default\"] = v;\n};\n\nvar ownKeys = function(o) {\n  ownKeys = Object.getOwnPropertyNames || function (o) {\n    var ar = [];\n    for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;\n    return ar;\n  };\n  return ownKeys(o);\n};\n\nexport function __importStar(mod) {\n  if (mod && mod.__esModule) return mod;\n  var result = {};\n  if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== \"default\") __createBinding(result, mod, k[i]);\n  __setModuleDefault(result, mod);\n  return result;\n}\n\nexport function __importDefault(mod) {\n  return (mod && mod.__esModule) ? mod : { default: mod };\n}\n\nexport function __classPrivateFieldGet(receiver, state, kind, f) {\n  if (kind === \"a\" && !f) throw new TypeError(\"Private accessor was defined without a getter\");\n  if (typeof state === \"function\" ? receiver !== state || !f : !state.has(receiver)) throw new TypeError(\"Cannot read private member from an object whose class did not declare it\");\n  return kind === \"m\" ? f : kind === \"a\" ? f.call(receiver) : f ? f.value : state.get(receiver);\n}\n\nexport function __classPrivateFieldSet(receiver, state, value, kind, f) {\n  if (kind === \"m\") throw new TypeError(\"Private method is not writable\");\n  if (kind === \"a\" && !f) throw new TypeError(\"Private accessor was defined without a setter\");\n  if (typeof state === \"function\" ? receiver !== state || !f : !state.has(receiver)) throw new TypeError(\"Cannot write private member to an object whose class did not declare it\");\n  return (kind === \"a\" ? f.call(receiver, value) : f ? f.value = value : state.set(receiver, value)), value;\n}\n\nexport function __classPrivateFieldIn(state, receiver) {\n  if (receiver === null || (typeof receiver !== \"object\" && typeof receiver !== \"function\")) throw new TypeError(\"Cannot use 'in' operator on non-object\");\n  return typeof state === \"function\" ? receiver === state : state.has(receiver);\n}\n\nexport function __addDisposableResource(env, value, async) {\n  if (value !== null && value !== void 0) {\n    if (typeof value !== \"object\" && typeof value !== \"function\") throw new TypeError(\"Object expected.\");\n    var dispose, inner;\n    if (async) {\n      if (!Symbol.asyncDispose) throw new TypeError(\"Symbol.asyncDispose is not defined.\");\n      dispose = value[Symbol.asyncDispose];\n    }\n    if (dispose === void 0) {\n      if (!Symbol.dispose) throw new TypeError(\"Symbol.dispose is not defined.\");\n      dispose = value[Symbol.dispose];\n      if (async) inner = dispose;\n    }\n    if (typeof dispose !== \"function\") throw new TypeError(\"Object not disposable.\");\n    if (inner) dispose = function() { try { inner.call(this); } catch (e) { return Promise.reject(e); } };\n    env.stack.push({ value: value, dispose: dispose, async: async });\n  }\n  else if (async) {\n    env.stack.push({ async: true });\n  }\n  return value;\n}\n\nvar _SuppressedError = typeof SuppressedError === \"function\" ? SuppressedError : function (error, suppressed, message) {\n  var e = new Error(message);\n  return e.name = \"SuppressedError\", e.error = error, e.suppressed = suppressed, e;\n};\n\nexport function __disposeResources(env) {\n  function fail(e) {\n    env.error = env.hasError ? new _SuppressedError(e, env.error, \"An error was suppressed during disposal.\") : e;\n    env.hasError = true;\n  }\n  var r, s = 0;\n  function next() {\n    while (r = env.stack.pop()) {\n      try {\n        if (!r.async && s === 1) return s = 0, env.stack.push(r), Promise.resolve().then(next);\n        if (r.dispose) {\n          var result = r.dispose.call(r.value);\n          if (r.async) return s |= 2, Promise.resolve(result).then(next, function(e) { fail(e); return next(); });\n        }\n        else s |= 1;\n      }\n      catch (e) {\n        fail(e);\n      }\n    }\n    if (s === 1) return env.hasError ? Promise.reject(env.error) : Promise.resolve();\n    if (env.hasError) throw env.error;\n  }\n  return next();\n}\n\nexport function __rewriteRelativeImportExtension(path, preserveJsx) {\n  if (typeof path === \"string\" && /^\\.\\.?\\//.test(path)) {\n      return path.replace(/\\.(tsx)$|((?:\\.d)?)((?:\\.[^./]+?)?)\\.([cm]?)ts$/i, function (m, tsx, d, ext, cm) {\n          return tsx ? preserveJsx ? \".jsx\" : \".js\" : d && (!ext || !cm) ? m : (d + ext + \".\" + cm.toLowerCase() + \"js\");\n      });\n  }\n  return path;\n}\n\nexport default {\n  __extends,\n  __assign,\n  __rest,\n  __decorate,\n  __param,\n  __esDecorate,\n  __runInitializers,\n  __propKey,\n  __setFunctionName,\n  __metadata,\n  __awaiter,\n  __generator,\n  __createBinding,\n  __exportStar,\n  __values,\n  __read,\n  __spread,\n  __spreadArrays,\n  __spreadArray,\n  __await,\n  __asyncGenerator,\n  __asyncDelegator,\n  __asyncValues,\n  __makeTemplateObject,\n  __importStar,\n  __importDefault,\n  __classPrivateFieldGet,\n  __classPrivateFieldSet,\n  __classPrivateFieldIn,\n  __addDisposableResource,\n  __disposeResources,\n  __rewriteRelativeImportExtension,\n};\n", "export var data = {\n    supplemental: {\n        languageMatching: {\n            'written-new': [\n                {\n                    paradigmLocales: {\n                        _locales: 'en en_GB es es_419 pt_BR pt_PT',\n                    },\n                },\n                {\n                    $enUS: {\n                        _value: 'AS+CA+GU+MH+MP+PH+PR+UM+US+VI',\n                    },\n                },\n                {\n                    $cnsar: {\n                        _value: 'HK+MO',\n                    },\n                },\n                {\n                    $americas: {\n                        _value: '019',\n                    },\n                },\n                {\n                    $maghreb: {\n                        _value: 'MA+DZ+TN+LY+MR+EH',\n                    },\n                },\n                {\n                    no: {\n                        _desired: 'nb',\n                        _distance: '1',\n                    },\n                },\n                {\n                    bs: {\n                        _desired: 'hr',\n                        _distance: '4',\n                    },\n                },\n                {\n                    bs: {\n                        _desired: 'sh',\n                        _distance: '4',\n                    },\n                },\n                {\n                    hr: {\n                        _desired: 'sh',\n                        _distance: '4',\n                    },\n                },\n                {\n                    sr: {\n                        _desired: 'sh',\n                        _distance: '4',\n                    },\n                },\n                {\n                    aa: {\n                        _desired: 'ssy',\n                        _distance: '4',\n                    },\n                },\n                {\n                    de: {\n                        _desired: 'gsw',\n                        _distance: '4',\n                        _oneway: 'true',\n                    },\n                },\n                {\n                    de: {\n                        _desired: 'lb',\n                        _distance: '4',\n                        _oneway: 'true',\n                    },\n                },\n                {\n                    no: {\n                        _desired: 'da',\n                        _distance: '8',\n                    },\n                },\n                {\n                    nb: {\n                        _desired: 'da',\n                        _distance: '8',\n                    },\n                },\n                {\n                    ru: {\n                        _desired: 'ab',\n                        _distance: '30',\n                        _oneway: 'true',\n                    },\n                },\n                {\n                    en: {\n                        _desired: 'ach',\n                        _distance: '30',\n                        _oneway: 'true',\n                    },\n                },\n                {\n                    nl: {\n                        _desired: 'af',\n                        _distance: '20',\n                        _oneway: 'true',\n                    },\n                },\n                {\n                    en: {\n                        _desired: 'ak',\n                        _distance: '30',\n                        _oneway: 'true',\n                    },\n                },\n                {\n                    en: {\n                        _desired: 'am',\n                        _distance: '30',\n                        _oneway: 'true',\n                    },\n                },\n                {\n                    es: {\n                        _desired: 'ay',\n                        _distance: '20',\n                        _oneway: 'true',\n                    },\n                },\n                {\n                    ru: {\n                        _desired: 'az',\n                        _distance: '30',\n                        _oneway: 'true',\n                    },\n                },\n                {\n                    ur: {\n                        _desired: 'bal',\n                        _distance: '20',\n                        _oneway: 'true',\n                    },\n                },\n                {\n                    ru: {\n                        _desired: 'be',\n                        _distance: '20',\n                        _oneway: 'true',\n                    },\n                },\n                {\n                    en: {\n                        _desired: 'bem',\n                        _distance: '30',\n                        _oneway: 'true',\n                    },\n                },\n                {\n                    hi: {\n                        _desired: 'bh',\n                        _distance: '30',\n                        _oneway: 'true',\n                    },\n                },\n                {\n                    en: {\n                        _desired: 'bn',\n                        _distance: '30',\n                        _oneway: 'true',\n                    },\n                },\n                {\n                    zh: {\n                        _desired: 'bo',\n                        _distance: '20',\n                        _oneway: 'true',\n                    },\n                },\n                {\n                    fr: {\n                        _desired: 'br',\n                        _distance: '20',\n                        _oneway: 'true',\n                    },\n                },\n                {\n                    es: {\n                        _desired: 'ca',\n                        _distance: '20',\n                        _oneway: 'true',\n                    },\n                },\n                {\n                    fil: {\n                        _desired: 'ceb',\n                        _distance: '30',\n                        _oneway: 'true',\n                    },\n                },\n                {\n                    en: {\n                        _desired: 'chr',\n                        _distance: '20',\n                        _oneway: 'true',\n                    },\n                },\n                {\n                    ar: {\n                        _desired: 'ckb',\n                        _distance: '30',\n                        _oneway: 'true',\n                    },\n                },\n                {\n                    fr: {\n                        _desired: 'co',\n                        _distance: '20',\n                        _oneway: 'true',\n                    },\n                },\n                {\n                    fr: {\n                        _desired: 'crs',\n                        _distance: '20',\n                        _oneway: 'true',\n                    },\n                },\n                {\n                    sk: {\n                        _desired: 'cs',\n                        _distance: '20',\n                    },\n                },\n                {\n                    en: {\n                        _desired: 'cy',\n                        _distance: '20',\n                        _oneway: 'true',\n                    },\n                },\n                {\n                    en: {\n                        _desired: 'ee',\n                        _distance: '30',\n                        _oneway: 'true',\n                    },\n                },\n                {\n                    en: {\n                        _desired: 'eo',\n                        _distance: '30',\n                        _oneway: 'true',\n                    },\n                },\n                {\n                    es: {\n                        _desired: 'eu',\n                        _distance: '20',\n                        _oneway: 'true',\n                    },\n                },\n                {\n                    da: {\n                        _desired: 'fo',\n                        _distance: '20',\n                        _oneway: 'true',\n                    },\n                },\n                {\n                    nl: {\n                        _desired: 'fy',\n                        _distance: '20',\n                        _oneway: 'true',\n                    },\n                },\n                {\n                    en: {\n                        _desired: 'ga',\n                        _distance: '20',\n                        _oneway: 'true',\n                    },\n                },\n                {\n                    en: {\n                        _desired: 'gaa',\n                        _distance: '30',\n                        _oneway: 'true',\n                    },\n                },\n                {\n                    en: {\n                        _desired: 'gd',\n                        _distance: '20',\n                        _oneway: 'true',\n                    },\n                },\n                {\n                    es: {\n                        _desired: 'gl',\n                        _distance: '20',\n                        _oneway: 'true',\n                    },\n                },\n                {\n                    es: {\n                        _desired: 'gn',\n                        _distance: '20',\n                        _oneway: 'true',\n                    },\n                },\n                {\n                    hi: {\n                        _desired: 'gu',\n                        _distance: '30',\n                        _oneway: 'true',\n                    },\n                },\n                {\n                    en: {\n                        _desired: 'ha',\n                        _distance: '30',\n                        _oneway: 'true',\n                    },\n                },\n                {\n                    en: {\n                        _desired: 'haw',\n                        _distance: '20',\n                        _oneway: 'true',\n                    },\n                },\n                {\n                    fr: {\n                        _desired: 'ht',\n                        _distance: '20',\n                        _oneway: 'true',\n                    },\n                },\n                {\n                    ru: {\n                        _desired: 'hy',\n                        _distance: '30',\n                        _oneway: 'true',\n                    },\n                },\n                {\n                    en: {\n                        _desired: 'ia',\n                        _distance: '30',\n                        _oneway: 'true',\n                    },\n                },\n                {\n                    en: {\n                        _desired: 'ig',\n                        _distance: '30',\n                        _oneway: 'true',\n                    },\n                },\n                {\n                    en: {\n                        _desired: 'is',\n                        _distance: '20',\n                        _oneway: 'true',\n                    },\n                },\n                {\n                    id: {\n                        _desired: 'jv',\n                        _distance: '20',\n                        _oneway: 'true',\n                    },\n                },\n                {\n                    en: {\n                        _desired: 'ka',\n                        _distance: '30',\n                        _oneway: 'true',\n                    },\n                },\n                {\n                    fr: {\n                        _desired: 'kg',\n                        _distance: '30',\n                        _oneway: 'true',\n                    },\n                },\n                {\n                    ru: {\n                        _desired: 'kk',\n                        _distance: '30',\n                        _oneway: 'true',\n                    },\n                },\n                {\n                    en: {\n                        _desired: 'km',\n                        _distance: '30',\n                        _oneway: 'true',\n                    },\n                },\n                {\n                    en: {\n                        _desired: 'kn',\n                        _distance: '30',\n                        _oneway: 'true',\n                    },\n                },\n                {\n                    en: {\n                        _desired: 'kri',\n                        _distance: '30',\n                        _oneway: 'true',\n                    },\n                },\n                {\n                    tr: {\n                        _desired: 'ku',\n                        _distance: '30',\n                        _oneway: 'true',\n                    },\n                },\n                {\n                    ru: {\n                        _desired: 'ky',\n                        _distance: '30',\n                        _oneway: 'true',\n                    },\n                },\n                {\n                    it: {\n                        _desired: 'la',\n                        _distance: '20',\n                        _oneway: 'true',\n                    },\n                },\n                {\n                    en: {\n                        _desired: 'lg',\n                        _distance: '30',\n                        _oneway: 'true',\n                    },\n                },\n                {\n                    fr: {\n                        _desired: 'ln',\n                        _distance: '30',\n                        _oneway: 'true',\n                    },\n                },\n                {\n                    en: {\n                        _desired: 'lo',\n                        _distance: '30',\n                        _oneway: 'true',\n                    },\n                },\n                {\n                    en: {\n                        _desired: 'loz',\n                        _distance: '30',\n                        _oneway: 'true',\n                    },\n                },\n                {\n                    fr: {\n                        _desired: 'lua',\n                        _distance: '30',\n                        _oneway: 'true',\n                    },\n                },\n                {\n                    hi: {\n                        _desired: 'mai',\n                        _distance: '20',\n                        _oneway: 'true',\n                    },\n                },\n                {\n                    en: {\n                        _desired: 'mfe',\n                        _distance: '30',\n                        _oneway: 'true',\n                    },\n                },\n                {\n                    fr: {\n                        _desired: 'mg',\n                        _distance: '30',\n                        _oneway: 'true',\n                    },\n                },\n                {\n                    en: {\n                        _desired: 'mi',\n                        _distance: '20',\n                        _oneway: 'true',\n                    },\n                },\n                {\n                    en: {\n                        _desired: 'ml',\n                        _distance: '30',\n                        _oneway: 'true',\n                    },\n                },\n                {\n                    ru: {\n                        _desired: 'mn',\n                        _distance: '30',\n                        _oneway: 'true',\n                    },\n                },\n                {\n                    hi: {\n                        _desired: 'mr',\n                        _distance: '30',\n                        _oneway: 'true',\n                    },\n                },\n                {\n                    id: {\n                        _desired: 'ms',\n                        _distance: '30',\n                        _oneway: 'true',\n                    },\n                },\n                {\n                    en: {\n                        _desired: 'mt',\n                        _distance: '30',\n                        _oneway: 'true',\n                    },\n                },\n                {\n                    en: {\n                        _desired: 'my',\n                        _distance: '30',\n                        _oneway: 'true',\n                    },\n                },\n                {\n                    en: {\n                        _desired: 'ne',\n                        _distance: '30',\n                        _oneway: 'true',\n                    },\n                },\n                {\n                    nb: {\n                        _desired: 'nn',\n                        _distance: '20',\n                    },\n                },\n                {\n                    no: {\n                        _desired: 'nn',\n                        _distance: '20',\n                    },\n                },\n                {\n                    en: {\n                        _desired: 'nso',\n                        _distance: '30',\n                        _oneway: 'true',\n                    },\n                },\n                {\n                    en: {\n                        _desired: 'ny',\n                        _distance: '30',\n                        _oneway: 'true',\n                    },\n                },\n                {\n                    en: {\n                        _desired: 'nyn',\n                        _distance: '30',\n                        _oneway: 'true',\n                    },\n                },\n                {\n                    fr: {\n                        _desired: 'oc',\n                        _distance: '20',\n                        _oneway: 'true',\n                    },\n                },\n                {\n                    en: {\n                        _desired: 'om',\n                        _distance: '30',\n                        _oneway: 'true',\n                    },\n                },\n                {\n                    en: {\n                        _desired: 'or',\n                        _distance: '30',\n                        _oneway: 'true',\n                    },\n                },\n                {\n                    en: {\n                        _desired: 'pa',\n                        _distance: '30',\n                        _oneway: 'true',\n                    },\n                },\n                {\n                    en: {\n                        _desired: 'pcm',\n                        _distance: '20',\n                        _oneway: 'true',\n                    },\n                },\n                {\n                    en: {\n                        _desired: 'ps',\n                        _distance: '30',\n                        _oneway: 'true',\n                    },\n                },\n                {\n                    es: {\n                        _desired: 'qu',\n                        _distance: '30',\n                        _oneway: 'true',\n                    },\n                },\n                {\n                    de: {\n                        _desired: 'rm',\n                        _distance: '20',\n                        _oneway: 'true',\n                    },\n                },\n                {\n                    en: {\n                        _desired: 'rn',\n                        _distance: '30',\n                        _oneway: 'true',\n                    },\n                },\n                {\n                    fr: {\n                        _desired: 'rw',\n                        _distance: '30',\n                        _oneway: 'true',\n                    },\n                },\n                {\n                    hi: {\n                        _desired: 'sa',\n                        _distance: '30',\n                        _oneway: 'true',\n                    },\n                },\n                {\n                    en: {\n                        _desired: 'sd',\n                        _distance: '30',\n                        _oneway: 'true',\n                    },\n                },\n                {\n                    en: {\n                        _desired: 'si',\n                        _distance: '30',\n                        _oneway: 'true',\n                    },\n                },\n                {\n                    en: {\n                        _desired: 'sn',\n                        _distance: '30',\n                        _oneway: 'true',\n                    },\n                },\n                {\n                    en: {\n                        _desired: 'so',\n                        _distance: '30',\n                        _oneway: 'true',\n                    },\n                },\n                {\n                    en: {\n                        _desired: 'sq',\n                        _distance: '30',\n                        _oneway: 'true',\n                    },\n                },\n                {\n                    en: {\n                        _desired: 'st',\n                        _distance: '30',\n                        _oneway: 'true',\n                    },\n                },\n                {\n                    id: {\n                        _desired: 'su',\n                        _distance: '20',\n                        _oneway: 'true',\n                    },\n                },\n                {\n                    en: {\n                        _desired: 'sw',\n                        _distance: '30',\n                        _oneway: 'true',\n                    },\n                },\n                {\n                    en: {\n                        _desired: 'ta',\n                        _distance: '30',\n                        _oneway: 'true',\n                    },\n                },\n                {\n                    en: {\n                        _desired: 'te',\n                        _distance: '30',\n                        _oneway: 'true',\n                    },\n                },\n                {\n                    ru: {\n                        _desired: 'tg',\n                        _distance: '30',\n                        _oneway: 'true',\n                    },\n                },\n                {\n                    en: {\n                        _desired: 'ti',\n                        _distance: '30',\n                        _oneway: 'true',\n                    },\n                },\n                {\n                    ru: {\n                        _desired: 'tk',\n                        _distance: '30',\n                        _oneway: 'true',\n                    },\n                },\n                {\n                    en: {\n                        _desired: 'tlh',\n                        _distance: '30',\n                        _oneway: 'true',\n                    },\n                },\n                {\n                    en: {\n                        _desired: 'tn',\n                        _distance: '30',\n                        _oneway: 'true',\n                    },\n                },\n                {\n                    en: {\n                        _desired: 'to',\n                        _distance: '30',\n                        _oneway: 'true',\n                    },\n                },\n                {\n                    ru: {\n                        _desired: 'tt',\n                        _distance: '30',\n                        _oneway: 'true',\n                    },\n                },\n                {\n                    en: {\n                        _desired: 'tum',\n                        _distance: '30',\n                        _oneway: 'true',\n                    },\n                },\n                {\n                    zh: {\n                        _desired: 'ug',\n                        _distance: '20',\n                        _oneway: 'true',\n                    },\n                },\n                {\n                    ru: {\n                        _desired: 'uk',\n                        _distance: '20',\n                        _oneway: 'true',\n                    },\n                },\n                {\n                    en: {\n                        _desired: 'ur',\n                        _distance: '30',\n                        _oneway: 'true',\n                    },\n                },\n                {\n                    ru: {\n                        _desired: 'uz',\n                        _distance: '30',\n                        _oneway: 'true',\n                    },\n                },\n                {\n                    fr: {\n                        _desired: 'wo',\n                        _distance: '30',\n                        _oneway: 'true',\n                    },\n                },\n                {\n                    en: {\n                        _desired: 'xh',\n                        _distance: '30',\n                        _oneway: 'true',\n                    },\n                },\n                {\n                    en: {\n                        _desired: 'yi',\n                        _distance: '30',\n                        _oneway: 'true',\n                    },\n                },\n                {\n                    en: {\n                        _desired: 'yo',\n                        _distance: '30',\n                        _oneway: 'true',\n                    },\n                },\n                {\n                    zh: {\n                        _desired: 'za',\n                        _distance: '20',\n                        _oneway: 'true',\n                    },\n                },\n                {\n                    en: {\n                        _desired: 'zu',\n                        _distance: '30',\n                        _oneway: 'true',\n                    },\n                },\n                {\n                    ar: {\n                        _desired: 'aao',\n                        _distance: '10',\n                        _oneway: 'true',\n                    },\n                },\n                {\n                    ar: {\n                        _desired: 'abh',\n                        _distance: '10',\n                        _oneway: 'true',\n                    },\n                },\n                {\n                    ar: {\n                        _desired: 'abv',\n                        _distance: '10',\n                        _oneway: 'true',\n                    },\n                },\n                {\n                    ar: {\n                        _desired: 'acm',\n                        _distance: '10',\n                        _oneway: 'true',\n                    },\n                },\n                {\n                    ar: {\n                        _desired: 'acq',\n                        _distance: '10',\n                        _oneway: 'true',\n                    },\n                },\n                {\n                    ar: {\n                        _desired: 'acw',\n                        _distance: '10',\n                        _oneway: 'true',\n                    },\n                },\n                {\n                    ar: {\n                        _desired: 'acx',\n                        _distance: '10',\n                        _oneway: 'true',\n                    },\n                },\n                {\n                    ar: {\n                        _desired: 'acy',\n                        _distance: '10',\n                        _oneway: 'true',\n                    },\n                },\n                {\n                    ar: {\n                        _desired: 'adf',\n                        _distance: '10',\n                        _oneway: 'true',\n                    },\n                },\n                {\n                    ar: {\n                        _desired: 'aeb',\n                        _distance: '10',\n                        _oneway: 'true',\n                    },\n                },\n                {\n                    ar: {\n                        _desired: 'aec',\n                        _distance: '10',\n                        _oneway: 'true',\n                    },\n                },\n                {\n                    ar: {\n                        _desired: 'afb',\n                        _distance: '10',\n                        _oneway: 'true',\n                    },\n                },\n                {\n                    ar: {\n                        _desired: 'ajp',\n                        _distance: '10',\n                        _oneway: 'true',\n                    },\n                },\n                {\n                    ar: {\n                        _desired: 'apc',\n                        _distance: '10',\n                        _oneway: 'true',\n                    },\n                },\n                {\n                    ar: {\n                        _desired: 'apd',\n                        _distance: '10',\n                        _oneway: 'true',\n                    },\n                },\n                {\n                    ar: {\n                        _desired: 'arq',\n                        _distance: '10',\n                        _oneway: 'true',\n                    },\n                },\n                {\n                    ar: {\n                        _desired: 'ars',\n                        _distance: '10',\n                        _oneway: 'true',\n                    },\n                },\n                {\n                    ar: {\n                        _desired: 'ary',\n                        _distance: '10',\n                        _oneway: 'true',\n                    },\n                },\n                {\n                    ar: {\n                        _desired: 'arz',\n                        _distance: '10',\n                        _oneway: 'true',\n                    },\n                },\n                {\n                    ar: {\n                        _desired: 'auz',\n                        _distance: '10',\n                        _oneway: 'true',\n                    },\n                },\n                {\n                    ar: {\n                        _desired: 'avl',\n                        _distance: '10',\n                        _oneway: 'true',\n                    },\n                },\n                {\n                    ar: {\n                        _desired: 'ayh',\n                        _distance: '10',\n                        _oneway: 'true',\n                    },\n                },\n                {\n                    ar: {\n                        _desired: 'ayl',\n                        _distance: '10',\n                        _oneway: 'true',\n                    },\n                },\n                {\n                    ar: {\n                        _desired: 'ayn',\n                        _distance: '10',\n                        _oneway: 'true',\n                    },\n                },\n                {\n                    ar: {\n                        _desired: 'ayp',\n                        _distance: '10',\n                        _oneway: 'true',\n                    },\n                },\n                {\n                    ar: {\n                        _desired: 'bbz',\n                        _distance: '10',\n                        _oneway: 'true',\n                    },\n                },\n                {\n                    ar: {\n                        _desired: 'pga',\n                        _distance: '10',\n                        _oneway: 'true',\n                    },\n                },\n                {\n                    ar: {\n                        _desired: 'shu',\n                        _distance: '10',\n                        _oneway: 'true',\n                    },\n                },\n                {\n                    ar: {\n                        _desired: 'ssh',\n                        _distance: '10',\n                        _oneway: 'true',\n                    },\n                },\n                {\n                    az: {\n                        _desired: 'azb',\n                        _distance: '10',\n                        _oneway: 'true',\n                    },\n                },\n                {\n                    et: {\n                        _desired: 'vro',\n                        _distance: '10',\n                        _oneway: 'true',\n                    },\n                },\n                {\n                    ff: {\n                        _desired: 'ffm',\n                        _distance: '10',\n                        _oneway: 'true',\n                    },\n                },\n                {\n                    ff: {\n                        _desired: 'fub',\n                        _distance: '10',\n                        _oneway: 'true',\n                    },\n                },\n                {\n                    ff: {\n                        _desired: 'fue',\n                        _distance: '10',\n                        _oneway: 'true',\n                    },\n                },\n                {\n                    ff: {\n                        _desired: 'fuf',\n                        _distance: '10',\n                        _oneway: 'true',\n                    },\n                },\n                {\n                    ff: {\n                        _desired: 'fuh',\n                        _distance: '10',\n                        _oneway: 'true',\n                    },\n                },\n                {\n                    ff: {\n                        _desired: 'fui',\n                        _distance: '10',\n                        _oneway: 'true',\n                    },\n                },\n                {\n                    ff: {\n                        _desired: 'fuq',\n                        _distance: '10',\n                        _oneway: 'true',\n                    },\n                },\n                {\n                    ff: {\n                        _desired: 'fuv',\n                        _distance: '10',\n                        _oneway: 'true',\n                    },\n                },\n                {\n                    gn: {\n                        _desired: 'gnw',\n                        _distance: '10',\n                        _oneway: 'true',\n                    },\n                },\n                {\n                    gn: {\n                        _desired: 'gui',\n                        _distance: '10',\n                        _oneway: 'true',\n                    },\n                },\n                {\n                    gn: {\n                        _desired: 'gun',\n                        _distance: '10',\n                        _oneway: 'true',\n                    },\n                },\n                {\n                    gn: {\n                        _desired: 'nhd',\n                        _distance: '10',\n                        _oneway: 'true',\n                    },\n                },\n                {\n                    iu: {\n                        _desired: 'ikt',\n                        _distance: '10',\n                        _oneway: 'true',\n                    },\n                },\n                {\n                    kln: {\n                        _desired: 'enb',\n                        _distance: '10',\n                        _oneway: 'true',\n                    },\n                },\n                {\n                    kln: {\n                        _desired: 'eyo',\n                        _distance: '10',\n                        _oneway: 'true',\n                    },\n                },\n                {\n                    kln: {\n                        _desired: 'niq',\n                        _distance: '10',\n                        _oneway: 'true',\n                    },\n                },\n                {\n                    kln: {\n                        _desired: 'oki',\n                        _distance: '10',\n                        _oneway: 'true',\n                    },\n                },\n                {\n                    kln: {\n                        _desired: 'pko',\n                        _distance: '10',\n                        _oneway: 'true',\n                    },\n                },\n                {\n                    kln: {\n                        _desired: 'sgc',\n                        _distance: '10',\n                        _oneway: 'true',\n                    },\n                },\n                {\n                    kln: {\n                        _desired: 'tec',\n                        _distance: '10',\n                        _oneway: 'true',\n                    },\n                },\n                {\n                    kln: {\n                        _desired: 'tuy',\n                        _distance: '10',\n                        _oneway: 'true',\n                    },\n                },\n                {\n                    kok: {\n                        _desired: 'gom',\n                        _distance: '10',\n                        _oneway: 'true',\n                    },\n                },\n                {\n                    kpe: {\n                        _desired: 'gkp',\n                        _distance: '10',\n                        _oneway: 'true',\n                    },\n                },\n                {\n                    luy: {\n                        _desired: 'ida',\n                        _distance: '10',\n                        _oneway: 'true',\n                    },\n                },\n                {\n                    luy: {\n                        _desired: 'lkb',\n                        _distance: '10',\n                        _oneway: 'true',\n                    },\n                },\n                {\n                    luy: {\n                        _desired: 'lko',\n                        _distance: '10',\n                        _oneway: 'true',\n                    },\n                },\n                {\n                    luy: {\n                        _desired: 'lks',\n                        _distance: '10',\n                        _oneway: 'true',\n                    },\n                },\n                {\n                    luy: {\n                        _desired: 'lri',\n                        _distance: '10',\n                        _oneway: 'true',\n                    },\n                },\n                {\n                    luy: {\n                        _desired: 'lrm',\n                        _distance: '10',\n                        _oneway: 'true',\n                    },\n                },\n                {\n                    luy: {\n                        _desired: 'lsm',\n                        _distance: '10',\n                        _oneway: 'true',\n                    },\n                },\n                {\n                    luy: {\n                        _desired: 'lto',\n                        _distance: '10',\n                        _oneway: 'true',\n                    },\n                },\n                {\n                    luy: {\n                        _desired: 'lts',\n                        _distance: '10',\n                        _oneway: 'true',\n                    },\n                },\n                {\n                    luy: {\n                        _desired: 'lwg',\n                        _distance: '10',\n                        _oneway: 'true',\n                    },\n                },\n                {\n                    luy: {\n                        _desired: 'nle',\n                        _distance: '10',\n                        _oneway: 'true',\n                    },\n                },\n                {\n                    luy: {\n                        _desired: 'nyd',\n                        _distance: '10',\n                        _oneway: 'true',\n                    },\n                },\n                {\n                    luy: {\n                        _desired: 'rag',\n                        _distance: '10',\n                        _oneway: 'true',\n                    },\n                },\n                {\n                    lv: {\n                        _desired: 'ltg',\n                        _distance: '10',\n                        _oneway: 'true',\n                    },\n                },\n                {\n                    mg: {\n                        _desired: 'bhr',\n                        _distance: '10',\n                        _oneway: 'true',\n                    },\n                },\n                {\n                    mg: {\n                        _desired: 'bjq',\n                        _distance: '10',\n                        _oneway: 'true',\n                    },\n                },\n                {\n                    mg: {\n                        _desired: 'bmm',\n                        _distance: '10',\n                        _oneway: 'true',\n                    },\n                },\n                {\n                    mg: {\n                        _desired: 'bzc',\n                        _distance: '10',\n                        _oneway: 'true',\n                    },\n                },\n                {\n                    mg: {\n                        _desired: 'msh',\n                        _distance: '10',\n                        _oneway: 'true',\n                    },\n                },\n                {\n                    mg: {\n                        _desired: 'skg',\n                        _distance: '10',\n                        _oneway: 'true',\n                    },\n                },\n                {\n                    mg: {\n                        _desired: 'tdx',\n                        _distance: '10',\n                        _oneway: 'true',\n                    },\n                },\n                {\n                    mg: {\n                        _desired: 'tkg',\n                        _distance: '10',\n                        _oneway: 'true',\n                    },\n                },\n                {\n                    mg: {\n                        _desired: 'txy',\n                        _distance: '10',\n                        _oneway: 'true',\n                    },\n                },\n                {\n                    mg: {\n                        _desired: 'xmv',\n                        _distance: '10',\n                        _oneway: 'true',\n                    },\n                },\n                {\n                    mg: {\n                        _desired: 'xmw',\n                        _distance: '10',\n                        _oneway: 'true',\n                    },\n                },\n                {\n                    mn: {\n                        _desired: 'mvf',\n                        _distance: '10',\n                        _oneway: 'true',\n                    },\n                },\n                {\n                    ms: {\n                        _desired: 'bjn',\n                        _distance: '10',\n                        _oneway: 'true',\n                    },\n                },\n                {\n                    ms: {\n                        _desired: 'btj',\n                        _distance: '10',\n                        _oneway: 'true',\n                    },\n                },\n                {\n                    ms: {\n                        _desired: 'bve',\n                        _distance: '10',\n                        _oneway: 'true',\n                    },\n                },\n                {\n                    ms: {\n                        _desired: 'bvu',\n                        _distance: '10',\n                        _oneway: 'true',\n                    },\n                },\n                {\n                    ms: {\n                        _desired: 'coa',\n                        _distance: '10',\n                        _oneway: 'true',\n                    },\n                },\n                {\n                    ms: {\n                        _desired: 'dup',\n                        _distance: '10',\n                        _oneway: 'true',\n                    },\n                },\n                {\n                    ms: {\n                        _desired: 'hji',\n                        _distance: '10',\n                        _oneway: 'true',\n                    },\n                },\n                {\n                    ms: {\n                        _desired: 'id',\n                        _distance: '10',\n                        _oneway: 'true',\n                    },\n                },\n                {\n                    ms: {\n                        _desired: 'jak',\n                        _distance: '10',\n                        _oneway: 'true',\n                    },\n                },\n                {\n                    ms: {\n                        _desired: 'jax',\n                        _distance: '10',\n                        _oneway: 'true',\n                    },\n                },\n                {\n                    ms: {\n                        _desired: 'kvb',\n                        _distance: '10',\n                        _oneway: 'true',\n                    },\n                },\n                {\n                    ms: {\n                        _desired: 'kvr',\n                        _distance: '10',\n                        _oneway: 'true',\n                    },\n                },\n                {\n                    ms: {\n                        _desired: 'kxd',\n                        _distance: '10',\n                        _oneway: 'true',\n                    },\n                },\n                {\n                    ms: {\n                        _desired: 'lce',\n                        _distance: '10',\n                        _oneway: 'true',\n                    },\n                },\n                {\n                    ms: {\n                        _desired: 'lcf',\n                        _distance: '10',\n                        _oneway: 'true',\n                    },\n                },\n                {\n                    ms: {\n                        _desired: 'liw',\n                        _distance: '10',\n                        _oneway: 'true',\n                    },\n                },\n                {\n                    ms: {\n                        _desired: 'max',\n                        _distance: '10',\n                        _oneway: 'true',\n                    },\n                },\n                {\n                    ms: {\n                        _desired: 'meo',\n                        _distance: '10',\n                        _oneway: 'true',\n                    },\n                },\n                {\n                    ms: {\n                        _desired: 'mfa',\n                        _distance: '10',\n                        _oneway: 'true',\n                    },\n                },\n                {\n                    ms: {\n                        _desired: 'mfb',\n                        _distance: '10',\n                        _oneway: 'true',\n                    },\n                },\n                {\n                    ms: {\n                        _desired: 'min',\n                        _distance: '10',\n                        _oneway: 'true',\n                    },\n                },\n                {\n                    ms: {\n                        _desired: 'mqg',\n                        _distance: '10',\n                        _oneway: 'true',\n                    },\n                },\n                {\n                    ms: {\n                        _desired: 'msi',\n                        _distance: '10',\n                        _oneway: 'true',\n                    },\n                },\n                {\n                    ms: {\n                        _desired: 'mui',\n                        _distance: '10',\n                        _oneway: 'true',\n                    },\n                },\n                {\n                    ms: {\n                        _desired: 'orn',\n                        _distance: '10',\n                        _oneway: 'true',\n                    },\n                },\n                {\n                    ms: {\n                        _desired: 'ors',\n                        _distance: '10',\n                        _oneway: 'true',\n                    },\n                },\n                {\n                    ms: {\n                        _desired: 'pel',\n                        _distance: '10',\n                        _oneway: 'true',\n                    },\n                },\n                {\n                    ms: {\n                        _desired: 'pse',\n                        _distance: '10',\n                        _oneway: 'true',\n                    },\n                },\n                {\n                    ms: {\n                        _desired: 'tmw',\n                        _distance: '10',\n                        _oneway: 'true',\n                    },\n                },\n                {\n                    ms: {\n                        _desired: 'urk',\n                        _distance: '10',\n                        _oneway: 'true',\n                    },\n                },\n                {\n                    ms: {\n                        _desired: 'vkk',\n                        _distance: '10',\n                        _oneway: 'true',\n                    },\n                },\n                {\n                    ms: {\n                        _desired: 'vkt',\n                        _distance: '10',\n                        _oneway: 'true',\n                    },\n                },\n                {\n                    ms: {\n                        _desired: 'xmm',\n                        _distance: '10',\n                        _oneway: 'true',\n                    },\n                },\n                {\n                    ms: {\n                        _desired: 'zlm',\n                        _distance: '10',\n                        _oneway: 'true',\n                    },\n                },\n                {\n                    ms: {\n                        _desired: 'zmi',\n                        _distance: '10',\n                        _oneway: 'true',\n                    },\n                },\n                {\n                    ne: {\n                        _desired: 'dty',\n                        _distance: '10',\n                        _oneway: 'true',\n                    },\n                },\n                {\n                    om: {\n                        _desired: 'gax',\n                        _distance: '10',\n                        _oneway: 'true',\n                    },\n                },\n                {\n                    om: {\n                        _desired: 'hae',\n                        _distance: '10',\n                        _oneway: 'true',\n                    },\n                },\n                {\n                    om: {\n                        _desired: 'orc',\n                        _distance: '10',\n                        _oneway: 'true',\n                    },\n                },\n                {\n                    or: {\n                        _desired: 'spv',\n                        _distance: '10',\n                        _oneway: 'true',\n                    },\n                },\n                {\n                    ps: {\n                        _desired: 'pbt',\n                        _distance: '10',\n                        _oneway: 'true',\n                    },\n                },\n                {\n                    ps: {\n                        _desired: 'pst',\n                        _distance: '10',\n                        _oneway: 'true',\n                    },\n                },\n                {\n                    qu: {\n                        _desired: 'qub',\n                        _distance: '10',\n                        _oneway: 'true',\n                    },\n                },\n                {\n                    qu: {\n                        _desired: 'qud',\n                        _distance: '10',\n                        _oneway: 'true',\n                    },\n                },\n                {\n                    qu: {\n                        _desired: 'quf',\n                        _distance: '10',\n                        _oneway: 'true',\n                    },\n                },\n                {\n                    qu: {\n                        _desired: 'qug',\n                        _distance: '10',\n                        _oneway: 'true',\n                    },\n                },\n                {\n                    qu: {\n                        _desired: 'quh',\n                        _distance: '10',\n                        _oneway: 'true',\n                    },\n                },\n                {\n                    qu: {\n                        _desired: 'quk',\n                        _distance: '10',\n                        _oneway: 'true',\n                    },\n                },\n                {\n                    qu: {\n                        _desired: 'qul',\n                        _distance: '10',\n                        _oneway: 'true',\n                    },\n                },\n                {\n                    qu: {\n                        _desired: 'qup',\n                        _distance: '10',\n                        _oneway: 'true',\n                    },\n                },\n                {\n                    qu: {\n                        _desired: 'qur',\n                        _distance: '10',\n                        _oneway: 'true',\n                    },\n                },\n                {\n                    qu: {\n                        _desired: 'qus',\n                        _distance: '10',\n                        _oneway: 'true',\n                    },\n                },\n                {\n                    qu: {\n                        _desired: 'quw',\n                        _distance: '10',\n                        _oneway: 'true',\n                    },\n                },\n                {\n                    qu: {\n                        _desired: 'qux',\n                        _distance: '10',\n                        _oneway: 'true',\n                    },\n                },\n                {\n                    qu: {\n                        _desired: 'quy',\n                        _distance: '10',\n                        _oneway: 'true',\n                    },\n                },\n                {\n                    qu: {\n                        _desired: 'qva',\n                        _distance: '10',\n                        _oneway: 'true',\n                    },\n                },\n                {\n                    qu: {\n                        _desired: 'qvc',\n                        _distance: '10',\n                        _oneway: 'true',\n                    },\n                },\n                {\n                    qu: {\n                        _desired: 'qve',\n                        _distance: '10',\n                        _oneway: 'true',\n                    },\n                },\n                {\n                    qu: {\n                        _desired: 'qvh',\n                        _distance: '10',\n                        _oneway: 'true',\n                    },\n                },\n                {\n                    qu: {\n                        _desired: 'qvi',\n                        _distance: '10',\n                        _oneway: 'true',\n                    },\n                },\n                {\n                    qu: {\n                        _desired: 'qvj',\n                        _distance: '10',\n                        _oneway: 'true',\n                    },\n                },\n                {\n                    qu: {\n                        _desired: 'qvl',\n                        _distance: '10',\n                        _oneway: 'true',\n                    },\n                },\n                {\n                    qu: {\n                        _desired: 'qvm',\n                        _distance: '10',\n                        _oneway: 'true',\n                    },\n                },\n                {\n                    qu: {\n                        _desired: 'qvn',\n                        _distance: '10',\n                        _oneway: 'true',\n                    },\n                },\n                {\n                    qu: {\n                        _desired: 'qvo',\n                        _distance: '10',\n                        _oneway: 'true',\n                    },\n                },\n                {\n                    qu: {\n                        _desired: 'qvp',\n                        _distance: '10',\n                        _oneway: 'true',\n                    },\n                },\n                {\n                    qu: {\n                        _desired: 'qvs',\n                        _distance: '10',\n                        _oneway: 'true',\n                    },\n                },\n                {\n                    qu: {\n                        _desired: 'qvw',\n                        _distance: '10',\n                        _oneway: 'true',\n                    },\n                },\n                {\n                    qu: {\n                        _desired: 'qvz',\n                        _distance: '10',\n                        _oneway: 'true',\n                    },\n                },\n                {\n                    qu: {\n                        _desired: 'qwa',\n                        _distance: '10',\n                        _oneway: 'true',\n                    },\n                },\n                {\n                    qu: {\n                        _desired: 'qwc',\n                        _distance: '10',\n                        _oneway: 'true',\n                    },\n                },\n                {\n                    qu: {\n                        _desired: 'qwh',\n                        _distance: '10',\n                        _oneway: 'true',\n                    },\n                },\n                {\n                    qu: {\n                        _desired: 'qws',\n                        _distance: '10',\n                        _oneway: 'true',\n                    },\n                },\n                {\n                    qu: {\n                        _desired: 'qxa',\n                        _distance: '10',\n                        _oneway: 'true',\n                    },\n                },\n                {\n                    qu: {\n                        _desired: 'qxc',\n                        _distance: '10',\n                        _oneway: 'true',\n                    },\n                },\n                {\n                    qu: {\n                        _desired: 'qxh',\n                        _distance: '10',\n                        _oneway: 'true',\n                    },\n                },\n                {\n                    qu: {\n                        _desired: 'qxl',\n                        _distance: '10',\n                        _oneway: 'true',\n                    },\n                },\n                {\n                    qu: {\n                        _desired: 'qxn',\n                        _distance: '10',\n                        _oneway: 'true',\n                    },\n                },\n                {\n                    qu: {\n                        _desired: 'qxo',\n                        _distance: '10',\n                        _oneway: 'true',\n                    },\n                },\n                {\n                    qu: {\n                        _desired: 'qxp',\n                        _distance: '10',\n                        _oneway: 'true',\n                    },\n                },\n                {\n                    qu: {\n                        _desired: 'qxr',\n                        _distance: '10',\n                        _oneway: 'true',\n                    },\n                },\n                {\n                    qu: {\n                        _desired: 'qxt',\n                        _distance: '10',\n                        _oneway: 'true',\n                    },\n                },\n                {\n                    qu: {\n                        _desired: 'qxu',\n                        _distance: '10',\n                        _oneway: 'true',\n                    },\n                },\n                {\n                    qu: {\n                        _desired: 'qxw',\n                        _distance: '10',\n                        _oneway: 'true',\n                    },\n                },\n                {\n                    sc: {\n                        _desired: 'sdc',\n                        _distance: '10',\n                        _oneway: 'true',\n                    },\n                },\n                {\n                    sc: {\n                        _desired: 'sdn',\n                        _distance: '10',\n                        _oneway: 'true',\n                    },\n                },\n                {\n                    sc: {\n                        _desired: 'sro',\n                        _distance: '10',\n                        _oneway: 'true',\n                    },\n                },\n                {\n                    sq: {\n                        _desired: 'aae',\n                        _distance: '10',\n                        _oneway: 'true',\n                    },\n                },\n                {\n                    sq: {\n                        _desired: 'aat',\n                        _distance: '10',\n                        _oneway: 'true',\n                    },\n                },\n                {\n                    sq: {\n                        _desired: 'aln',\n                        _distance: '10',\n                        _oneway: 'true',\n                    },\n                },\n                {\n                    syr: {\n                        _desired: 'aii',\n                        _distance: '10',\n                        _oneway: 'true',\n                    },\n                },\n                {\n                    uz: {\n                        _desired: 'uzs',\n                        _distance: '10',\n                        _oneway: 'true',\n                    },\n                },\n                {\n                    yi: {\n                        _desired: 'yih',\n                        _distance: '10',\n                        _oneway: 'true',\n                    },\n                },\n                {\n                    zh: {\n                        _desired: 'cdo',\n                        _distance: '10',\n                        _oneway: 'true',\n                    },\n                },\n                {\n                    zh: {\n                        _desired: 'cjy',\n                        _distance: '10',\n                        _oneway: 'true',\n                    },\n                },\n                {\n                    zh: {\n                        _desired: 'cpx',\n                        _distance: '10',\n                        _oneway: 'true',\n                    },\n                },\n                {\n                    zh: {\n                        _desired: 'czh',\n                        _distance: '10',\n                        _oneway: 'true',\n                    },\n                },\n                {\n                    zh: {\n                        _desired: 'czo',\n                        _distance: '10',\n                        _oneway: 'true',\n                    },\n                },\n                {\n                    zh: {\n                        _desired: 'gan',\n                        _distance: '10',\n                        _oneway: 'true',\n                    },\n                },\n                {\n                    zh: {\n                        _desired: 'hak',\n                        _distance: '10',\n                        _oneway: 'true',\n                    },\n                },\n                {\n                    zh: {\n                        _desired: 'hsn',\n                        _distance: '10',\n                        _oneway: 'true',\n                    },\n                },\n                {\n                    zh: {\n                        _desired: 'lzh',\n                        _distance: '10',\n                        _oneway: 'true',\n                    },\n                },\n                {\n                    zh: {\n                        _desired: 'mnp',\n                        _distance: '10',\n                        _oneway: 'true',\n                    },\n                },\n                {\n                    zh: {\n                        _desired: 'nan',\n                        _distance: '10',\n                        _oneway: 'true',\n                    },\n                },\n                {\n                    zh: {\n                        _desired: 'wuu',\n                        _distance: '10',\n                        _oneway: 'true',\n                    },\n                },\n                {\n                    zh: {\n                        _desired: 'yue',\n                        _distance: '10',\n                        _oneway: 'true',\n                    },\n                },\n                {\n                    '*': {\n                        _desired: '*',\n                        _distance: '80',\n                    },\n                },\n                {\n                    'en-Latn': {\n                        _desired: 'am-Ethi',\n                        _distance: '10',\n                        _oneway: 'true',\n                    },\n                },\n                {\n                    'ru-Cyrl': {\n                        _desired: 'az-Latn',\n                        _distance: '10',\n                        _oneway: 'true',\n                    },\n                },\n                {\n                    'en-Latn': {\n                        _desired: 'bn-Beng',\n                        _distance: '10',\n                        _oneway: 'true',\n                    },\n                },\n                {\n                    'zh-Hans': {\n                        _desired: 'bo-Tibt',\n                        _distance: '10',\n                        _oneway: 'true',\n                    },\n                },\n                {\n                    'ru-Cyrl': {\n                        _desired: 'hy-Armn',\n                        _distance: '10',\n                        _oneway: 'true',\n                    },\n                },\n                {\n                    'en-Latn': {\n                        _desired: 'ka-Geor',\n                        _distance: '10',\n                        _oneway: 'true',\n                    },\n                },\n                {\n                    'en-Latn': {\n                        _desired: 'km-Khmr',\n                        _distance: '10',\n                        _oneway: 'true',\n                    },\n                },\n                {\n                    'en-Latn': {\n                        _desired: 'kn-Knda',\n                        _distance: '10',\n                        _oneway: 'true',\n                    },\n                },\n                {\n                    'en-Latn': {\n                        _desired: 'lo-Laoo',\n                        _distance: '10',\n                        _oneway: 'true',\n                    },\n                },\n                {\n                    'en-Latn': {\n                        _desired: 'ml-Mlym',\n                        _distance: '10',\n                        _oneway: 'true',\n                    },\n                },\n                {\n                    'en-Latn': {\n                        _desired: 'my-Mymr',\n                        _distance: '10',\n                        _oneway: 'true',\n                    },\n                },\n                {\n                    'en-Latn': {\n                        _desired: 'ne-Deva',\n                        _distance: '10',\n                        _oneway: 'true',\n                    },\n                },\n                {\n                    'en-Latn': {\n                        _desired: 'or-Orya',\n                        _distance: '10',\n                        _oneway: 'true',\n                    },\n                },\n                {\n                    'en-Latn': {\n                        _desired: 'pa-Guru',\n                        _distance: '10',\n                        _oneway: 'true',\n                    },\n                },\n                {\n                    'en-Latn': {\n                        _desired: 'ps-Arab',\n                        _distance: '10',\n                        _oneway: 'true',\n                    },\n                },\n                {\n                    'en-Latn': {\n                        _desired: 'sd-Arab',\n                        _distance: '10',\n                        _oneway: 'true',\n                    },\n                },\n                {\n                    'en-Latn': {\n                        _desired: 'si-Sinh',\n                        _distance: '10',\n                        _oneway: 'true',\n                    },\n                },\n                {\n                    'en-Latn': {\n                        _desired: 'ta-Taml',\n                        _distance: '10',\n                        _oneway: 'true',\n                    },\n                },\n                {\n                    'en-Latn': {\n                        _desired: 'te-Telu',\n                        _distance: '10',\n                        _oneway: 'true',\n                    },\n                },\n                {\n                    'en-Latn': {\n                        _desired: 'ti-Ethi',\n                        _distance: '10',\n                        _oneway: 'true',\n                    },\n                },\n                {\n                    'ru-Cyrl': {\n                        _desired: 'tk-Latn',\n                        _distance: '10',\n                        _oneway: 'true',\n                    },\n                },\n                {\n                    'en-Latn': {\n                        _desired: 'ur-Arab',\n                        _distance: '10',\n                        _oneway: 'true',\n                    },\n                },\n                {\n                    'ru-Cyrl': {\n                        _desired: 'uz-Latn',\n                        _distance: '10',\n                        _oneway: 'true',\n                    },\n                },\n                {\n                    'en-Latn': {\n                        _desired: 'yi-Hebr',\n                        _distance: '10',\n                        _oneway: 'true',\n                    },\n                },\n                {\n                    'sr-Cyrl': {\n                        _desired: 'sr-Latn',\n                        _distance: '5',\n                    },\n                },\n                {\n                    'zh-Hans': {\n                        _desired: 'za-Latn',\n                        _distance: '10',\n                        _oneway: 'true',\n                    },\n                },\n                {\n                    'zh-Hans': {\n                        _desired: 'zh-Hani',\n                        _distance: '20',\n                        _oneway: 'true',\n                    },\n                },\n                {\n                    'zh-Hant': {\n                        _desired: 'zh-Hani',\n                        _distance: '20',\n                        _oneway: 'true',\n                    },\n                },\n                {\n                    'ar-Arab': {\n                        _desired: 'ar-Latn',\n                        _distance: '20',\n                        _oneway: 'true',\n                    },\n                },\n                {\n                    'bn-Beng': {\n                        _desired: 'bn-Latn',\n                        _distance: '20',\n                        _oneway: 'true',\n                    },\n                },\n                {\n                    'gu-Gujr': {\n                        _desired: 'gu-Latn',\n                        _distance: '20',\n                        _oneway: 'true',\n                    },\n                },\n                {\n                    'hi-Deva': {\n                        _desired: 'hi-Latn',\n                        _distance: '20',\n                        _oneway: 'true',\n                    },\n                },\n                {\n                    'kn-Knda': {\n                        _desired: 'kn-Latn',\n                        _distance: '20',\n                        _oneway: 'true',\n                    },\n                },\n                {\n                    'ml-Mlym': {\n                        _desired: 'ml-Latn',\n                        _distance: '20',\n                        _oneway: 'true',\n                    },\n                },\n                {\n                    'mr-Deva': {\n                        _desired: 'mr-Latn',\n                        _distance: '20',\n                        _oneway: 'true',\n                    },\n                },\n                {\n                    'ta-Taml': {\n                        _desired: 'ta-Latn',\n                        _distance: '20',\n                        _oneway: 'true',\n                    },\n                },\n                {\n                    'te-Telu': {\n                        _desired: 'te-Latn',\n                        _distance: '20',\n                        _oneway: 'true',\n                    },\n                },\n                {\n                    'zh-Hans': {\n                        _desired: 'zh-Latn',\n                        _distance: '20',\n                        _oneway: 'true',\n                    },\n                },\n                {\n                    'ja-Jpan': {\n                        _desired: 'ja-Latn',\n                        _distance: '5',\n                        _oneway: 'true',\n                    },\n                },\n                {\n                    'ja-Jpan': {\n                        _desired: 'ja-Hani',\n                        _distance: '5',\n                        _oneway: 'true',\n                    },\n                },\n                {\n                    'ja-Jpan': {\n                        _desired: 'ja-Hira',\n                        _distance: '5',\n                        _oneway: 'true',\n                    },\n                },\n                {\n                    'ja-Jpan': {\n                        _desired: 'ja-Kana',\n                        _distance: '5',\n                        _oneway: 'true',\n                    },\n                },\n                {\n                    'ja-Jpan': {\n                        _desired: 'ja-Hrkt',\n                        _distance: '5',\n                        _oneway: 'true',\n                    },\n                },\n                {\n                    'ja-Hrkt': {\n                        _desired: 'ja-Hira',\n                        _distance: '5',\n                        _oneway: 'true',\n                    },\n                },\n                {\n                    'ja-Hrkt': {\n                        _desired: 'ja-Kana',\n                        _distance: '5',\n                        _oneway: 'true',\n                    },\n                },\n                {\n                    'ko-Kore': {\n                        _desired: 'ko-Hani',\n                        _distance: '5',\n                        _oneway: 'true',\n                    },\n                },\n                {\n                    'ko-Kore': {\n                        _desired: 'ko-Hang',\n                        _distance: '5',\n                        _oneway: 'true',\n                    },\n                },\n                {\n                    'ko-Kore': {\n                        _desired: 'ko-Jamo',\n                        _distance: '5',\n                        _oneway: 'true',\n                    },\n                },\n                {\n                    'ko-Hang': {\n                        _desired: 'ko-Jamo',\n                        _distance: '5',\n                        _oneway: 'true',\n                    },\n                },\n                {\n                    '*-*': {\n                        _desired: '*-*',\n                        _distance: '50',\n                    },\n                },\n                {\n                    'ar-*-$maghreb': {\n                        _desired: 'ar-*-$maghreb',\n                        _distance: '4',\n                    },\n                },\n                {\n                    'ar-*-$!maghreb': {\n                        _desired: 'ar-*-$!maghreb',\n                        _distance: '4',\n                    },\n                },\n                {\n                    'ar-*-*': {\n                        _desired: 'ar-*-*',\n                        _distance: '5',\n                    },\n                },\n                {\n                    'en-*-$enUS': {\n                        _desired: 'en-*-$enUS',\n                        _distance: '4',\n                    },\n                },\n                {\n                    'en-*-GB': {\n                        _desired: 'en-*-$!enUS',\n                        _distance: '3',\n                    },\n                },\n                {\n                    'en-*-$!enUS': {\n                        _desired: 'en-*-$!enUS',\n                        _distance: '4',\n                    },\n                },\n                {\n                    'en-*-*': {\n                        _desired: 'en-*-*',\n                        _distance: '5',\n                    },\n                },\n                {\n                    'es-*-$americas': {\n                        _desired: 'es-*-$americas',\n                        _distance: '4',\n                    },\n                },\n                {\n                    'es-*-$!americas': {\n                        _desired: 'es-*-$!americas',\n                        _distance: '4',\n                    },\n                },\n                {\n                    'es-*-*': {\n                        _desired: 'es-*-*',\n                        _distance: '5',\n                    },\n                },\n                {\n                    'pt-*-$americas': {\n                        _desired: 'pt-*-$americas',\n                        _distance: '4',\n                    },\n                },\n                {\n                    'pt-*-$!americas': {\n                        _desired: 'pt-*-$!americas',\n                        _distance: '4',\n                    },\n                },\n                {\n                    'pt-*-*': {\n                        _desired: 'pt-*-*',\n                        _distance: '5',\n                    },\n                },\n                {\n                    'zh-Hant-$cnsar': {\n                        _desired: 'zh-Hant-$cnsar',\n                        _distance: '4',\n                    },\n                },\n                {\n                    'zh-Hant-$!cnsar': {\n                        _desired: 'zh-Hant-$!cnsar',\n                        _distance: '4',\n                    },\n                },\n                {\n                    'zh-Hant-*': {\n                        _desired: 'zh-Hant-*',\n                        _distance: '5',\n                    },\n                },\n                {\n                    '*-*-*': {\n                        _desired: '*-*-*',\n                        _distance: '4',\n                    },\n                },\n            ],\n        },\n    },\n};\n", "// This file is generated from regions-gen.ts\nexport var regions = {\n    \"001\": [\n        \"001\",\n        \"001-status-grouping\",\n        \"002\",\n        \"005\",\n        \"009\",\n        \"011\",\n        \"013\",\n        \"014\",\n        \"015\",\n        \"017\",\n        \"018\",\n        \"019\",\n        \"021\",\n        \"029\",\n        \"030\",\n        \"034\",\n        \"035\",\n        \"039\",\n        \"053\",\n        \"054\",\n        \"057\",\n        \"061\",\n        \"142\",\n        \"143\",\n        \"145\",\n        \"150\",\n        \"151\",\n        \"154\",\n        \"155\",\n        \"AC\",\n        \"AD\",\n        \"AE\",\n        \"AF\",\n        \"AG\",\n        \"AI\",\n        \"AL\",\n        \"AM\",\n        \"AO\",\n        \"AQ\",\n        \"AR\",\n        \"AS\",\n        \"AT\",\n        \"AU\",\n        \"AW\",\n        \"AX\",\n        \"AZ\",\n        \"BA\",\n        \"BB\",\n        \"BD\",\n        \"BE\",\n        \"BF\",\n        \"BG\",\n        \"BH\",\n        \"BI\",\n        \"BJ\",\n        \"BL\",\n        \"BM\",\n        \"BN\",\n        \"BO\",\n        \"BQ\",\n        \"BR\",\n        \"BS\",\n        \"BT\",\n        \"BV\",\n        \"BW\",\n        \"BY\",\n        \"BZ\",\n        \"CA\",\n        \"CC\",\n        \"CD\",\n        \"CF\",\n        \"CG\",\n        \"CH\",\n        \"CI\",\n        \"CK\",\n        \"CL\",\n        \"CM\",\n        \"CN\",\n        \"CO\",\n        \"CP\",\n        \"CQ\",\n        \"CR\",\n        \"CU\",\n        \"CV\",\n        \"CW\",\n        \"CX\",\n        \"CY\",\n        \"CZ\",\n        \"DE\",\n        \"DG\",\n        \"DJ\",\n        \"DK\",\n        \"DM\",\n        \"DO\",\n        \"DZ\",\n        \"EA\",\n        \"EC\",\n        \"EE\",\n        \"EG\",\n        \"EH\",\n        \"ER\",\n        \"ES\",\n        \"ET\",\n        \"EU\",\n        \"EZ\",\n        \"FI\",\n        \"FJ\",\n        \"FK\",\n        \"FM\",\n        \"FO\",\n        \"FR\",\n        \"GA\",\n        \"GB\",\n        \"GD\",\n        \"GE\",\n        \"GF\",\n        \"GG\",\n        \"GH\",\n        \"GI\",\n        \"GL\",\n        \"GM\",\n        \"GN\",\n        \"GP\",\n        \"GQ\",\n        \"GR\",\n        \"GS\",\n        \"GT\",\n        \"GU\",\n        \"GW\",\n        \"GY\",\n        \"HK\",\n        \"HM\",\n        \"HN\",\n        \"HR\",\n        \"HT\",\n        \"HU\",\n        \"IC\",\n        \"ID\",\n        \"IE\",\n        \"IL\",\n        \"IM\",\n        \"IN\",\n        \"IO\",\n        \"IQ\",\n        \"IR\",\n        \"IS\",\n        \"IT\",\n        \"JE\",\n        \"JM\",\n        \"JO\",\n        \"JP\",\n        \"KE\",\n        \"KG\",\n        \"KH\",\n        \"KI\",\n        \"KM\",\n        \"KN\",\n        \"KP\",\n        \"KR\",\n        \"KW\",\n        \"KY\",\n        \"KZ\",\n        \"LA\",\n        \"LB\",\n        \"LC\",\n        \"LI\",\n        \"LK\",\n        \"LR\",\n        \"LS\",\n        \"LT\",\n        \"LU\",\n        \"LV\",\n        \"LY\",\n        \"MA\",\n        \"MC\",\n        \"MD\",\n        \"ME\",\n        \"MF\",\n        \"MG\",\n        \"MH\",\n        \"MK\",\n        \"ML\",\n        \"MM\",\n        \"MN\",\n        \"MO\",\n        \"MP\",\n        \"MQ\",\n        \"MR\",\n        \"MS\",\n        \"MT\",\n        \"MU\",\n        \"MV\",\n        \"MW\",\n        \"MX\",\n        \"MY\",\n        \"MZ\",\n        \"NA\",\n        \"NC\",\n        \"NE\",\n        \"NF\",\n        \"NG\",\n        \"NI\",\n        \"NL\",\n        \"NO\",\n        \"NP\",\n        \"NR\",\n        \"NU\",\n        \"NZ\",\n        \"OM\",\n        \"PA\",\n        \"PE\",\n        \"PF\",\n        \"PG\",\n        \"PH\",\n        \"PK\",\n        \"PL\",\n        \"PM\",\n        \"PN\",\n        \"PR\",\n        \"PS\",\n        \"PT\",\n        \"PW\",\n        \"PY\",\n        \"QA\",\n        \"QO\",\n        \"RE\",\n        \"RO\",\n        \"RS\",\n        \"RU\",\n        \"RW\",\n        \"SA\",\n        \"SB\",\n        \"SC\",\n        \"SD\",\n        \"SE\",\n        \"SG\",\n        \"SH\",\n        \"SI\",\n        \"SJ\",\n        \"SK\",\n        \"SL\",\n        \"SM\",\n        \"SN\",\n        \"SO\",\n        \"SR\",\n        \"SS\",\n        \"ST\",\n        \"SV\",\n        \"SX\",\n        \"SY\",\n        \"SZ\",\n        \"TA\",\n        \"TC\",\n        \"TD\",\n        \"TF\",\n        \"TG\",\n        \"TH\",\n        \"TJ\",\n        \"TK\",\n        \"TL\",\n        \"TM\",\n        \"TN\",\n        \"TO\",\n        \"TR\",\n        \"TT\",\n        \"TV\",\n        \"TW\",\n        \"TZ\",\n        \"UA\",\n        \"UG\",\n        \"UM\",\n        \"UN\",\n        \"US\",\n        \"UY\",\n        \"UZ\",\n        \"VA\",\n        \"VC\",\n        \"VE\",\n        \"VG\",\n        \"VI\",\n        \"VN\",\n        \"VU\",\n        \"WF\",\n        \"WS\",\n        \"XK\",\n        \"YE\",\n        \"YT\",\n        \"ZA\",\n        \"ZM\",\n        \"ZW\"\n    ],\n    \"002\": [\n        \"002\",\n        \"002-status-grouping\",\n        \"011\",\n        \"014\",\n        \"015\",\n        \"017\",\n        \"018\",\n        \"202\",\n        \"AO\",\n        \"BF\",\n        \"BI\",\n        \"BJ\",\n        \"BW\",\n        \"CD\",\n        \"CF\",\n        \"CG\",\n        \"CI\",\n        \"CM\",\n        \"CV\",\n        \"DJ\",\n        \"DZ\",\n        \"EA\",\n        \"EG\",\n        \"EH\",\n        \"ER\",\n        \"ET\",\n        \"GA\",\n        \"GH\",\n        \"GM\",\n        \"GN\",\n        \"GQ\",\n        \"GW\",\n        \"IC\",\n        \"IO\",\n        \"KE\",\n        \"KM\",\n        \"LR\",\n        \"LS\",\n        \"LY\",\n        \"MA\",\n        \"MG\",\n        \"ML\",\n        \"MR\",\n        \"MU\",\n        \"MW\",\n        \"MZ\",\n        \"NA\",\n        \"NE\",\n        \"NG\",\n        \"RE\",\n        \"RW\",\n        \"SC\",\n        \"SD\",\n        \"SH\",\n        \"SL\",\n        \"SN\",\n        \"SO\",\n        \"SS\",\n        \"ST\",\n        \"SZ\",\n        \"TD\",\n        \"TF\",\n        \"TG\",\n        \"TN\",\n        \"TZ\",\n        \"UG\",\n        \"YT\",\n        \"ZA\",\n        \"ZM\",\n        \"ZW\"\n    ],\n    \"003\": [\n        \"003\",\n        \"013\",\n        \"021\",\n        \"029\",\n        \"AG\",\n        \"AI\",\n        \"AW\",\n        \"BB\",\n        \"BL\",\n        \"BM\",\n        \"BQ\",\n        \"BS\",\n        \"BZ\",\n        \"CA\",\n        \"CR\",\n        \"CU\",\n        \"CW\",\n        \"DM\",\n        \"DO\",\n        \"GD\",\n        \"GL\",\n        \"GP\",\n        \"GT\",\n        \"HN\",\n        \"HT\",\n        \"JM\",\n        \"KN\",\n        \"KY\",\n        \"LC\",\n        \"MF\",\n        \"MQ\",\n        \"MS\",\n        \"MX\",\n        \"NI\",\n        \"PA\",\n        \"PM\",\n        \"PR\",\n        \"SV\",\n        \"SX\",\n        \"TC\",\n        \"TT\",\n        \"US\",\n        \"VC\",\n        \"VG\",\n        \"VI\"\n    ],\n    \"005\": [\n        \"005\",\n        \"AR\",\n        \"BO\",\n        \"BR\",\n        \"BV\",\n        \"CL\",\n        \"CO\",\n        \"EC\",\n        \"FK\",\n        \"GF\",\n        \"GS\",\n        \"GY\",\n        \"PE\",\n        \"PY\",\n        \"SR\",\n        \"UY\",\n        \"VE\"\n    ],\n    \"009\": [\n        \"009\",\n        \"053\",\n        \"054\",\n        \"057\",\n        \"061\",\n        \"AC\",\n        \"AQ\",\n        \"AS\",\n        \"AU\",\n        \"CC\",\n        \"CK\",\n        \"CP\",\n        \"CX\",\n        \"DG\",\n        \"FJ\",\n        \"FM\",\n        \"GU\",\n        \"HM\",\n        \"KI\",\n        \"MH\",\n        \"MP\",\n        \"NC\",\n        \"NF\",\n        \"NR\",\n        \"NU\",\n        \"NZ\",\n        \"PF\",\n        \"PG\",\n        \"PN\",\n        \"PW\",\n        \"QO\",\n        \"SB\",\n        \"TA\",\n        \"TK\",\n        \"TO\",\n        \"TV\",\n        \"UM\",\n        \"VU\",\n        \"WF\",\n        \"WS\"\n    ],\n    \"011\": [\n        \"011\",\n        \"BF\",\n        \"BJ\",\n        \"CI\",\n        \"CV\",\n        \"GH\",\n        \"GM\",\n        \"GN\",\n        \"GW\",\n        \"LR\",\n        \"ML\",\n        \"MR\",\n        \"NE\",\n        \"NG\",\n        \"SH\",\n        \"SL\",\n        \"SN\",\n        \"TG\"\n    ],\n    \"013\": [\n        \"013\",\n        \"BZ\",\n        \"CR\",\n        \"GT\",\n        \"HN\",\n        \"MX\",\n        \"NI\",\n        \"PA\",\n        \"SV\"\n    ],\n    \"014\": [\n        \"014\",\n        \"BI\",\n        \"DJ\",\n        \"ER\",\n        \"ET\",\n        \"IO\",\n        \"KE\",\n        \"KM\",\n        \"MG\",\n        \"MU\",\n        \"MW\",\n        \"MZ\",\n        \"RE\",\n        \"RW\",\n        \"SC\",\n        \"SO\",\n        \"SS\",\n        \"TF\",\n        \"TZ\",\n        \"UG\",\n        \"YT\",\n        \"ZM\",\n        \"ZW\"\n    ],\n    \"015\": [\n        \"015\",\n        \"DZ\",\n        \"EA\",\n        \"EG\",\n        \"EH\",\n        \"IC\",\n        \"LY\",\n        \"MA\",\n        \"SD\",\n        \"TN\"\n    ],\n    \"017\": [\n        \"017\",\n        \"AO\",\n        \"CD\",\n        \"CF\",\n        \"CG\",\n        \"CM\",\n        \"GA\",\n        \"GQ\",\n        \"ST\",\n        \"TD\"\n    ],\n    \"018\": [\n        \"018\",\n        \"BW\",\n        \"LS\",\n        \"NA\",\n        \"SZ\",\n        \"ZA\"\n    ],\n    \"019\": [\n        \"003\",\n        \"005\",\n        \"013\",\n        \"019\",\n        \"019-status-grouping\",\n        \"021\",\n        \"029\",\n        \"419\",\n        \"AG\",\n        \"AI\",\n        \"AR\",\n        \"AW\",\n        \"BB\",\n        \"BL\",\n        \"BM\",\n        \"BO\",\n        \"BQ\",\n        \"BR\",\n        \"BS\",\n        \"BV\",\n        \"BZ\",\n        \"CA\",\n        \"CL\",\n        \"CO\",\n        \"CR\",\n        \"CU\",\n        \"CW\",\n        \"DM\",\n        \"DO\",\n        \"EC\",\n        \"FK\",\n        \"GD\",\n        \"GF\",\n        \"GL\",\n        \"GP\",\n        \"GS\",\n        \"GT\",\n        \"GY\",\n        \"HN\",\n        \"HT\",\n        \"JM\",\n        \"KN\",\n        \"KY\",\n        \"LC\",\n        \"MF\",\n        \"MQ\",\n        \"MS\",\n        \"MX\",\n        \"NI\",\n        \"PA\",\n        \"PE\",\n        \"PM\",\n        \"PR\",\n        \"PY\",\n        \"SR\",\n        \"SV\",\n        \"SX\",\n        \"TC\",\n        \"TT\",\n        \"US\",\n        \"UY\",\n        \"VC\",\n        \"VE\",\n        \"VG\",\n        \"VI\"\n    ],\n    \"021\": [\n        \"021\",\n        \"BM\",\n        \"CA\",\n        \"GL\",\n        \"PM\",\n        \"US\"\n    ],\n    \"029\": [\n        \"029\",\n        \"AG\",\n        \"AI\",\n        \"AW\",\n        \"BB\",\n        \"BL\",\n        \"BQ\",\n        \"BS\",\n        \"CU\",\n        \"CW\",\n        \"DM\",\n        \"DO\",\n        \"GD\",\n        \"GP\",\n        \"HT\",\n        \"JM\",\n        \"KN\",\n        \"KY\",\n        \"LC\",\n        \"MF\",\n        \"MQ\",\n        \"MS\",\n        \"PR\",\n        \"SX\",\n        \"TC\",\n        \"TT\",\n        \"VC\",\n        \"VG\",\n        \"VI\"\n    ],\n    \"030\": [\n        \"030\",\n        \"CN\",\n        \"HK\",\n        \"JP\",\n        \"KP\",\n        \"KR\",\n        \"MN\",\n        \"MO\",\n        \"TW\"\n    ],\n    \"034\": [\n        \"034\",\n        \"AF\",\n        \"BD\",\n        \"BT\",\n        \"IN\",\n        \"IR\",\n        \"LK\",\n        \"MV\",\n        \"NP\",\n        \"PK\"\n    ],\n    \"035\": [\n        \"035\",\n        \"BN\",\n        \"ID\",\n        \"KH\",\n        \"LA\",\n        \"MM\",\n        \"MY\",\n        \"PH\",\n        \"SG\",\n        \"TH\",\n        \"TL\",\n        \"VN\"\n    ],\n    \"039\": [\n        \"039\",\n        \"AD\",\n        \"AL\",\n        \"BA\",\n        \"ES\",\n        \"GI\",\n        \"GR\",\n        \"HR\",\n        \"IT\",\n        \"ME\",\n        \"MK\",\n        \"MT\",\n        \"PT\",\n        \"RS\",\n        \"SI\",\n        \"SM\",\n        \"VA\",\n        \"XK\"\n    ],\n    \"053\": [\n        \"053\",\n        \"AU\",\n        \"CC\",\n        \"CX\",\n        \"HM\",\n        \"NF\",\n        \"NZ\"\n    ],\n    \"054\": [\n        \"054\",\n        \"FJ\",\n        \"NC\",\n        \"PG\",\n        \"SB\",\n        \"VU\"\n    ],\n    \"057\": [\n        \"057\",\n        \"FM\",\n        \"GU\",\n        \"KI\",\n        \"MH\",\n        \"MP\",\n        \"NR\",\n        \"PW\",\n        \"UM\"\n    ],\n    \"061\": [\n        \"061\",\n        \"AS\",\n        \"CK\",\n        \"NU\",\n        \"PF\",\n        \"PN\",\n        \"TK\",\n        \"TO\",\n        \"TV\",\n        \"WF\",\n        \"WS\"\n    ],\n    \"142\": [\n        \"030\",\n        \"034\",\n        \"035\",\n        \"142\",\n        \"143\",\n        \"145\",\n        \"AE\",\n        \"AF\",\n        \"AM\",\n        \"AZ\",\n        \"BD\",\n        \"BH\",\n        \"BN\",\n        \"BT\",\n        \"CN\",\n        \"CY\",\n        \"GE\",\n        \"HK\",\n        \"ID\",\n        \"IL\",\n        \"IN\",\n        \"IQ\",\n        \"IR\",\n        \"JO\",\n        \"JP\",\n        \"KG\",\n        \"KH\",\n        \"KP\",\n        \"KR\",\n        \"KW\",\n        \"KZ\",\n        \"LA\",\n        \"LB\",\n        \"LK\",\n        \"MM\",\n        \"MN\",\n        \"MO\",\n        \"MV\",\n        \"MY\",\n        \"NP\",\n        \"OM\",\n        \"PH\",\n        \"PK\",\n        \"PS\",\n        \"QA\",\n        \"SA\",\n        \"SG\",\n        \"SY\",\n        \"TH\",\n        \"TJ\",\n        \"TL\",\n        \"TM\",\n        \"TR\",\n        \"TW\",\n        \"UZ\",\n        \"VN\",\n        \"YE\"\n    ],\n    \"143\": [\n        \"143\",\n        \"KG\",\n        \"KZ\",\n        \"TJ\",\n        \"TM\",\n        \"UZ\"\n    ],\n    \"145\": [\n        \"145\",\n        \"AE\",\n        \"AM\",\n        \"AZ\",\n        \"BH\",\n        \"CY\",\n        \"GE\",\n        \"IL\",\n        \"IQ\",\n        \"JO\",\n        \"KW\",\n        \"LB\",\n        \"OM\",\n        \"PS\",\n        \"QA\",\n        \"SA\",\n        \"SY\",\n        \"TR\",\n        \"YE\"\n    ],\n    \"150\": [\n        \"039\",\n        \"150\",\n        \"151\",\n        \"154\",\n        \"155\",\n        \"AD\",\n        \"AL\",\n        \"AT\",\n        \"AX\",\n        \"BA\",\n        \"BE\",\n        \"BG\",\n        \"BY\",\n        \"CH\",\n        \"CQ\",\n        \"CZ\",\n        \"DE\",\n        \"DK\",\n        \"EE\",\n        \"ES\",\n        \"FI\",\n        \"FO\",\n        \"FR\",\n        \"GB\",\n        \"GG\",\n        \"GI\",\n        \"GR\",\n        \"HR\",\n        \"HU\",\n        \"IE\",\n        \"IM\",\n        \"IS\",\n        \"IT\",\n        \"JE\",\n        \"LI\",\n        \"LT\",\n        \"LU\",\n        \"LV\",\n        \"MC\",\n        \"MD\",\n        \"ME\",\n        \"MK\",\n        \"MT\",\n        \"NL\",\n        \"NO\",\n        \"PL\",\n        \"PT\",\n        \"RO\",\n        \"RS\",\n        \"RU\",\n        \"SE\",\n        \"SI\",\n        \"SJ\",\n        \"SK\",\n        \"SM\",\n        \"UA\",\n        \"VA\",\n        \"XK\"\n    ],\n    \"151\": [\n        \"151\",\n        \"BG\",\n        \"BY\",\n        \"CZ\",\n        \"HU\",\n        \"MD\",\n        \"PL\",\n        \"RO\",\n        \"RU\",\n        \"SK\",\n        \"UA\"\n    ],\n    \"154\": [\n        \"154\",\n        \"AX\",\n        \"CQ\",\n        \"DK\",\n        \"EE\",\n        \"FI\",\n        \"FO\",\n        \"GB\",\n        \"GG\",\n        \"IE\",\n        \"IM\",\n        \"IS\",\n        \"JE\",\n        \"LT\",\n        \"LV\",\n        \"NO\",\n        \"SE\",\n        \"SJ\"\n    ],\n    \"155\": [\n        \"155\",\n        \"AT\",\n        \"BE\",\n        \"CH\",\n        \"DE\",\n        \"FR\",\n        \"LI\",\n        \"LU\",\n        \"MC\",\n        \"NL\"\n    ],\n    \"202\": [\n        \"011\",\n        \"014\",\n        \"017\",\n        \"018\",\n        \"202\",\n        \"AO\",\n        \"BF\",\n        \"BI\",\n        \"BJ\",\n        \"BW\",\n        \"CD\",\n        \"CF\",\n        \"CG\",\n        \"CI\",\n        \"CM\",\n        \"CV\",\n        \"DJ\",\n        \"ER\",\n        \"ET\",\n        \"GA\",\n        \"GH\",\n        \"GM\",\n        \"GN\",\n        \"GQ\",\n        \"GW\",\n        \"IO\",\n        \"KE\",\n        \"KM\",\n        \"LR\",\n        \"LS\",\n        \"MG\",\n        \"ML\",\n        \"MR\",\n        \"MU\",\n        \"MW\",\n        \"MZ\",\n        \"NA\",\n        \"NE\",\n        \"NG\",\n        \"RE\",\n        \"RW\",\n        \"SC\",\n        \"SH\",\n        \"SL\",\n        \"SN\",\n        \"SO\",\n        \"SS\",\n        \"ST\",\n        \"SZ\",\n        \"TD\",\n        \"TF\",\n        \"TG\",\n        \"TZ\",\n        \"UG\",\n        \"YT\",\n        \"ZA\",\n        \"ZM\",\n        \"ZW\"\n    ],\n    \"419\": [\n        \"005\",\n        \"013\",\n        \"029\",\n        \"419\",\n        \"AG\",\n        \"AI\",\n        \"AR\",\n        \"AW\",\n        \"BB\",\n        \"BL\",\n        \"BO\",\n        \"BQ\",\n        \"BR\",\n        \"BS\",\n        \"BV\",\n        \"BZ\",\n        \"CL\",\n        \"CO\",\n        \"CR\",\n        \"CU\",\n        \"CW\",\n        \"DM\",\n        \"DO\",\n        \"EC\",\n        \"FK\",\n        \"GD\",\n        \"GF\",\n        \"GP\",\n        \"GS\",\n        \"GT\",\n        \"GY\",\n        \"HN\",\n        \"HT\",\n        \"JM\",\n        \"KN\",\n        \"KY\",\n        \"LC\",\n        \"MF\",\n        \"MQ\",\n        \"MS\",\n        \"MX\",\n        \"NI\",\n        \"PA\",\n        \"PE\",\n        \"PR\",\n        \"PY\",\n        \"SR\",\n        \"SV\",\n        \"SX\",\n        \"TC\",\n        \"TT\",\n        \"UY\",\n        \"VC\",\n        \"VE\",\n        \"VG\",\n        \"VI\"\n    ],\n    \"EU\": [\n        \"AT\",\n        \"BE\",\n        \"BG\",\n        \"CY\",\n        \"CZ\",\n        \"DE\",\n        \"DK\",\n        \"EE\",\n        \"ES\",\n        \"EU\",\n        \"FI\",\n        \"FR\",\n        \"GR\",\n        \"HR\",\n        \"HU\",\n        \"IE\",\n        \"IT\",\n        \"LT\",\n        \"LU\",\n        \"LV\",\n        \"MT\",\n        \"NL\",\n        \"PL\",\n        \"PT\",\n        \"RO\",\n        \"SE\",\n        \"SI\",\n        \"SK\"\n    ],\n    \"EZ\": [\n        \"AT\",\n        \"BE\",\n        \"CY\",\n        \"DE\",\n        \"EE\",\n        \"ES\",\n        \"EZ\",\n        \"FI\",\n        \"FR\",\n        \"GR\",\n        \"IE\",\n        \"IT\",\n        \"LT\",\n        \"LU\",\n        \"LV\",\n        \"MT\",\n        \"NL\",\n        \"PT\",\n        \"SI\",\n        \"SK\"\n    ],\n    \"QO\": [\n        \"AC\",\n        \"AQ\",\n        \"CP\",\n        \"DG\",\n        \"QO\",\n        \"TA\"\n    ],\n    \"UN\": [\n        \"AD\",\n        \"AE\",\n        \"AF\",\n        \"AG\",\n        \"AL\",\n        \"AM\",\n        \"AO\",\n        \"AR\",\n        \"AT\",\n        \"AU\",\n        \"AZ\",\n        \"BA\",\n        \"BB\",\n        \"BD\",\n        \"BE\",\n        \"BF\",\n        \"BG\",\n        \"BH\",\n        \"BI\",\n        \"BJ\",\n        \"BN\",\n        \"BO\",\n        \"BR\",\n        \"BS\",\n        \"BT\",\n        \"BW\",\n        \"BY\",\n        \"BZ\",\n        \"CA\",\n        \"CD\",\n        \"CF\",\n        \"CG\",\n        \"CH\",\n        \"CI\",\n        \"CL\",\n        \"CM\",\n        \"CN\",\n        \"CO\",\n        \"CR\",\n        \"CU\",\n        \"CV\",\n        \"CY\",\n        \"CZ\",\n        \"DE\",\n        \"DJ\",\n        \"DK\",\n        \"DM\",\n        \"DO\",\n        \"DZ\",\n        \"EC\",\n        \"EE\",\n        \"EG\",\n        \"ER\",\n        \"ES\",\n        \"ET\",\n        \"FI\",\n        \"FJ\",\n        \"FM\",\n        \"FR\",\n        \"GA\",\n        \"GB\",\n        \"GD\",\n        \"GE\",\n        \"GH\",\n        \"GM\",\n        \"GN\",\n        \"GQ\",\n        \"GR\",\n        \"GT\",\n        \"GW\",\n        \"GY\",\n        \"HN\",\n        \"HR\",\n        \"HT\",\n        \"HU\",\n        \"ID\",\n        \"IE\",\n        \"IL\",\n        \"IN\",\n        \"IQ\",\n        \"IR\",\n        \"IS\",\n        \"IT\",\n        \"JM\",\n        \"JO\",\n        \"JP\",\n        \"KE\",\n        \"KG\",\n        \"KH\",\n        \"KI\",\n        \"KM\",\n        \"KN\",\n        \"KP\",\n        \"KR\",\n        \"KW\",\n        \"KZ\",\n        \"LA\",\n        \"LB\",\n        \"LC\",\n        \"LI\",\n        \"LK\",\n        \"LR\",\n        \"LS\",\n        \"LT\",\n        \"LU\",\n        \"LV\",\n        \"LY\",\n        \"MA\",\n        \"MC\",\n        \"MD\",\n        \"ME\",\n        \"MG\",\n        \"MH\",\n        \"MK\",\n        \"ML\",\n        \"MM\",\n        \"MN\",\n        \"MR\",\n        \"MT\",\n        \"MU\",\n        \"MV\",\n        \"MW\",\n        \"MX\",\n        \"MY\",\n        \"MZ\",\n        \"NA\",\n        \"NE\",\n        \"NG\",\n        \"NI\",\n        \"NL\",\n        \"NO\",\n        \"NP\",\n        \"NR\",\n        \"NZ\",\n        \"OM\",\n        \"PA\",\n        \"PE\",\n        \"PG\",\n        \"PH\",\n        \"PK\",\n        \"PL\",\n        \"PT\",\n        \"PW\",\n        \"PY\",\n        \"QA\",\n        \"RO\",\n        \"RS\",\n        \"RU\",\n        \"RW\",\n        \"SA\",\n        \"SB\",\n        \"SC\",\n        \"SD\",\n        \"SE\",\n        \"SG\",\n        \"SI\",\n        \"SK\",\n        \"SL\",\n        \"SM\",\n        \"SN\",\n        \"SO\",\n        \"SR\",\n        \"SS\",\n        \"ST\",\n        \"SV\",\n        \"SY\",\n        \"SZ\",\n        \"TD\",\n        \"TG\",\n        \"TH\",\n        \"TJ\",\n        \"TL\",\n        \"TM\",\n        \"TN\",\n        \"TO\",\n        \"TR\",\n        \"TT\",\n        \"TV\",\n        \"TZ\",\n        \"UA\",\n        \"UG\",\n        \"UN\",\n        \"US\",\n        \"UY\",\n        \"UZ\",\n        \"VC\",\n        \"VE\",\n        \"VN\",\n        \"VU\",\n        \"WS\",\n        \"YE\",\n        \"ZA\",\n        \"ZM\",\n        \"ZW\"\n    ]\n};\n", "import { __spreadArray } from \"tslib\";\nimport { data as jsonData } from './languageMatching';\nimport { regions } from './regions.generated';\nexport var UNICODE_EXTENSION_SEQUENCE_REGEX = /-u(?:-[0-9a-z]{2,8})+/gi;\nexport function invariant(condition, message, Err) {\n    if (Err === void 0) { Err = Error; }\n    if (!condition) {\n        throw new Err(message);\n    }\n}\n// This is effectively 2 languages in 2 different regions in the same cluster\nvar DEFAULT_MATCHING_THRESHOLD = 838;\nvar PROCESSED_DATA;\nfunction processData() {\n    var _a, _b;\n    if (!PROCESSED_DATA) {\n        var paradigmLocales = (_b = (_a = jsonData.supplemental.languageMatching['written-new'][0]) === null || _a === void 0 ? void 0 : _a.paradigmLocales) === null || _b === void 0 ? void 0 : _b._locales.split(' ');\n        var matchVariables = jsonData.supplemental.languageMatching['written-new'].slice(1, 5);\n        var data = jsonData.supplemental.languageMatching['written-new'].slice(5);\n        var matches = data.map(function (d) {\n            var key = Object.keys(d)[0];\n            var value = d[key];\n            return {\n                supported: key,\n                desired: value._desired,\n                distance: +value._distance,\n                oneway: value.oneway === 'true' ? true : false,\n            };\n        }, {});\n        PROCESSED_DATA = {\n            matches: matches,\n            matchVariables: matchVariables.reduce(function (all, d) {\n                var key = Object.keys(d)[0];\n                var value = d[key];\n                all[key.slice(1)] = value._value.split('+');\n                return all;\n            }, {}),\n            paradigmLocales: __spreadArray(__spreadArray([], paradigmLocales, true), paradigmLocales.map(function (l) {\n                return new Intl.Locale(l.replace(/_/g, '-')).maximize().toString();\n            }), true),\n        };\n    }\n    return PROCESSED_DATA;\n}\nfunction isMatched(locale, languageMatchInfoLocale, matchVariables) {\n    var _a = languageMatchInfoLocale.split('-'), language = _a[0], script = _a[1], region = _a[2];\n    var matches = true;\n    if (region && region[0] === '$') {\n        var shouldInclude = region[1] !== '!';\n        var matchRegions = shouldInclude\n            ? matchVariables[region.slice(1)]\n            : matchVariables[region.slice(2)];\n        var expandedMatchedRegions = matchRegions\n            .map(function (r) { return regions[r] || [r]; })\n            .reduce(function (all, list) { return __spreadArray(__spreadArray([], all, true), list, true); }, []);\n        matches && (matches = !(expandedMatchedRegions.indexOf(locale.region || '') > -1 !=\n            shouldInclude));\n    }\n    else {\n        matches && (matches = locale.region\n            ? region === '*' || region === locale.region\n            : true);\n    }\n    matches && (matches = locale.script ? script === '*' || script === locale.script : true);\n    matches && (matches = locale.language\n        ? language === '*' || language === locale.language\n        : true);\n    return matches;\n}\nfunction serializeLSR(lsr) {\n    return [lsr.language, lsr.script, lsr.region].filter(Boolean).join('-');\n}\nfunction findMatchingDistanceForLSR(desired, supported, data) {\n    for (var _i = 0, _a = data.matches; _i < _a.length; _i++) {\n        var d = _a[_i];\n        var matches = isMatched(desired, d.desired, data.matchVariables) &&\n            isMatched(supported, d.supported, data.matchVariables);\n        if (!d.oneway && !matches) {\n            matches =\n                isMatched(desired, d.supported, data.matchVariables) &&\n                    isMatched(supported, d.desired, data.matchVariables);\n        }\n        if (matches) {\n            var distance = d.distance * 10;\n            if (data.paradigmLocales.indexOf(serializeLSR(desired)) > -1 !=\n                data.paradigmLocales.indexOf(serializeLSR(supported)) > -1) {\n                return distance - 1;\n            }\n            return distance;\n        }\n    }\n    throw new Error('No matching distance found');\n}\nexport function findMatchingDistance(desired, supported) {\n    var desiredLocale = new Intl.Locale(desired).maximize();\n    var supportedLocale = new Intl.Locale(supported).maximize();\n    var desiredLSR = {\n        language: desiredLocale.language,\n        script: desiredLocale.script || '',\n        region: desiredLocale.region || '',\n    };\n    var supportedLSR = {\n        language: supportedLocale.language,\n        script: supportedLocale.script || '',\n        region: supportedLocale.region || '',\n    };\n    var matchingDistance = 0;\n    var data = processData();\n    if (desiredLSR.language !== supportedLSR.language) {\n        matchingDistance += findMatchingDistanceForLSR({\n            language: desiredLocale.language,\n            script: '',\n            region: '',\n        }, {\n            language: supportedLocale.language,\n            script: '',\n            region: '',\n        }, data);\n    }\n    if (desiredLSR.script !== supportedLSR.script) {\n        matchingDistance += findMatchingDistanceForLSR({\n            language: desiredLocale.language,\n            script: desiredLSR.script,\n            region: '',\n        }, {\n            language: supportedLocale.language,\n            script: supportedLSR.script,\n            region: '',\n        }, data);\n    }\n    if (desiredLSR.region !== supportedLSR.region) {\n        matchingDistance += findMatchingDistanceForLSR(desiredLSR, supportedLSR, data);\n    }\n    return matchingDistance;\n}\nexport function findBestMatch(requestedLocales, supportedLocales, threshold) {\n    if (threshold === void 0) { threshold = DEFAULT_MATCHING_THRESHOLD; }\n    var lowestDistance = Infinity;\n    var result = {\n        matchedDesiredLocale: '',\n        distances: {},\n    };\n    requestedLocales.forEach(function (desired, i) {\n        if (!result.distances[desired]) {\n            result.distances[desired] = {};\n        }\n        supportedLocales.forEach(function (supported) {\n            // Add some weight to the distance based on the order of the supported locales\n            // Add penalty for the order of the requested locales, which currently is 0 since ECMA-402\n            // doesn't really have room for weighted locales like `en; q=0.1`\n            var distance = findMatchingDistance(desired, supported) + 0 + i * 40;\n            result.distances[desired][supported] = distance;\n            if (distance < lowestDistance) {\n                lowestDistance = distance;\n                result.matchedDesiredLocale = desired;\n                result.matchedSupportedLocale = supported;\n            }\n        });\n    });\n    if (lowestDistance >= threshold) {\n        result.matchedDesiredLocale = undefined;\n        result.matchedSupportedLocale = undefined;\n    }\n    return result;\n}\n", "import { UNICODE_EXTENSION_SEQUENCE_REGEX, findBestMatch } from './utils';\n/**\n * https://tc39.es/ecma402/#sec-bestfitmatcher\n * @param availableLocales\n * @param requestedLocales\n * @param getDefaultLocale\n */\nexport function BestFitMatcher(availableLocales, requestedLocales, getDefaultLocale) {\n    var foundLocale;\n    var extension;\n    var noExtensionLocales = [];\n    var noExtensionLocaleMap = requestedLocales.reduce(function (all, l) {\n        var noExtensionLocale = l.replace(UNICODE_EXTENSION_SEQUENCE_REGEX, '');\n        noExtensionLocales.push(noExtensionLocale);\n        all[noExtensionLocale] = l;\n        return all;\n    }, {});\n    var result = findBestMatch(noExtensionLocales, availableLocales);\n    if (result.matchedSupportedLocale && result.matchedDesiredLocale) {\n        foundLocale = result.matchedSupportedLocale;\n        extension =\n            noExtensionLocaleMap[result.matchedDesiredLocale].slice(result.matchedDesiredLocale.length) || undefined;\n    }\n    if (!foundLocale) {\n        return { locale: getDefaultLocale() };\n    }\n    return {\n        locale: foundLocale,\n        extension: extension,\n    };\n}\n", "import { invariant } from './utils';\nexport function CanonicalizeUValue(ukey, uvalue) {\n    // TODO: Implement algorithm for CanonicalizeUValue per https://tc39.es/ecma402/#sec-canonicalizeuvalue\n    var lowerValue = uvalue.toLowerCase();\n    invariant(ukey !== undefined, \"ukey must be defined\");\n    var canonicalized = lowerValue;\n    return canonicalized;\n}\n", "export function CanonicalizeUnicodeLocaleId(locale) {\n    return Intl.getCanonicalLocales(locale)[0];\n}\n", "import { CanonicalizeUnicodeLocaleId } from './CanonicalizeUnicodeLocaleId';\nimport { invariant } from './utils';\nexport function InsertUnicodeExtensionAndCanonicalize(locale, attributes, keywords) {\n    invariant(locale.indexOf('-u-') === -1, 'Expected locale to not have a Unicode locale extension');\n    var extension = '-u';\n    for (var _i = 0, attributes_1 = attributes; _i < attributes_1.length; _i++) {\n        var attr = attributes_1[_i];\n        extension += \"-\".concat(attr);\n    }\n    for (var _a = 0, keywords_1 = keywords; _a < keywords_1.length; _a++) {\n        var kw = keywords_1[_a];\n        var key = kw.key, value = kw.value;\n        extension += \"-\".concat(key);\n        if (value !== '') {\n            extension += \"-\".concat(value);\n        }\n    }\n    if (extension === '-u') {\n        return CanonicalizeUnicodeLocaleId(locale);\n    }\n    var privateIndex = locale.indexOf('-x-');\n    var newLocale;\n    if (privateIndex === -1) {\n        newLocale = locale + extension;\n    }\n    else {\n        var preExtension = locale.slice(0, privateIndex);\n        var postExtension = locale.slice(privateIndex);\n        newLocale = preExtension + extension + postExtension;\n    }\n    return CanonicalizeUnicodeLocaleId(newLocale);\n}\n", "/**\n * https://tc39.es/ecma402/#sec-bestavailablelocale\n * @param availableLocales\n * @param locale\n */\nexport function BestAvailableLocale(availableLocales, locale) {\n    var candidate = locale;\n    while (true) {\n        if (availableLocales.indexOf(candidate) > -1) {\n            return candidate;\n        }\n        var pos = candidate.lastIndexOf('-');\n        if (!~pos) {\n            return undefined;\n        }\n        if (pos >= 2 && candidate[pos - 2] === '-') {\n            pos -= 2;\n        }\n        candidate = candidate.slice(0, pos);\n    }\n}\n", "import { BestAvailableLocale } from './BestAvailableLocale';\nimport { UNICODE_EXTENSION_SEQUENCE_REGEX } from './utils';\n/**\n * https://tc39.es/ecma402/#sec-lookupmatcher\n * @param availableLocales\n * @param requestedLocales\n * @param getDefaultLocale\n */\nexport function LookupMatcher(availableLocales, requestedLocales, getDefaultLocale) {\n    var result = { locale: '' };\n    for (var _i = 0, requestedLocales_1 = requestedLocales; _i < requestedLocales_1.length; _i++) {\n        var locale = requestedLocales_1[_i];\n        var noExtensionLocale = locale.replace(UNICODE_EXTENSION_SEQUENCE_REGEX, '');\n        var availableLocale = BestAvailableLocale(availableLocales, noExtensionLocale);\n        if (availableLocale) {\n            result.locale = availableLocale;\n            if (locale !== noExtensionLocale) {\n                result.extension = locale.slice(noExtensionLocale.length, locale.length);\n            }\n            return result;\n        }\n    }\n    result.locale = getDefaultLocale();\n    return result;\n}\n", "import { invariant } from './utils';\nexport function UnicodeExtensionComponents(extension) {\n    invariant(extension === extension.toLowerCase(), 'Expected extension to be lowercase');\n    invariant(extension.slice(0, 3) === '-u-', 'Expected extension to be a Unicode locale extension');\n    var attributes = [];\n    var keywords = [];\n    var keyword;\n    var size = extension.length;\n    var k = 3;\n    while (k < size) {\n        var e = extension.indexOf('-', k);\n        var len = void 0;\n        if (e === -1) {\n            len = size - k;\n        }\n        else {\n            len = e - k;\n        }\n        var subtag = extension.slice(k, k + len);\n        invariant(len >= 2, 'Expected a subtag to have at least 2 characters');\n        if (keyword === undefined && len != 2) {\n            if (attributes.indexOf(subtag) === -1) {\n                attributes.push(subtag);\n            }\n        }\n        else if (len === 2) {\n            keyword = { key: subtag, value: '' };\n            if (keywords.find(function (k) { return k.key === (keyword === null || keyword === void 0 ? void 0 : keyword.key); }) === undefined) {\n                keywords.push(keyword);\n            }\n        }\n        else if ((keyword === null || keyword === void 0 ? void 0 : keyword.value) === '') {\n            keyword.value = subtag;\n        }\n        else {\n            invariant(keyword !== undefined, 'Expected keyword to be defined');\n            keyword.value += '-' + subtag;\n        }\n        k += len + 1;\n    }\n    return { attributes: attributes, keywords: keywords };\n}\n", "import { BestFitMatcher } from './BestFitMatcher';\nimport { CanonicalizeUValue } from './CanonicalizeUValue';\nimport { InsertUnicodeExtensionAndCanonicalize } from './InsertUnicodeExtensionAndCanonicalize';\nimport { LookupMatcher } from './LookupMatcher';\nimport { UnicodeExtensionComponents } from './UnicodeExtensionComponents';\nimport { invariant } from './utils';\n/**\n * https://tc39.es/ecma402/#sec-resolvelocale\n */\nexport function ResolveLocale(availableLocales, requestedLocales, options, relevantExtensionKeys, localeData, getDefaultLocale) {\n    var _a;\n    var matcher = options.localeMatcher;\n    var r;\n    if (matcher === 'lookup') {\n        r = LookupMatcher(Array.from(availableLocales), requestedLocales, getDefaultLocale);\n    }\n    else {\n        r = BestFitMatcher(Array.from(availableLocales), requestedLocales, getDefaultLocale);\n    }\n    if (r == null) {\n        r = {\n            locale: getDefaultLocale(),\n            extension: '',\n        };\n    }\n    var foundLocale = r.locale;\n    var foundLocaleData = localeData[foundLocale];\n    // TODO: We can't really guarantee that the locale data is available\n    // invariant(\n    //   foundLocaleData !== undefined,\n    //   `Missing locale data for ${foundLocale}`\n    // )\n    var result = { locale: 'en', dataLocale: foundLocale };\n    var components;\n    var keywords;\n    if (r.extension) {\n        components = UnicodeExtensionComponents(r.extension);\n        keywords = components.keywords;\n    }\n    else {\n        keywords = [];\n    }\n    var supportedKeywords = [];\n    var _loop_1 = function (key) {\n        // TODO: Shouldn't default to empty array, see TODO above\n        var keyLocaleData = (_a = foundLocaleData === null || foundLocaleData === void 0 ? void 0 : foundLocaleData[key]) !== null && _a !== void 0 ? _a : [];\n        invariant(Array.isArray(keyLocaleData), \"keyLocaleData for \".concat(key, \" must be an array\"));\n        var value = keyLocaleData[0];\n        invariant(value === undefined || typeof value === 'string', \"value must be a string or undefined\");\n        var supportedKeyword = void 0;\n        var entry = keywords.find(function (k) { return k.key === key; });\n        if (entry) {\n            var requestedValue = entry.value;\n            if (requestedValue !== '') {\n                if (keyLocaleData.indexOf(requestedValue) > -1) {\n                    value = requestedValue;\n                    supportedKeyword = {\n                        key: key,\n                        value: value,\n                    };\n                }\n            }\n            else if (keyLocaleData.indexOf('true') > -1) {\n                value = 'true';\n                supportedKeyword = {\n                    key: key,\n                    value: value,\n                };\n            }\n        }\n        var optionsValue = options[key];\n        invariant(optionsValue == null || typeof optionsValue === 'string', \"optionsValue must be a string or undefined\");\n        if (typeof optionsValue === 'string') {\n            var ukey = key.toLowerCase();\n            optionsValue = CanonicalizeUValue(ukey, optionsValue);\n            if (optionsValue === '') {\n                optionsValue = 'true';\n            }\n        }\n        if (optionsValue !== value && keyLocaleData.indexOf(optionsValue) > -1) {\n            value = optionsValue;\n            supportedKeyword = undefined;\n        }\n        if (supportedKeyword) {\n            supportedKeywords.push(supportedKeyword);\n        }\n        result[key] = value;\n    };\n    for (var _i = 0, relevantExtensionKeys_1 = relevantExtensionKeys; _i < relevantExtensionKeys_1.length; _i++) {\n        var key = relevantExtensionKeys_1[_i];\n        _loop_1(key);\n    }\n    var supportedAttributes = [];\n    if (supportedKeywords.length > 0) {\n        supportedAttributes = [];\n        foundLocale = InsertUnicodeExtensionAndCanonicalize(foundLocale, supportedAttributes, supportedKeywords);\n    }\n    result.locale = foundLocale;\n    return result;\n}\n", "import { BestAvailableLocale } from './BestAvailableLocale';\nimport { UNICODE_EXTENSION_SEQUENCE_REGEX } from './utils';\n/**\n * https://tc39.es/ecma402/#sec-lookupsupportedlocales\n * @param availableLocales\n * @param requestedLocales\n */\nexport function LookupSupportedLocales(availableLocales, requestedLocales) {\n    var subset = [];\n    for (var _i = 0, requestedLocales_1 = requestedLocales; _i < requestedLocales_1.length; _i++) {\n        var locale = requestedLocales_1[_i];\n        var noExtensionLocale = locale.replace(UNICODE_EXTENSION_SEQUENCE_REGEX, '');\n        var availableLocale = BestAvailableLocale(availableLocales, noExtensionLocale);\n        if (availableLocale) {\n            subset.push(availableLocale);\n        }\n    }\n    return subset;\n}\n", "import { CanonicalizeLocaleList } from './abstract/CanonicalizeLocaleList';\nimport { ResolveLocale } from './abstract/ResolveLocale';\nexport function match(requestedLocales, availableLocales, defaultLocale, opts) {\n    return ResolveLocale(availableLocales, CanonicalizeLocaleList(requestedLocales), {\n        localeMatcher: (opts === null || opts === void 0 ? void 0 : opts.algorithm) || 'best fit',\n    }, [], {}, function () { return defaultLocale; }).locale;\n}\nexport { LookupSupportedLocales } from './abstract/LookupSupportedLocales';\nexport { ResolveLocale } from './abstract/ResolveLocale';\n"], "mappings": ";;;AAIO,SAAS,uBAAuB,SAAS;AAC5C,SAAO,KAAK,oBAAoB,OAAO;AAC3C;;;AC+MO,SAAS,cAAc,IAAI,MAAM,MAAM;AAC5C,MAAI,QAAQ,UAAU,WAAW,EAAG,UAAS,IAAI,GAAG,IAAI,KAAK,QAAQ,IAAI,IAAI,GAAG,KAAK;AACjF,QAAI,MAAM,EAAE,KAAK,OAAO;AACpB,UAAI,CAAC,GAAI,MAAK,MAAM,UAAU,MAAM,KAAK,MAAM,GAAG,CAAC;AACnD,SAAG,CAAC,IAAI,KAAK,CAAC;AAAA,IAClB;AAAA,EACJ;AACA,SAAO,GAAG,OAAO,MAAM,MAAM,UAAU,MAAM,KAAK,IAAI,CAAC;AACzD;;;AC7NO,IAAI,OAAO;AAAA,EACd,cAAc;AAAA,IACV,kBAAkB;AAAA,MACd,eAAe;AAAA,QACX;AAAA,UACI,iBAAiB;AAAA,YACb,UAAU;AAAA,UACd;AAAA,QACJ;AAAA,QACA;AAAA,UACI,OAAO;AAAA,YACH,QAAQ;AAAA,UACZ;AAAA,QACJ;AAAA,QACA;AAAA,UACI,QAAQ;AAAA,YACJ,QAAQ;AAAA,UACZ;AAAA,QACJ;AAAA,QACA;AAAA,UACI,WAAW;AAAA,YACP,QAAQ;AAAA,UACZ;AAAA,QACJ;AAAA,QACA;AAAA,UACI,UAAU;AAAA,YACN,QAAQ;AAAA,UACZ;AAAA,QACJ;AAAA,QACA;AAAA,UACI,IAAI;AAAA,YACA,UAAU;AAAA,YACV,WAAW;AAAA,UACf;AAAA,QACJ;AAAA,QACA;AAAA,UACI,IAAI;AAAA,YACA,UAAU;AAAA,YACV,WAAW;AAAA,UACf;AAAA,QACJ;AAAA,QACA;AAAA,UACI,IAAI;AAAA,YACA,UAAU;AAAA,YACV,WAAW;AAAA,UACf;AAAA,QACJ;AAAA,QACA;AAAA,UACI,IAAI;AAAA,YACA,UAAU;AAAA,YACV,WAAW;AAAA,UACf;AAAA,QACJ;AAAA,QACA;AAAA,UACI,IAAI;AAAA,YACA,UAAU;AAAA,YACV,WAAW;AAAA,UACf;AAAA,QACJ;AAAA,QACA;AAAA,UACI,IAAI;AAAA,YACA,UAAU;AAAA,YACV,WAAW;AAAA,UACf;AAAA,QACJ;AAAA,QACA;AAAA,UACI,IAAI;AAAA,YACA,UAAU;AAAA,YACV,WAAW;AAAA,YACX,SAAS;AAAA,UACb;AAAA,QACJ;AAAA,QACA;AAAA,UACI,IAAI;AAAA,YACA,UAAU;AAAA,YACV,WAAW;AAAA,YACX,SAAS;AAAA,UACb;AAAA,QACJ;AAAA,QACA;AAAA,UACI,IAAI;AAAA,YACA,UAAU;AAAA,YACV,WAAW;AAAA,UACf;AAAA,QACJ;AAAA,QACA;AAAA,UACI,IAAI;AAAA,YACA,UAAU;AAAA,YACV,WAAW;AAAA,UACf;AAAA,QACJ;AAAA,QACA;AAAA,UACI,IAAI;AAAA,YACA,UAAU;AAAA,YACV,WAAW;AAAA,YACX,SAAS;AAAA,UACb;AAAA,QACJ;AAAA,QACA;AAAA,UACI,IAAI;AAAA,YACA,UAAU;AAAA,YACV,WAAW;AAAA,YACX,SAAS;AAAA,UACb;AAAA,QACJ;AAAA,QACA;AAAA,UACI,IAAI;AAAA,YACA,UAAU;AAAA,YACV,WAAW;AAAA,YACX,SAAS;AAAA,UACb;AAAA,QACJ;AAAA,QACA;AAAA,UACI,IAAI;AAAA,YACA,UAAU;AAAA,YACV,WAAW;AAAA,YACX,SAAS;AAAA,UACb;AAAA,QACJ;AAAA,QACA;AAAA,UACI,IAAI;AAAA,YACA,UAAU;AAAA,YACV,WAAW;AAAA,YACX,SAAS;AAAA,UACb;AAAA,QACJ;AAAA,QACA;AAAA,UACI,IAAI;AAAA,YACA,UAAU;AAAA,YACV,WAAW;AAAA,YACX,SAAS;AAAA,UACb;AAAA,QACJ;AAAA,QACA;AAAA,UACI,IAAI;AAAA,YACA,UAAU;AAAA,YACV,WAAW;AAAA,YACX,SAAS;AAAA,UACb;AAAA,QACJ;AAAA,QACA;AAAA,UACI,IAAI;AAAA,YACA,UAAU;AAAA,YACV,WAAW;AAAA,YACX,SAAS;AAAA,UACb;AAAA,QACJ;AAAA,QACA;AAAA,UACI,IAAI;AAAA,YACA,UAAU;AAAA,YACV,WAAW;AAAA,YACX,SAAS;AAAA,UACb;AAAA,QACJ;AAAA,QACA;AAAA,UACI,IAAI;AAAA,YACA,UAAU;AAAA,YACV,WAAW;AAAA,YACX,SAAS;AAAA,UACb;AAAA,QACJ;AAAA,QACA;AAAA,UACI,IAAI;AAAA,YACA,UAAU;AAAA,YACV,WAAW;AAAA,YACX,SAAS;AAAA,UACb;AAAA,QACJ;AAAA,QACA;AAAA,UACI,IAAI;AAAA,YACA,UAAU;AAAA,YACV,WAAW;AAAA,YACX,SAAS;AAAA,UACb;AAAA,QACJ;AAAA,QACA;AAAA,UACI,IAAI;AAAA,YACA,UAAU;AAAA,YACV,WAAW;AAAA,YACX,SAAS;AAAA,UACb;AAAA,QACJ;AAAA,QACA;AAAA,UACI,IAAI;AAAA,YACA,UAAU;AAAA,YACV,WAAW;AAAA,YACX,SAAS;AAAA,UACb;AAAA,QACJ;AAAA,QACA;AAAA,UACI,IAAI;AAAA,YACA,UAAU;AAAA,YACV,WAAW;AAAA,YACX,SAAS;AAAA,UACb;AAAA,QACJ;AAAA,QACA;AAAA,UACI,KAAK;AAAA,YACD,UAAU;AAAA,YACV,WAAW;AAAA,YACX,SAAS;AAAA,UACb;AAAA,QACJ;AAAA,QACA;AAAA,UACI,IAAI;AAAA,YACA,UAAU;AAAA,YACV,WAAW;AAAA,YACX,SAAS;AAAA,UACb;AAAA,QACJ;AAAA,QACA;AAAA,UACI,IAAI;AAAA,YACA,UAAU;AAAA,YACV,WAAW;AAAA,YACX,SAAS;AAAA,UACb;AAAA,QACJ;AAAA,QACA;AAAA,UACI,IAAI;AAAA,YACA,UAAU;AAAA,YACV,WAAW;AAAA,YACX,SAAS;AAAA,UACb;AAAA,QACJ;AAAA,QACA;AAAA,UACI,IAAI;AAAA,YACA,UAAU;AAAA,YACV,WAAW;AAAA,YACX,SAAS;AAAA,UACb;AAAA,QACJ;AAAA,QACA;AAAA,UACI,IAAI;AAAA,YACA,UAAU;AAAA,YACV,WAAW;AAAA,UACf;AAAA,QACJ;AAAA,QACA;AAAA,UACI,IAAI;AAAA,YACA,UAAU;AAAA,YACV,WAAW;AAAA,YACX,SAAS;AAAA,UACb;AAAA,QACJ;AAAA,QACA;AAAA,UACI,IAAI;AAAA,YACA,UAAU;AAAA,YACV,WAAW;AAAA,YACX,SAAS;AAAA,UACb;AAAA,QACJ;AAAA,QACA;AAAA,UACI,IAAI;AAAA,YACA,UAAU;AAAA,YACV,WAAW;AAAA,YACX,SAAS;AAAA,UACb;AAAA,QACJ;AAAA,QACA;AAAA,UACI,IAAI;AAAA,YACA,UAAU;AAAA,YACV,WAAW;AAAA,YACX,SAAS;AAAA,UACb;AAAA,QACJ;AAAA,QACA;AAAA,UACI,IAAI;AAAA,YACA,UAAU;AAAA,YACV,WAAW;AAAA,YACX,SAAS;AAAA,UACb;AAAA,QACJ;AAAA,QACA;AAAA,UACI,IAAI;AAAA,YACA,UAAU;AAAA,YACV,WAAW;AAAA,YACX,SAAS;AAAA,UACb;AAAA,QACJ;AAAA,QACA;AAAA,UACI,IAAI;AAAA,YACA,UAAU;AAAA,YACV,WAAW;AAAA,YACX,SAAS;AAAA,UACb;AAAA,QACJ;AAAA,QACA;AAAA,UACI,IAAI;AAAA,YACA,UAAU;AAAA,YACV,WAAW;AAAA,YACX,SAAS;AAAA,UACb;AAAA,QACJ;AAAA,QACA;AAAA,UACI,IAAI;AAAA,YACA,UAAU;AAAA,YACV,WAAW;AAAA,YACX,SAAS;AAAA,UACb;AAAA,QACJ;AAAA,QACA;AAAA,UACI,IAAI;AAAA,YACA,UAAU;AAAA,YACV,WAAW;AAAA,YACX,SAAS;AAAA,UACb;AAAA,QACJ;AAAA,QACA;AAAA,UACI,IAAI;AAAA,YACA,UAAU;AAAA,YACV,WAAW;AAAA,YACX,SAAS;AAAA,UACb;AAAA,QACJ;AAAA,QACA;AAAA,UACI,IAAI;AAAA,YACA,UAAU;AAAA,YACV,WAAW;AAAA,YACX,SAAS;AAAA,UACb;AAAA,QACJ;AAAA,QACA;AAAA,UACI,IAAI;AAAA,YACA,UAAU;AAAA,YACV,WAAW;AAAA,YACX,SAAS;AAAA,UACb;AAAA,QACJ;AAAA,QACA;AAAA,UACI,IAAI;AAAA,YACA,UAAU;AAAA,YACV,WAAW;AAAA,YACX,SAAS;AAAA,UACb;AAAA,QACJ;AAAA,QACA;AAAA,UACI,IAAI;AAAA,YACA,UAAU;AAAA,YACV,WAAW;AAAA,YACX,SAAS;AAAA,UACb;AAAA,QACJ;AAAA,QACA;AAAA,UACI,IAAI;AAAA,YACA,UAAU;AAAA,YACV,WAAW;AAAA,YACX,SAAS;AAAA,UACb;AAAA,QACJ;AAAA,QACA;AAAA,UACI,IAAI;AAAA,YACA,UAAU;AAAA,YACV,WAAW;AAAA,YACX,SAAS;AAAA,UACb;AAAA,QACJ;AAAA,QACA;AAAA,UACI,IAAI;AAAA,YACA,UAAU;AAAA,YACV,WAAW;AAAA,YACX,SAAS;AAAA,UACb;AAAA,QACJ;AAAA,QACA;AAAA,UACI,IAAI;AAAA,YACA,UAAU;AAAA,YACV,WAAW;AAAA,YACX,SAAS;AAAA,UACb;AAAA,QACJ;AAAA,QACA;AAAA,UACI,IAAI;AAAA,YACA,UAAU;AAAA,YACV,WAAW;AAAA,YACX,SAAS;AAAA,UACb;AAAA,QACJ;AAAA,QACA;AAAA,UACI,IAAI;AAAA,YACA,UAAU;AAAA,YACV,WAAW;AAAA,YACX,SAAS;AAAA,UACb;AAAA,QACJ;AAAA,QACA;AAAA,UACI,IAAI;AAAA,YACA,UAAU;AAAA,YACV,WAAW;AAAA,YACX,SAAS;AAAA,UACb;AAAA,QACJ;AAAA,QACA;AAAA,UACI,IAAI;AAAA,YACA,UAAU;AAAA,YACV,WAAW;AAAA,YACX,SAAS;AAAA,UACb;AAAA,QACJ;AAAA,QACA;AAAA,UACI,IAAI;AAAA,YACA,UAAU;AAAA,YACV,WAAW;AAAA,YACX,SAAS;AAAA,UACb;AAAA,QACJ;AAAA,QACA;AAAA,UACI,IAAI;AAAA,YACA,UAAU;AAAA,YACV,WAAW;AAAA,YACX,SAAS;AAAA,UACb;AAAA,QACJ;AAAA,QACA;AAAA,UACI,IAAI;AAAA,YACA,UAAU;AAAA,YACV,WAAW;AAAA,YACX,SAAS;AAAA,UACb;AAAA,QACJ;AAAA,QACA;AAAA,UACI,IAAI;AAAA,YACA,UAAU;AAAA,YACV,WAAW;AAAA,YACX,SAAS;AAAA,UACb;AAAA,QACJ;AAAA,QACA;AAAA,UACI,IAAI;AAAA,YACA,UAAU;AAAA,YACV,WAAW;AAAA,YACX,SAAS;AAAA,UACb;AAAA,QACJ;AAAA,QACA;AAAA,UACI,IAAI;AAAA,YACA,UAAU;AAAA,YACV,WAAW;AAAA,YACX,SAAS;AAAA,UACb;AAAA,QACJ;AAAA,QACA;AAAA,UACI,IAAI;AAAA,YACA,UAAU;AAAA,YACV,WAAW;AAAA,YACX,SAAS;AAAA,UACb;AAAA,QACJ;AAAA,QACA;AAAA,UACI,IAAI;AAAA,YACA,UAAU;AAAA,YACV,WAAW;AAAA,YACX,SAAS;AAAA,UACb;AAAA,QACJ;AAAA,QACA;AAAA,UACI,IAAI;AAAA,YACA,UAAU;AAAA,YACV,WAAW;AAAA,YACX,SAAS;AAAA,UACb;AAAA,QACJ;AAAA,QACA;AAAA,UACI,IAAI;AAAA,YACA,UAAU;AAAA,YACV,WAAW;AAAA,YACX,SAAS;AAAA,UACb;AAAA,QACJ;AAAA,QACA;AAAA,UACI,IAAI;AAAA,YACA,UAAU;AAAA,YACV,WAAW;AAAA,YACX,SAAS;AAAA,UACb;AAAA,QACJ;AAAA,QACA;AAAA,UACI,IAAI;AAAA,YACA,UAAU;AAAA,YACV,WAAW;AAAA,YACX,SAAS;AAAA,UACb;AAAA,QACJ;AAAA,QACA;AAAA,UACI,IAAI;AAAA,YACA,UAAU;AAAA,YACV,WAAW;AAAA,YACX,SAAS;AAAA,UACb;AAAA,QACJ;AAAA,QACA;AAAA,UACI,IAAI;AAAA,YACA,UAAU;AAAA,YACV,WAAW;AAAA,YACX,SAAS;AAAA,UACb;AAAA,QACJ;AAAA,QACA;AAAA,UACI,IAAI;AAAA,YACA,UAAU;AAAA,YACV,WAAW;AAAA,YACX,SAAS;AAAA,UACb;AAAA,QACJ;AAAA,QACA;AAAA,UACI,IAAI;AAAA,YACA,UAAU;AAAA,YACV,WAAW;AAAA,YACX,SAAS;AAAA,UACb;AAAA,QACJ;AAAA,QACA;AAAA,UACI,IAAI;AAAA,YACA,UAAU;AAAA,YACV,WAAW;AAAA,YACX,SAAS;AAAA,UACb;AAAA,QACJ;AAAA,QACA;AAAA,UACI,IAAI;AAAA,YACA,UAAU;AAAA,YACV,WAAW;AAAA,YACX,SAAS;AAAA,UACb;AAAA,QACJ;AAAA,QACA;AAAA,UACI,IAAI;AAAA,YACA,UAAU;AAAA,YACV,WAAW;AAAA,YACX,SAAS;AAAA,UACb;AAAA,QACJ;AAAA,QACA;AAAA,UACI,IAAI;AAAA,YACA,UAAU;AAAA,YACV,WAAW;AAAA,YACX,SAAS;AAAA,UACb;AAAA,QACJ;AAAA,QACA;AAAA,UACI,IAAI;AAAA,YACA,UAAU;AAAA,YACV,WAAW;AAAA,YACX,SAAS;AAAA,UACb;AAAA,QACJ;AAAA,QACA;AAAA,UACI,IAAI;AAAA,YACA,UAAU;AAAA,YACV,WAAW;AAAA,YACX,SAAS;AAAA,UACb;AAAA,QACJ;AAAA,QACA;AAAA,UACI,IAAI;AAAA,YACA,UAAU;AAAA,YACV,WAAW;AAAA,UACf;AAAA,QACJ;AAAA,QACA;AAAA,UACI,IAAI;AAAA,YACA,UAAU;AAAA,YACV,WAAW;AAAA,UACf;AAAA,QACJ;AAAA,QACA;AAAA,UACI,IAAI;AAAA,YACA,UAAU;AAAA,YACV,WAAW;AAAA,YACX,SAAS;AAAA,UACb;AAAA,QACJ;AAAA,QACA;AAAA,UACI,IAAI;AAAA,YACA,UAAU;AAAA,YACV,WAAW;AAAA,YACX,SAAS;AAAA,UACb;AAAA,QACJ;AAAA,QACA;AAAA,UACI,IAAI;AAAA,YACA,UAAU;AAAA,YACV,WAAW;AAAA,YACX,SAAS;AAAA,UACb;AAAA,QACJ;AAAA,QACA;AAAA,UACI,IAAI;AAAA,YACA,UAAU;AAAA,YACV,WAAW;AAAA,YACX,SAAS;AAAA,UACb;AAAA,QACJ;AAAA,QACA;AAAA,UACI,IAAI;AAAA,YACA,UAAU;AAAA,YACV,WAAW;AAAA,YACX,SAAS;AAAA,UACb;AAAA,QACJ;AAAA,QACA;AAAA,UACI,IAAI;AAAA,YACA,UAAU;AAAA,YACV,WAAW;AAAA,YACX,SAAS;AAAA,UACb;AAAA,QACJ;AAAA,QACA;AAAA,UACI,IAAI;AAAA,YACA,UAAU;AAAA,YACV,WAAW;AAAA,YACX,SAAS;AAAA,UACb;AAAA,QACJ;AAAA,QACA;AAAA,UACI,IAAI;AAAA,YACA,UAAU;AAAA,YACV,WAAW;AAAA,YACX,SAAS;AAAA,UACb;AAAA,QACJ;AAAA,QACA;AAAA,UACI,IAAI;AAAA,YACA,UAAU;AAAA,YACV,WAAW;AAAA,YACX,SAAS;AAAA,UACb;AAAA,QACJ;AAAA,QACA;AAAA,UACI,IAAI;AAAA,YACA,UAAU;AAAA,YACV,WAAW;AAAA,YACX,SAAS;AAAA,UACb;AAAA,QACJ;AAAA,QACA;AAAA,UACI,IAAI;AAAA,YACA,UAAU;AAAA,YACV,WAAW;AAAA,YACX,SAAS;AAAA,UACb;AAAA,QACJ;AAAA,QACA;AAAA,UACI,IAAI;AAAA,YACA,UAAU;AAAA,YACV,WAAW;AAAA,YACX,SAAS;AAAA,UACb;AAAA,QACJ;AAAA,QACA;AAAA,UACI,IAAI;AAAA,YACA,UAAU;AAAA,YACV,WAAW;AAAA,YACX,SAAS;AAAA,UACb;AAAA,QACJ;AAAA,QACA;AAAA,UACI,IAAI;AAAA,YACA,UAAU;AAAA,YACV,WAAW;AAAA,YACX,SAAS;AAAA,UACb;AAAA,QACJ;AAAA,QACA;AAAA,UACI,IAAI;AAAA,YACA,UAAU;AAAA,YACV,WAAW;AAAA,YACX,SAAS;AAAA,UACb;AAAA,QACJ;AAAA,QACA;AAAA,UACI,IAAI;AAAA,YACA,UAAU;AAAA,YACV,WAAW;AAAA,YACX,SAAS;AAAA,UACb;AAAA,QACJ;AAAA,QACA;AAAA,UACI,IAAI;AAAA,YACA,UAAU;AAAA,YACV,WAAW;AAAA,YACX,SAAS;AAAA,UACb;AAAA,QACJ;AAAA,QACA;AAAA,UACI,IAAI;AAAA,YACA,UAAU;AAAA,YACV,WAAW;AAAA,YACX,SAAS;AAAA,UACb;AAAA,QACJ;AAAA,QACA;AAAA,UACI,IAAI;AAAA,YACA,UAAU;AAAA,YACV,WAAW;AAAA,YACX,SAAS;AAAA,UACb;AAAA,QACJ;AAAA,QACA;AAAA,UACI,IAAI;AAAA,YACA,UAAU;AAAA,YACV,WAAW;AAAA,YACX,SAAS;AAAA,UACb;AAAA,QACJ;AAAA,QACA;AAAA,UACI,IAAI;AAAA,YACA,UAAU;AAAA,YACV,WAAW;AAAA,YACX,SAAS;AAAA,UACb;AAAA,QACJ;AAAA,QACA;AAAA,UACI,IAAI;AAAA,YACA,UAAU;AAAA,YACV,WAAW;AAAA,YACX,SAAS;AAAA,UACb;AAAA,QACJ;AAAA,QACA;AAAA,UACI,IAAI;AAAA,YACA,UAAU;AAAA,YACV,WAAW;AAAA,YACX,SAAS;AAAA,UACb;AAAA,QACJ;AAAA,QACA;AAAA,UACI,IAAI;AAAA,YACA,UAAU;AAAA,YACV,WAAW;AAAA,YACX,SAAS;AAAA,UACb;AAAA,QACJ;AAAA,QACA;AAAA,UACI,IAAI;AAAA,YACA,UAAU;AAAA,YACV,WAAW;AAAA,YACX,SAAS;AAAA,UACb;AAAA,QACJ;AAAA,QACA;AAAA,UACI,IAAI;AAAA,YACA,UAAU;AAAA,YACV,WAAW;AAAA,YACX,SAAS;AAAA,UACb;AAAA,QACJ;AAAA,QACA;AAAA,UACI,IAAI;AAAA,YACA,UAAU;AAAA,YACV,WAAW;AAAA,YACX,SAAS;AAAA,UACb;AAAA,QACJ;AAAA,QACA;AAAA,UACI,IAAI;AAAA,YACA,UAAU;AAAA,YACV,WAAW;AAAA,YACX,SAAS;AAAA,UACb;AAAA,QACJ;AAAA,QACA;AAAA,UACI,IAAI;AAAA,YACA,UAAU;AAAA,YACV,WAAW;AAAA,YACX,SAAS;AAAA,UACb;AAAA,QACJ;AAAA,QACA;AAAA,UACI,IAAI;AAAA,YACA,UAAU;AAAA,YACV,WAAW;AAAA,YACX,SAAS;AAAA,UACb;AAAA,QACJ;AAAA,QACA;AAAA,UACI,IAAI;AAAA,YACA,UAAU;AAAA,YACV,WAAW;AAAA,YACX,SAAS;AAAA,UACb;AAAA,QACJ;AAAA,QACA;AAAA,UACI,IAAI;AAAA,YACA,UAAU;AAAA,YACV,WAAW;AAAA,YACX,SAAS;AAAA,UACb;AAAA,QACJ;AAAA,QACA;AAAA,UACI,IAAI;AAAA,YACA,UAAU;AAAA,YACV,WAAW;AAAA,YACX,SAAS;AAAA,UACb;AAAA,QACJ;AAAA,QACA;AAAA,UACI,IAAI;AAAA,YACA,UAAU;AAAA,YACV,WAAW;AAAA,YACX,SAAS;AAAA,UACb;AAAA,QACJ;AAAA,QACA;AAAA,UACI,IAAI;AAAA,YACA,UAAU;AAAA,YACV,WAAW;AAAA,YACX,SAAS;AAAA,UACb;AAAA,QACJ;AAAA,QACA;AAAA,UACI,IAAI;AAAA,YACA,UAAU;AAAA,YACV,WAAW;AAAA,YACX,SAAS;AAAA,UACb;AAAA,QACJ;AAAA,QACA;AAAA,UACI,IAAI;AAAA,YACA,UAAU;AAAA,YACV,WAAW;AAAA,YACX,SAAS;AAAA,UACb;AAAA,QACJ;AAAA,QACA;AAAA,UACI,IAAI;AAAA,YACA,UAAU;AAAA,YACV,WAAW;AAAA,YACX,SAAS;AAAA,UACb;AAAA,QACJ;AAAA,QACA;AAAA,UACI,IAAI;AAAA,YACA,UAAU;AAAA,YACV,WAAW;AAAA,YACX,SAAS;AAAA,UACb;AAAA,QACJ;AAAA,QACA;AAAA,UACI,IAAI;AAAA,YACA,UAAU;AAAA,YACV,WAAW;AAAA,YACX,SAAS;AAAA,UACb;AAAA,QACJ;AAAA,QACA;AAAA,UACI,IAAI;AAAA,YACA,UAAU;AAAA,YACV,WAAW;AAAA,YACX,SAAS;AAAA,UACb;AAAA,QACJ;AAAA,QACA;AAAA,UACI,IAAI;AAAA,YACA,UAAU;AAAA,YACV,WAAW;AAAA,YACX,SAAS;AAAA,UACb;AAAA,QACJ;AAAA,QACA;AAAA,UACI,IAAI;AAAA,YACA,UAAU;AAAA,YACV,WAAW;AAAA,YACX,SAAS;AAAA,UACb;AAAA,QACJ;AAAA,QACA;AAAA,UACI,IAAI;AAAA,YACA,UAAU;AAAA,YACV,WAAW;AAAA,YACX,SAAS;AAAA,UACb;AAAA,QACJ;AAAA,QACA;AAAA,UACI,IAAI;AAAA,YACA,UAAU;AAAA,YACV,WAAW;AAAA,YACX,SAAS;AAAA,UACb;AAAA,QACJ;AAAA,QACA;AAAA,UACI,IAAI;AAAA,YACA,UAAU;AAAA,YACV,WAAW;AAAA,YACX,SAAS;AAAA,UACb;AAAA,QACJ;AAAA,QACA;AAAA,UACI,IAAI;AAAA,YACA,UAAU;AAAA,YACV,WAAW;AAAA,YACX,SAAS;AAAA,UACb;AAAA,QACJ;AAAA,QACA;AAAA,UACI,IAAI;AAAA,YACA,UAAU;AAAA,YACV,WAAW;AAAA,YACX,SAAS;AAAA,UACb;AAAA,QACJ;AAAA,QACA;AAAA,UACI,IAAI;AAAA,YACA,UAAU;AAAA,YACV,WAAW;AAAA,YACX,SAAS;AAAA,UACb;AAAA,QACJ;AAAA,QACA;AAAA,UACI,IAAI;AAAA,YACA,UAAU;AAAA,YACV,WAAW;AAAA,YACX,SAAS;AAAA,UACb;AAAA,QACJ;AAAA,QACA;AAAA,UACI,IAAI;AAAA,YACA,UAAU;AAAA,YACV,WAAW;AAAA,YACX,SAAS;AAAA,UACb;AAAA,QACJ;AAAA,QACA;AAAA,UACI,IAAI;AAAA,YACA,UAAU;AAAA,YACV,WAAW;AAAA,YACX,SAAS;AAAA,UACb;AAAA,QACJ;AAAA,QACA;AAAA,UACI,IAAI;AAAA,YACA,UAAU;AAAA,YACV,WAAW;AAAA,YACX,SAAS;AAAA,UACb;AAAA,QACJ;AAAA,QACA;AAAA,UACI,IAAI;AAAA,YACA,UAAU;AAAA,YACV,WAAW;AAAA,YACX,SAAS;AAAA,UACb;AAAA,QACJ;AAAA,QACA;AAAA,UACI,IAAI;AAAA,YACA,UAAU;AAAA,YACV,WAAW;AAAA,YACX,SAAS;AAAA,UACb;AAAA,QACJ;AAAA,QACA;AAAA,UACI,IAAI;AAAA,YACA,UAAU;AAAA,YACV,WAAW;AAAA,YACX,SAAS;AAAA,UACb;AAAA,QACJ;AAAA,QACA;AAAA,UACI,IAAI;AAAA,YACA,UAAU;AAAA,YACV,WAAW;AAAA,YACX,SAAS;AAAA,UACb;AAAA,QACJ;AAAA,QACA;AAAA,UACI,IAAI;AAAA,YACA,UAAU;AAAA,YACV,WAAW;AAAA,YACX,SAAS;AAAA,UACb;AAAA,QACJ;AAAA,QACA;AAAA,UACI,IAAI;AAAA,YACA,UAAU;AAAA,YACV,WAAW;AAAA,YACX,SAAS;AAAA,UACb;AAAA,QACJ;AAAA,QACA;AAAA,UACI,IAAI;AAAA,YACA,UAAU;AAAA,YACV,WAAW;AAAA,YACX,SAAS;AAAA,UACb;AAAA,QACJ;AAAA,QACA;AAAA,UACI,IAAI;AAAA,YACA,UAAU;AAAA,YACV,WAAW;AAAA,YACX,SAAS;AAAA,UACb;AAAA,QACJ;AAAA,QACA;AAAA,UACI,IAAI;AAAA,YACA,UAAU;AAAA,YACV,WAAW;AAAA,YACX,SAAS;AAAA,UACb;AAAA,QACJ;AAAA,QACA;AAAA,UACI,IAAI;AAAA,YACA,UAAU;AAAA,YACV,WAAW;AAAA,YACX,SAAS;AAAA,UACb;AAAA,QACJ;AAAA,QACA;AAAA,UACI,IAAI;AAAA,YACA,UAAU;AAAA,YACV,WAAW;AAAA,YACX,SAAS;AAAA,UACb;AAAA,QACJ;AAAA,QACA;AAAA,UACI,IAAI;AAAA,YACA,UAAU;AAAA,YACV,WAAW;AAAA,YACX,SAAS;AAAA,UACb;AAAA,QACJ;AAAA,QACA;AAAA,UACI,IAAI;AAAA,YACA,UAAU;AAAA,YACV,WAAW;AAAA,YACX,SAAS;AAAA,UACb;AAAA,QACJ;AAAA,QACA;AAAA,UACI,IAAI;AAAA,YACA,UAAU;AAAA,YACV,WAAW;AAAA,YACX,SAAS;AAAA,UACb;AAAA,QACJ;AAAA,QACA;AAAA,UACI,IAAI;AAAA,YACA,UAAU;AAAA,YACV,WAAW;AAAA,YACX,SAAS;AAAA,UACb;AAAA,QACJ;AAAA,QACA;AAAA,UACI,IAAI;AAAA,YACA,UAAU;AAAA,YACV,WAAW;AAAA,YACX,SAAS;AAAA,UACb;AAAA,QACJ;AAAA,QACA;AAAA,UACI,IAAI;AAAA,YACA,UAAU;AAAA,YACV,WAAW;AAAA,YACX,SAAS;AAAA,UACb;AAAA,QACJ;AAAA,QACA;AAAA,UACI,IAAI;AAAA,YACA,UAAU;AAAA,YACV,WAAW;AAAA,YACX,SAAS;AAAA,UACb;AAAA,QACJ;AAAA,QACA;AAAA,UACI,IAAI;AAAA,YACA,UAAU;AAAA,YACV,WAAW;AAAA,YACX,SAAS;AAAA,UACb;AAAA,QACJ;AAAA,QACA;AAAA,UACI,IAAI;AAAA,YACA,UAAU;AAAA,YACV,WAAW;AAAA,YACX,SAAS;AAAA,UACb;AAAA,QACJ;AAAA,QACA;AAAA,UACI,IAAI;AAAA,YACA,UAAU;AAAA,YACV,WAAW;AAAA,YACX,SAAS;AAAA,UACb;AAAA,QACJ;AAAA,QACA;AAAA,UACI,IAAI;AAAA,YACA,UAAU;AAAA,YACV,WAAW;AAAA,YACX,SAAS;AAAA,UACb;AAAA,QACJ;AAAA,QACA;AAAA,UACI,IAAI;AAAA,YACA,UAAU;AAAA,YACV,WAAW;AAAA,YACX,SAAS;AAAA,UACb;AAAA,QACJ;AAAA,QACA;AAAA,UACI,IAAI;AAAA,YACA,UAAU;AAAA,YACV,WAAW;AAAA,YACX,SAAS;AAAA,UACb;AAAA,QACJ;AAAA,QACA;AAAA,UACI,IAAI;AAAA,YACA,UAAU;AAAA,YACV,WAAW;AAAA,YACX,SAAS;AAAA,UACb;AAAA,QACJ;AAAA,QACA;AAAA,UACI,IAAI;AAAA,YACA,UAAU;AAAA,YACV,WAAW;AAAA,YACX,SAAS;AAAA,UACb;AAAA,QACJ;AAAA,QACA;AAAA,UACI,IAAI;AAAA,YACA,UAAU;AAAA,YACV,WAAW;AAAA,YACX,SAAS;AAAA,UACb;AAAA,QACJ;AAAA,QACA;AAAA,UACI,IAAI;AAAA,YACA,UAAU;AAAA,YACV,WAAW;AAAA,YACX,SAAS;AAAA,UACb;AAAA,QACJ;AAAA,QACA;AAAA,UACI,IAAI;AAAA,YACA,UAAU;AAAA,YACV,WAAW;AAAA,YACX,SAAS;AAAA,UACb;AAAA,QACJ;AAAA,QACA;AAAA,UACI,IAAI;AAAA,YACA,UAAU;AAAA,YACV,WAAW;AAAA,YACX,SAAS;AAAA,UACb;AAAA,QACJ;AAAA,QACA;AAAA,UACI,IAAI;AAAA,YACA,UAAU;AAAA,YACV,WAAW;AAAA,YACX,SAAS;AAAA,UACb;AAAA,QACJ;AAAA,QACA;AAAA,UACI,IAAI;AAAA,YACA,UAAU;AAAA,YACV,WAAW;AAAA,YACX,SAAS;AAAA,UACb;AAAA,QACJ;AAAA,QACA;AAAA,UACI,IAAI;AAAA,YACA,UAAU;AAAA,YACV,WAAW;AAAA,YACX,SAAS;AAAA,UACb;AAAA,QACJ;AAAA,QACA;AAAA,UACI,KAAK;AAAA,YACD,UAAU;AAAA,YACV,WAAW;AAAA,YACX,SAAS;AAAA,UACb;AAAA,QACJ;AAAA,QACA;AAAA,UACI,KAAK;AAAA,YACD,UAAU;AAAA,YACV,WAAW;AAAA,YACX,SAAS;AAAA,UACb;AAAA,QACJ;AAAA,QACA;AAAA,UACI,KAAK;AAAA,YACD,UAAU;AAAA,YACV,WAAW;AAAA,YACX,SAAS;AAAA,UACb;AAAA,QACJ;AAAA,QACA;AAAA,UACI,KAAK;AAAA,YACD,UAAU;AAAA,YACV,WAAW;AAAA,YACX,SAAS;AAAA,UACb;AAAA,QACJ;AAAA,QACA;AAAA,UACI,KAAK;AAAA,YACD,UAAU;AAAA,YACV,WAAW;AAAA,YACX,SAAS;AAAA,UACb;AAAA,QACJ;AAAA,QACA;AAAA,UACI,KAAK;AAAA,YACD,UAAU;AAAA,YACV,WAAW;AAAA,YACX,SAAS;AAAA,UACb;AAAA,QACJ;AAAA,QACA;AAAA,UACI,KAAK;AAAA,YACD,UAAU;AAAA,YACV,WAAW;AAAA,YACX,SAAS;AAAA,UACb;AAAA,QACJ;AAAA,QACA;AAAA,UACI,KAAK;AAAA,YACD,UAAU;AAAA,YACV,WAAW;AAAA,YACX,SAAS;AAAA,UACb;AAAA,QACJ;AAAA,QACA;AAAA,UACI,KAAK;AAAA,YACD,UAAU;AAAA,YACV,WAAW;AAAA,YACX,SAAS;AAAA,UACb;AAAA,QACJ;AAAA,QACA;AAAA,UACI,KAAK;AAAA,YACD,UAAU;AAAA,YACV,WAAW;AAAA,YACX,SAAS;AAAA,UACb;AAAA,QACJ;AAAA,QACA;AAAA,UACI,KAAK;AAAA,YACD,UAAU;AAAA,YACV,WAAW;AAAA,YACX,SAAS;AAAA,UACb;AAAA,QACJ;AAAA,QACA;AAAA,UACI,KAAK;AAAA,YACD,UAAU;AAAA,YACV,WAAW;AAAA,YACX,SAAS;AAAA,UACb;AAAA,QACJ;AAAA,QACA;AAAA,UACI,KAAK;AAAA,YACD,UAAU;AAAA,YACV,WAAW;AAAA,YACX,SAAS;AAAA,UACb;AAAA,QACJ;AAAA,QACA;AAAA,UACI,KAAK;AAAA,YACD,UAAU;AAAA,YACV,WAAW;AAAA,YACX,SAAS;AAAA,UACb;AAAA,QACJ;AAAA,QACA;AAAA,UACI,KAAK;AAAA,YACD,UAAU;AAAA,YACV,WAAW;AAAA,YACX,SAAS;AAAA,UACb;AAAA,QACJ;AAAA,QACA;AAAA,UACI,KAAK;AAAA,YACD,UAAU;AAAA,YACV,WAAW;AAAA,YACX,SAAS;AAAA,UACb;AAAA,QACJ;AAAA,QACA;AAAA,UACI,KAAK;AAAA,YACD,UAAU;AAAA,YACV,WAAW;AAAA,YACX,SAAS;AAAA,UACb;AAAA,QACJ;AAAA,QACA;AAAA,UACI,KAAK;AAAA,YACD,UAAU;AAAA,YACV,WAAW;AAAA,YACX,SAAS;AAAA,UACb;AAAA,QACJ;AAAA,QACA;AAAA,UACI,KAAK;AAAA,YACD,UAAU;AAAA,YACV,WAAW;AAAA,YACX,SAAS;AAAA,UACb;AAAA,QACJ;AAAA,QACA;AAAA,UACI,KAAK;AAAA,YACD,UAAU;AAAA,YACV,WAAW;AAAA,YACX,SAAS;AAAA,UACb;AAAA,QACJ;AAAA,QACA;AAAA,UACI,KAAK;AAAA,YACD,UAAU;AAAA,YACV,WAAW;AAAA,YACX,SAAS;AAAA,UACb;AAAA,QACJ;AAAA,QACA;AAAA,UACI,KAAK;AAAA,YACD,UAAU;AAAA,YACV,WAAW;AAAA,YACX,SAAS;AAAA,UACb;AAAA,QACJ;AAAA,QACA;AAAA,UACI,KAAK;AAAA,YACD,UAAU;AAAA,YACV,WAAW;AAAA,YACX,SAAS;AAAA,UACb;AAAA,QACJ;AAAA,QACA;AAAA,UACI,IAAI;AAAA,YACA,UAAU;AAAA,YACV,WAAW;AAAA,YACX,SAAS;AAAA,UACb;AAAA,QACJ;AAAA,QACA;AAAA,UACI,IAAI;AAAA,YACA,UAAU;AAAA,YACV,WAAW;AAAA,YACX,SAAS;AAAA,UACb;AAAA,QACJ;AAAA,QACA;AAAA,UACI,IAAI;AAAA,YACA,UAAU;AAAA,YACV,WAAW;AAAA,YACX,SAAS;AAAA,UACb;AAAA,QACJ;AAAA,QACA;AAAA,UACI,IAAI;AAAA,YACA,UAAU;AAAA,YACV,WAAW;AAAA,YACX,SAAS;AAAA,UACb;AAAA,QACJ;AAAA,QACA;AAAA,UACI,IAAI;AAAA,YACA,UAAU;AAAA,YACV,WAAW;AAAA,YACX,SAAS;AAAA,UACb;AAAA,QACJ;AAAA,QACA;AAAA,UACI,IAAI;AAAA,YACA,UAAU;AAAA,YACV,WAAW;AAAA,YACX,SAAS;AAAA,UACb;AAAA,QACJ;AAAA,QACA;AAAA,UACI,IAAI;AAAA,YACA,UAAU;AAAA,YACV,WAAW;AAAA,YACX,SAAS;AAAA,UACb;AAAA,QACJ;AAAA,QACA;AAAA,UACI,IAAI;AAAA,YACA,UAAU;AAAA,YACV,WAAW;AAAA,YACX,SAAS;AAAA,UACb;AAAA,QACJ;AAAA,QACA;AAAA,UACI,IAAI;AAAA,YACA,UAAU;AAAA,YACV,WAAW;AAAA,YACX,SAAS;AAAA,UACb;AAAA,QACJ;AAAA,QACA;AAAA,UACI,IAAI;AAAA,YACA,UAAU;AAAA,YACV,WAAW;AAAA,YACX,SAAS;AAAA,UACb;AAAA,QACJ;AAAA,QACA;AAAA,UACI,IAAI;AAAA,YACA,UAAU;AAAA,YACV,WAAW;AAAA,YACX,SAAS;AAAA,UACb;AAAA,QACJ;AAAA,QACA;AAAA,UACI,IAAI;AAAA,YACA,UAAU;AAAA,YACV,WAAW;AAAA,YACX,SAAS;AAAA,UACb;AAAA,QACJ;AAAA,QACA;AAAA,UACI,IAAI;AAAA,YACA,UAAU;AAAA,YACV,WAAW;AAAA,YACX,SAAS;AAAA,UACb;AAAA,QACJ;AAAA,QACA;AAAA,UACI,IAAI;AAAA,YACA,UAAU;AAAA,YACV,WAAW;AAAA,YACX,SAAS;AAAA,UACb;AAAA,QACJ;AAAA,QACA;AAAA,UACI,IAAI;AAAA,YACA,UAAU;AAAA,YACV,WAAW;AAAA,YACX,SAAS;AAAA,UACb;AAAA,QACJ;AAAA,QACA;AAAA,UACI,IAAI;AAAA,YACA,UAAU;AAAA,YACV,WAAW;AAAA,YACX,SAAS;AAAA,UACb;AAAA,QACJ;AAAA,QACA;AAAA,UACI,IAAI;AAAA,YACA,UAAU;AAAA,YACV,WAAW;AAAA,YACX,SAAS;AAAA,UACb;AAAA,QACJ;AAAA,QACA;AAAA,UACI,IAAI;AAAA,YACA,UAAU;AAAA,YACV,WAAW;AAAA,YACX,SAAS;AAAA,UACb;AAAA,QACJ;AAAA,QACA;AAAA,UACI,IAAI;AAAA,YACA,UAAU;AAAA,YACV,WAAW;AAAA,YACX,SAAS;AAAA,UACb;AAAA,QACJ;AAAA,QACA;AAAA,UACI,IAAI;AAAA,YACA,UAAU;AAAA,YACV,WAAW;AAAA,YACX,SAAS;AAAA,UACb;AAAA,QACJ;AAAA,QACA;AAAA,UACI,IAAI;AAAA,YACA,UAAU;AAAA,YACV,WAAW;AAAA,YACX,SAAS;AAAA,UACb;AAAA,QACJ;AAAA,QACA;AAAA,UACI,IAAI;AAAA,YACA,UAAU;AAAA,YACV,WAAW;AAAA,YACX,SAAS;AAAA,UACb;AAAA,QACJ;AAAA,QACA;AAAA,UACI,IAAI;AAAA,YACA,UAAU;AAAA,YACV,WAAW;AAAA,YACX,SAAS;AAAA,UACb;AAAA,QACJ;AAAA,QACA;AAAA,UACI,IAAI;AAAA,YACA,UAAU;AAAA,YACV,WAAW;AAAA,YACX,SAAS;AAAA,UACb;AAAA,QACJ;AAAA,QACA;AAAA,UACI,IAAI;AAAA,YACA,UAAU;AAAA,YACV,WAAW;AAAA,YACX,SAAS;AAAA,UACb;AAAA,QACJ;AAAA,QACA;AAAA,UACI,IAAI;AAAA,YACA,UAAU;AAAA,YACV,WAAW;AAAA,YACX,SAAS;AAAA,UACb;AAAA,QACJ;AAAA,QACA;AAAA,UACI,IAAI;AAAA,YACA,UAAU;AAAA,YACV,WAAW;AAAA,YACX,SAAS;AAAA,UACb;AAAA,QACJ;AAAA,QACA;AAAA,UACI,IAAI;AAAA,YACA,UAAU;AAAA,YACV,WAAW;AAAA,YACX,SAAS;AAAA,UACb;AAAA,QACJ;AAAA,QACA;AAAA,UACI,IAAI;AAAA,YACA,UAAU;AAAA,YACV,WAAW;AAAA,YACX,SAAS;AAAA,UACb;AAAA,QACJ;AAAA,QACA;AAAA,UACI,IAAI;AAAA,YACA,UAAU;AAAA,YACV,WAAW;AAAA,YACX,SAAS;AAAA,UACb;AAAA,QACJ;AAAA,QACA;AAAA,UACI,IAAI;AAAA,YACA,UAAU;AAAA,YACV,WAAW;AAAA,YACX,SAAS;AAAA,UACb;AAAA,QACJ;AAAA,QACA;AAAA,UACI,IAAI;AAAA,YACA,UAAU;AAAA,YACV,WAAW;AAAA,YACX,SAAS;AAAA,UACb;AAAA,QACJ;AAAA,QACA;AAAA,UACI,IAAI;AAAA,YACA,UAAU;AAAA,YACV,WAAW;AAAA,YACX,SAAS;AAAA,UACb;AAAA,QACJ;AAAA,QACA;AAAA,UACI,IAAI;AAAA,YACA,UAAU;AAAA,YACV,WAAW;AAAA,YACX,SAAS;AAAA,UACb;AAAA,QACJ;AAAA,QACA;AAAA,UACI,IAAI;AAAA,YACA,UAAU;AAAA,YACV,WAAW;AAAA,YACX,SAAS;AAAA,UACb;AAAA,QACJ;AAAA,QACA;AAAA,UACI,IAAI;AAAA,YACA,UAAU;AAAA,YACV,WAAW;AAAA,YACX,SAAS;AAAA,UACb;AAAA,QACJ;AAAA,QACA;AAAA,UACI,IAAI;AAAA,YACA,UAAU;AAAA,YACV,WAAW;AAAA,YACX,SAAS;AAAA,UACb;AAAA,QACJ;AAAA,QACA;AAAA,UACI,IAAI;AAAA,YACA,UAAU;AAAA,YACV,WAAW;AAAA,YACX,SAAS;AAAA,UACb;AAAA,QACJ;AAAA,QACA;AAAA,UACI,IAAI;AAAA,YACA,UAAU;AAAA,YACV,WAAW;AAAA,YACX,SAAS;AAAA,UACb;AAAA,QACJ;AAAA,QACA;AAAA,UACI,IAAI;AAAA,YACA,UAAU;AAAA,YACV,WAAW;AAAA,YACX,SAAS;AAAA,UACb;AAAA,QACJ;AAAA,QACA;AAAA,UACI,IAAI;AAAA,YACA,UAAU;AAAA,YACV,WAAW;AAAA,YACX,SAAS;AAAA,UACb;AAAA,QACJ;AAAA,QACA;AAAA,UACI,IAAI;AAAA,YACA,UAAU;AAAA,YACV,WAAW;AAAA,YACX,SAAS;AAAA,UACb;AAAA,QACJ;AAAA,QACA;AAAA,UACI,IAAI;AAAA,YACA,UAAU;AAAA,YACV,WAAW;AAAA,YACX,SAAS;AAAA,UACb;AAAA,QACJ;AAAA,QACA;AAAA,UACI,IAAI;AAAA,YACA,UAAU;AAAA,YACV,WAAW;AAAA,YACX,SAAS;AAAA,UACb;AAAA,QACJ;AAAA,QACA;AAAA,UACI,IAAI;AAAA,YACA,UAAU;AAAA,YACV,WAAW;AAAA,YACX,SAAS;AAAA,UACb;AAAA,QACJ;AAAA,QACA;AAAA,UACI,IAAI;AAAA,YACA,UAAU;AAAA,YACV,WAAW;AAAA,YACX,SAAS;AAAA,UACb;AAAA,QACJ;AAAA,QACA;AAAA,UACI,IAAI;AAAA,YACA,UAAU;AAAA,YACV,WAAW;AAAA,YACX,SAAS;AAAA,UACb;AAAA,QACJ;AAAA,QACA;AAAA,UACI,IAAI;AAAA,YACA,UAAU;AAAA,YACV,WAAW;AAAA,YACX,SAAS;AAAA,UACb;AAAA,QACJ;AAAA,QACA;AAAA,UACI,IAAI;AAAA,YACA,UAAU;AAAA,YACV,WAAW;AAAA,YACX,SAAS;AAAA,UACb;AAAA,QACJ;AAAA,QACA;AAAA,UACI,IAAI;AAAA,YACA,UAAU;AAAA,YACV,WAAW;AAAA,YACX,SAAS;AAAA,UACb;AAAA,QACJ;AAAA,QACA;AAAA,UACI,IAAI;AAAA,YACA,UAAU;AAAA,YACV,WAAW;AAAA,YACX,SAAS;AAAA,UACb;AAAA,QACJ;AAAA,QACA;AAAA,UACI,IAAI;AAAA,YACA,UAAU;AAAA,YACV,WAAW;AAAA,YACX,SAAS;AAAA,UACb;AAAA,QACJ;AAAA,QACA;AAAA,UACI,IAAI;AAAA,YACA,UAAU;AAAA,YACV,WAAW;AAAA,YACX,SAAS;AAAA,UACb;AAAA,QACJ;AAAA,QACA;AAAA,UACI,IAAI;AAAA,YACA,UAAU;AAAA,YACV,WAAW;AAAA,YACX,SAAS;AAAA,UACb;AAAA,QACJ;AAAA,QACA;AAAA,UACI,IAAI;AAAA,YACA,UAAU;AAAA,YACV,WAAW;AAAA,YACX,SAAS;AAAA,UACb;AAAA,QACJ;AAAA,QACA;AAAA,UACI,IAAI;AAAA,YACA,UAAU;AAAA,YACV,WAAW;AAAA,YACX,SAAS;AAAA,UACb;AAAA,QACJ;AAAA,QACA;AAAA,UACI,IAAI;AAAA,YACA,UAAU;AAAA,YACV,WAAW;AAAA,YACX,SAAS;AAAA,UACb;AAAA,QACJ;AAAA,QACA;AAAA,UACI,IAAI;AAAA,YACA,UAAU;AAAA,YACV,WAAW;AAAA,YACX,SAAS;AAAA,UACb;AAAA,QACJ;AAAA,QACA;AAAA,UACI,IAAI;AAAA,YACA,UAAU;AAAA,YACV,WAAW;AAAA,YACX,SAAS;AAAA,UACb;AAAA,QACJ;AAAA,QACA;AAAA,UACI,IAAI;AAAA,YACA,UAAU;AAAA,YACV,WAAW;AAAA,YACX,SAAS;AAAA,UACb;AAAA,QACJ;AAAA,QACA;AAAA,UACI,IAAI;AAAA,YACA,UAAU;AAAA,YACV,WAAW;AAAA,YACX,SAAS;AAAA,UACb;AAAA,QACJ;AAAA,QACA;AAAA,UACI,IAAI;AAAA,YACA,UAAU;AAAA,YACV,WAAW;AAAA,YACX,SAAS;AAAA,UACb;AAAA,QACJ;AAAA,QACA;AAAA,UACI,IAAI;AAAA,YACA,UAAU;AAAA,YACV,WAAW;AAAA,YACX,SAAS;AAAA,UACb;AAAA,QACJ;AAAA,QACA;AAAA,UACI,IAAI;AAAA,YACA,UAAU;AAAA,YACV,WAAW;AAAA,YACX,SAAS;AAAA,UACb;AAAA,QACJ;AAAA,QACA;AAAA,UACI,IAAI;AAAA,YACA,UAAU;AAAA,YACV,WAAW;AAAA,YACX,SAAS;AAAA,UACb;AAAA,QACJ;AAAA,QACA;AAAA,UACI,IAAI;AAAA,YACA,UAAU;AAAA,YACV,WAAW;AAAA,YACX,SAAS;AAAA,UACb;AAAA,QACJ;AAAA,QACA;AAAA,UACI,IAAI;AAAA,YACA,UAAU;AAAA,YACV,WAAW;AAAA,YACX,SAAS;AAAA,UACb;AAAA,QACJ;AAAA,QACA;AAAA,UACI,IAAI;AAAA,YACA,UAAU;AAAA,YACV,WAAW;AAAA,YACX,SAAS;AAAA,UACb;AAAA,QACJ;AAAA,QACA;AAAA,UACI,IAAI;AAAA,YACA,UAAU;AAAA,YACV,WAAW;AAAA,YACX,SAAS;AAAA,UACb;AAAA,QACJ;AAAA,QACA;AAAA,UACI,IAAI;AAAA,YACA,UAAU;AAAA,YACV,WAAW;AAAA,YACX,SAAS;AAAA,UACb;AAAA,QACJ;AAAA,QACA;AAAA,UACI,IAAI;AAAA,YACA,UAAU;AAAA,YACV,WAAW;AAAA,YACX,SAAS;AAAA,UACb;AAAA,QACJ;AAAA,QACA;AAAA,UACI,IAAI;AAAA,YACA,UAAU;AAAA,YACV,WAAW;AAAA,YACX,SAAS;AAAA,UACb;AAAA,QACJ;AAAA,QACA;AAAA,UACI,IAAI;AAAA,YACA,UAAU;AAAA,YACV,WAAW;AAAA,YACX,SAAS;AAAA,UACb;AAAA,QACJ;AAAA,QACA;AAAA,UACI,IAAI;AAAA,YACA,UAAU;AAAA,YACV,WAAW;AAAA,YACX,SAAS;AAAA,UACb;AAAA,QACJ;AAAA,QACA;AAAA,UACI,IAAI;AAAA,YACA,UAAU;AAAA,YACV,WAAW;AAAA,YACX,SAAS;AAAA,UACb;AAAA,QACJ;AAAA,QACA;AAAA,UACI,IAAI;AAAA,YACA,UAAU;AAAA,YACV,WAAW;AAAA,YACX,SAAS;AAAA,UACb;AAAA,QACJ;AAAA,QACA;AAAA,UACI,IAAI;AAAA,YACA,UAAU;AAAA,YACV,WAAW;AAAA,YACX,SAAS;AAAA,UACb;AAAA,QACJ;AAAA,QACA;AAAA,UACI,IAAI;AAAA,YACA,UAAU;AAAA,YACV,WAAW;AAAA,YACX,SAAS;AAAA,UACb;AAAA,QACJ;AAAA,QACA;AAAA,UACI,IAAI;AAAA,YACA,UAAU;AAAA,YACV,WAAW;AAAA,YACX,SAAS;AAAA,UACb;AAAA,QACJ;AAAA,QACA;AAAA,UACI,IAAI;AAAA,YACA,UAAU;AAAA,YACV,WAAW;AAAA,YACX,SAAS;AAAA,UACb;AAAA,QACJ;AAAA,QACA;AAAA,UACI,IAAI;AAAA,YACA,UAAU;AAAA,YACV,WAAW;AAAA,YACX,SAAS;AAAA,UACb;AAAA,QACJ;AAAA,QACA;AAAA,UACI,IAAI;AAAA,YACA,UAAU;AAAA,YACV,WAAW;AAAA,YACX,SAAS;AAAA,UACb;AAAA,QACJ;AAAA,QACA;AAAA,UACI,IAAI;AAAA,YACA,UAAU;AAAA,YACV,WAAW;AAAA,YACX,SAAS;AAAA,UACb;AAAA,QACJ;AAAA,QACA;AAAA,UACI,IAAI;AAAA,YACA,UAAU;AAAA,YACV,WAAW;AAAA,YACX,SAAS;AAAA,UACb;AAAA,QACJ;AAAA,QACA;AAAA,UACI,IAAI;AAAA,YACA,UAAU;AAAA,YACV,WAAW;AAAA,YACX,SAAS;AAAA,UACb;AAAA,QACJ;AAAA,QACA;AAAA,UACI,IAAI;AAAA,YACA,UAAU;AAAA,YACV,WAAW;AAAA,YACX,SAAS;AAAA,UACb;AAAA,QACJ;AAAA,QACA;AAAA,UACI,IAAI;AAAA,YACA,UAAU;AAAA,YACV,WAAW;AAAA,YACX,SAAS;AAAA,UACb;AAAA,QACJ;AAAA,QACA;AAAA,UACI,IAAI;AAAA,YACA,UAAU;AAAA,YACV,WAAW;AAAA,YACX,SAAS;AAAA,UACb;AAAA,QACJ;AAAA,QACA;AAAA,UACI,IAAI;AAAA,YACA,UAAU;AAAA,YACV,WAAW;AAAA,YACX,SAAS;AAAA,UACb;AAAA,QACJ;AAAA,QACA;AAAA,UACI,IAAI;AAAA,YACA,UAAU;AAAA,YACV,WAAW;AAAA,YACX,SAAS;AAAA,UACb;AAAA,QACJ;AAAA,QACA;AAAA,UACI,IAAI;AAAA,YACA,UAAU;AAAA,YACV,WAAW;AAAA,YACX,SAAS;AAAA,UACb;AAAA,QACJ;AAAA,QACA;AAAA,UACI,IAAI;AAAA,YACA,UAAU;AAAA,YACV,WAAW;AAAA,YACX,SAAS;AAAA,UACb;AAAA,QACJ;AAAA,QACA;AAAA,UACI,IAAI;AAAA,YACA,UAAU;AAAA,YACV,WAAW;AAAA,YACX,SAAS;AAAA,UACb;AAAA,QACJ;AAAA,QACA;AAAA,UACI,IAAI;AAAA,YACA,UAAU;AAAA,YACV,WAAW;AAAA,YACX,SAAS;AAAA,UACb;AAAA,QACJ;AAAA,QACA;AAAA,UACI,IAAI;AAAA,YACA,UAAU;AAAA,YACV,WAAW;AAAA,YACX,SAAS;AAAA,UACb;AAAA,QACJ;AAAA,QACA;AAAA,UACI,IAAI;AAAA,YACA,UAAU;AAAA,YACV,WAAW;AAAA,YACX,SAAS;AAAA,UACb;AAAA,QACJ;AAAA,QACA;AAAA,UACI,IAAI;AAAA,YACA,UAAU;AAAA,YACV,WAAW;AAAA,YACX,SAAS;AAAA,UACb;AAAA,QACJ;AAAA,QACA;AAAA,UACI,IAAI;AAAA,YACA,UAAU;AAAA,YACV,WAAW;AAAA,YACX,SAAS;AAAA,UACb;AAAA,QACJ;AAAA,QACA;AAAA,UACI,IAAI;AAAA,YACA,UAAU;AAAA,YACV,WAAW;AAAA,YACX,SAAS;AAAA,UACb;AAAA,QACJ;AAAA,QACA;AAAA,UACI,IAAI;AAAA,YACA,UAAU;AAAA,YACV,WAAW;AAAA,YACX,SAAS;AAAA,UACb;AAAA,QACJ;AAAA,QACA;AAAA,UACI,IAAI;AAAA,YACA,UAAU;AAAA,YACV,WAAW;AAAA,YACX,SAAS;AAAA,UACb;AAAA,QACJ;AAAA,QACA;AAAA,UACI,IAAI;AAAA,YACA,UAAU;AAAA,YACV,WAAW;AAAA,YACX,SAAS;AAAA,UACb;AAAA,QACJ;AAAA,QACA;AAAA,UACI,IAAI;AAAA,YACA,UAAU;AAAA,YACV,WAAW;AAAA,YACX,SAAS;AAAA,UACb;AAAA,QACJ;AAAA,QACA;AAAA,UACI,KAAK;AAAA,YACD,UAAU;AAAA,YACV,WAAW;AAAA,YACX,SAAS;AAAA,UACb;AAAA,QACJ;AAAA,QACA;AAAA,UACI,IAAI;AAAA,YACA,UAAU;AAAA,YACV,WAAW;AAAA,YACX,SAAS;AAAA,UACb;AAAA,QACJ;AAAA,QACA;AAAA,UACI,IAAI;AAAA,YACA,UAAU;AAAA,YACV,WAAW;AAAA,YACX,SAAS;AAAA,UACb;AAAA,QACJ;AAAA,QACA;AAAA,UACI,IAAI;AAAA,YACA,UAAU;AAAA,YACV,WAAW;AAAA,YACX,SAAS;AAAA,UACb;AAAA,QACJ;AAAA,QACA;AAAA,UACI,IAAI;AAAA,YACA,UAAU;AAAA,YACV,WAAW;AAAA,YACX,SAAS;AAAA,UACb;AAAA,QACJ;AAAA,QACA;AAAA,UACI,IAAI;AAAA,YACA,UAAU;AAAA,YACV,WAAW;AAAA,YACX,SAAS;AAAA,UACb;AAAA,QACJ;AAAA,QACA;AAAA,UACI,IAAI;AAAA,YACA,UAAU;AAAA,YACV,WAAW;AAAA,YACX,SAAS;AAAA,UACb;AAAA,QACJ;AAAA,QACA;AAAA,UACI,IAAI;AAAA,YACA,UAAU;AAAA,YACV,WAAW;AAAA,YACX,SAAS;AAAA,UACb;AAAA,QACJ;AAAA,QACA;AAAA,UACI,IAAI;AAAA,YACA,UAAU;AAAA,YACV,WAAW;AAAA,YACX,SAAS;AAAA,UACb;AAAA,QACJ;AAAA,QACA;AAAA,UACI,IAAI;AAAA,YACA,UAAU;AAAA,YACV,WAAW;AAAA,YACX,SAAS;AAAA,UACb;AAAA,QACJ;AAAA,QACA;AAAA,UACI,IAAI;AAAA,YACA,UAAU;AAAA,YACV,WAAW;AAAA,YACX,SAAS;AAAA,UACb;AAAA,QACJ;AAAA,QACA;AAAA,UACI,IAAI;AAAA,YACA,UAAU;AAAA,YACV,WAAW;AAAA,YACX,SAAS;AAAA,UACb;AAAA,QACJ;AAAA,QACA;AAAA,UACI,IAAI;AAAA,YACA,UAAU;AAAA,YACV,WAAW;AAAA,YACX,SAAS;AAAA,UACb;AAAA,QACJ;AAAA,QACA;AAAA,UACI,IAAI;AAAA,YACA,UAAU;AAAA,YACV,WAAW;AAAA,YACX,SAAS;AAAA,UACb;AAAA,QACJ;AAAA,QACA;AAAA,UACI,IAAI;AAAA,YACA,UAAU;AAAA,YACV,WAAW;AAAA,YACX,SAAS;AAAA,UACb;AAAA,QACJ;AAAA,QACA;AAAA,UACI,IAAI;AAAA,YACA,UAAU;AAAA,YACV,WAAW;AAAA,YACX,SAAS;AAAA,UACb;AAAA,QACJ;AAAA,QACA;AAAA,UACI,KAAK;AAAA,YACD,UAAU;AAAA,YACV,WAAW;AAAA,UACf;AAAA,QACJ;AAAA,QACA;AAAA,UACI,WAAW;AAAA,YACP,UAAU;AAAA,YACV,WAAW;AAAA,YACX,SAAS;AAAA,UACb;AAAA,QACJ;AAAA,QACA;AAAA,UACI,WAAW;AAAA,YACP,UAAU;AAAA,YACV,WAAW;AAAA,YACX,SAAS;AAAA,UACb;AAAA,QACJ;AAAA,QACA;AAAA,UACI,WAAW;AAAA,YACP,UAAU;AAAA,YACV,WAAW;AAAA,YACX,SAAS;AAAA,UACb;AAAA,QACJ;AAAA,QACA;AAAA,UACI,WAAW;AAAA,YACP,UAAU;AAAA,YACV,WAAW;AAAA,YACX,SAAS;AAAA,UACb;AAAA,QACJ;AAAA,QACA;AAAA,UACI,WAAW;AAAA,YACP,UAAU;AAAA,YACV,WAAW;AAAA,YACX,SAAS;AAAA,UACb;AAAA,QACJ;AAAA,QACA;AAAA,UACI,WAAW;AAAA,YACP,UAAU;AAAA,YACV,WAAW;AAAA,YACX,SAAS;AAAA,UACb;AAAA,QACJ;AAAA,QACA;AAAA,UACI,WAAW;AAAA,YACP,UAAU;AAAA,YACV,WAAW;AAAA,YACX,SAAS;AAAA,UACb;AAAA,QACJ;AAAA,QACA;AAAA,UACI,WAAW;AAAA,YACP,UAAU;AAAA,YACV,WAAW;AAAA,YACX,SAAS;AAAA,UACb;AAAA,QACJ;AAAA,QACA;AAAA,UACI,WAAW;AAAA,YACP,UAAU;AAAA,YACV,WAAW;AAAA,YACX,SAAS;AAAA,UACb;AAAA,QACJ;AAAA,QACA;AAAA,UACI,WAAW;AAAA,YACP,UAAU;AAAA,YACV,WAAW;AAAA,YACX,SAAS;AAAA,UACb;AAAA,QACJ;AAAA,QACA;AAAA,UACI,WAAW;AAAA,YACP,UAAU;AAAA,YACV,WAAW;AAAA,YACX,SAAS;AAAA,UACb;AAAA,QACJ;AAAA,QACA;AAAA,UACI,WAAW;AAAA,YACP,UAAU;AAAA,YACV,WAAW;AAAA,YACX,SAAS;AAAA,UACb;AAAA,QACJ;AAAA,QACA;AAAA,UACI,WAAW;AAAA,YACP,UAAU;AAAA,YACV,WAAW;AAAA,YACX,SAAS;AAAA,UACb;AAAA,QACJ;AAAA,QACA;AAAA,UACI,WAAW;AAAA,YACP,UAAU;AAAA,YACV,WAAW;AAAA,YACX,SAAS;AAAA,UACb;AAAA,QACJ;AAAA,QACA;AAAA,UACI,WAAW;AAAA,YACP,UAAU;AAAA,YACV,WAAW;AAAA,YACX,SAAS;AAAA,UACb;AAAA,QACJ;AAAA,QACA;AAAA,UACI,WAAW;AAAA,YACP,UAAU;AAAA,YACV,WAAW;AAAA,YACX,SAAS;AAAA,UACb;AAAA,QACJ;AAAA,QACA;AAAA,UACI,WAAW;AAAA,YACP,UAAU;AAAA,YACV,WAAW;AAAA,YACX,SAAS;AAAA,UACb;AAAA,QACJ;AAAA,QACA;AAAA,UACI,WAAW;AAAA,YACP,UAAU;AAAA,YACV,WAAW;AAAA,YACX,SAAS;AAAA,UACb;AAAA,QACJ;AAAA,QACA;AAAA,UACI,WAAW;AAAA,YACP,UAAU;AAAA,YACV,WAAW;AAAA,YACX,SAAS;AAAA,UACb;AAAA,QACJ;AAAA,QACA;AAAA,UACI,WAAW;AAAA,YACP,UAAU;AAAA,YACV,WAAW;AAAA,YACX,SAAS;AAAA,UACb;AAAA,QACJ;AAAA,QACA;AAAA,UACI,WAAW;AAAA,YACP,UAAU;AAAA,YACV,WAAW;AAAA,YACX,SAAS;AAAA,UACb;AAAA,QACJ;AAAA,QACA;AAAA,UACI,WAAW;AAAA,YACP,UAAU;AAAA,YACV,WAAW;AAAA,YACX,SAAS;AAAA,UACb;AAAA,QACJ;AAAA,QACA;AAAA,UACI,WAAW;AAAA,YACP,UAAU;AAAA,YACV,WAAW;AAAA,YACX,SAAS;AAAA,UACb;AAAA,QACJ;AAAA,QACA;AAAA,UACI,WAAW;AAAA,YACP,UAAU;AAAA,YACV,WAAW;AAAA,YACX,SAAS;AAAA,UACb;AAAA,QACJ;AAAA,QACA;AAAA,UACI,WAAW;AAAA,YACP,UAAU;AAAA,YACV,WAAW;AAAA,UACf;AAAA,QACJ;AAAA,QACA;AAAA,UACI,WAAW;AAAA,YACP,UAAU;AAAA,YACV,WAAW;AAAA,YACX,SAAS;AAAA,UACb;AAAA,QACJ;AAAA,QACA;AAAA,UACI,WAAW;AAAA,YACP,UAAU;AAAA,YACV,WAAW;AAAA,YACX,SAAS;AAAA,UACb;AAAA,QACJ;AAAA,QACA;AAAA,UACI,WAAW;AAAA,YACP,UAAU;AAAA,YACV,WAAW;AAAA,YACX,SAAS;AAAA,UACb;AAAA,QACJ;AAAA,QACA;AAAA,UACI,WAAW;AAAA,YACP,UAAU;AAAA,YACV,WAAW;AAAA,YACX,SAAS;AAAA,UACb;AAAA,QACJ;AAAA,QACA;AAAA,UACI,WAAW;AAAA,YACP,UAAU;AAAA,YACV,WAAW;AAAA,YACX,SAAS;AAAA,UACb;AAAA,QACJ;AAAA,QACA;AAAA,UACI,WAAW;AAAA,YACP,UAAU;AAAA,YACV,WAAW;AAAA,YACX,SAAS;AAAA,UACb;AAAA,QACJ;AAAA,QACA;AAAA,UACI,WAAW;AAAA,YACP,UAAU;AAAA,YACV,WAAW;AAAA,YACX,SAAS;AAAA,UACb;AAAA,QACJ;AAAA,QACA;AAAA,UACI,WAAW;AAAA,YACP,UAAU;AAAA,YACV,WAAW;AAAA,YACX,SAAS;AAAA,UACb;AAAA,QACJ;AAAA,QACA;AAAA,UACI,WAAW;AAAA,YACP,UAAU;AAAA,YACV,WAAW;AAAA,YACX,SAAS;AAAA,UACb;AAAA,QACJ;AAAA,QACA;AAAA,UACI,WAAW;AAAA,YACP,UAAU;AAAA,YACV,WAAW;AAAA,YACX,SAAS;AAAA,UACb;AAAA,QACJ;AAAA,QACA;AAAA,UACI,WAAW;AAAA,YACP,UAAU;AAAA,YACV,WAAW;AAAA,YACX,SAAS;AAAA,UACb;AAAA,QACJ;AAAA,QACA;AAAA,UACI,WAAW;AAAA,YACP,UAAU;AAAA,YACV,WAAW;AAAA,YACX,SAAS;AAAA,UACb;AAAA,QACJ;AAAA,QACA;AAAA,UACI,WAAW;AAAA,YACP,UAAU;AAAA,YACV,WAAW;AAAA,YACX,SAAS;AAAA,UACb;AAAA,QACJ;AAAA,QACA;AAAA,UACI,WAAW;AAAA,YACP,UAAU;AAAA,YACV,WAAW;AAAA,YACX,SAAS;AAAA,UACb;AAAA,QACJ;AAAA,QACA;AAAA,UACI,WAAW;AAAA,YACP,UAAU;AAAA,YACV,WAAW;AAAA,YACX,SAAS;AAAA,UACb;AAAA,QACJ;AAAA,QACA;AAAA,UACI,WAAW;AAAA,YACP,UAAU;AAAA,YACV,WAAW;AAAA,YACX,SAAS;AAAA,UACb;AAAA,QACJ;AAAA,QACA;AAAA,UACI,WAAW;AAAA,YACP,UAAU;AAAA,YACV,WAAW;AAAA,YACX,SAAS;AAAA,UACb;AAAA,QACJ;AAAA,QACA;AAAA,UACI,WAAW;AAAA,YACP,UAAU;AAAA,YACV,WAAW;AAAA,YACX,SAAS;AAAA,UACb;AAAA,QACJ;AAAA,QACA;AAAA,UACI,WAAW;AAAA,YACP,UAAU;AAAA,YACV,WAAW;AAAA,YACX,SAAS;AAAA,UACb;AAAA,QACJ;AAAA,QACA;AAAA,UACI,WAAW;AAAA,YACP,UAAU;AAAA,YACV,WAAW;AAAA,YACX,SAAS;AAAA,UACb;AAAA,QACJ;AAAA,QACA;AAAA,UACI,WAAW;AAAA,YACP,UAAU;AAAA,YACV,WAAW;AAAA,YACX,SAAS;AAAA,UACb;AAAA,QACJ;AAAA,QACA;AAAA,UACI,WAAW;AAAA,YACP,UAAU;AAAA,YACV,WAAW;AAAA,YACX,SAAS;AAAA,UACb;AAAA,QACJ;AAAA,QACA;AAAA,UACI,WAAW;AAAA,YACP,UAAU;AAAA,YACV,WAAW;AAAA,YACX,SAAS;AAAA,UACb;AAAA,QACJ;AAAA,QACA;AAAA,UACI,WAAW;AAAA,YACP,UAAU;AAAA,YACV,WAAW;AAAA,YACX,SAAS;AAAA,UACb;AAAA,QACJ;AAAA,QACA;AAAA,UACI,OAAO;AAAA,YACH,UAAU;AAAA,YACV,WAAW;AAAA,UACf;AAAA,QACJ;AAAA,QACA;AAAA,UACI,iBAAiB;AAAA,YACb,UAAU;AAAA,YACV,WAAW;AAAA,UACf;AAAA,QACJ;AAAA,QACA;AAAA,UACI,kBAAkB;AAAA,YACd,UAAU;AAAA,YACV,WAAW;AAAA,UACf;AAAA,QACJ;AAAA,QACA;AAAA,UACI,UAAU;AAAA,YACN,UAAU;AAAA,YACV,WAAW;AAAA,UACf;AAAA,QACJ;AAAA,QACA;AAAA,UACI,cAAc;AAAA,YACV,UAAU;AAAA,YACV,WAAW;AAAA,UACf;AAAA,QACJ;AAAA,QACA;AAAA,UACI,WAAW;AAAA,YACP,UAAU;AAAA,YACV,WAAW;AAAA,UACf;AAAA,QACJ;AAAA,QACA;AAAA,UACI,eAAe;AAAA,YACX,UAAU;AAAA,YACV,WAAW;AAAA,UACf;AAAA,QACJ;AAAA,QACA;AAAA,UACI,UAAU;AAAA,YACN,UAAU;AAAA,YACV,WAAW;AAAA,UACf;AAAA,QACJ;AAAA,QACA;AAAA,UACI,kBAAkB;AAAA,YACd,UAAU;AAAA,YACV,WAAW;AAAA,UACf;AAAA,QACJ;AAAA,QACA;AAAA,UACI,mBAAmB;AAAA,YACf,UAAU;AAAA,YACV,WAAW;AAAA,UACf;AAAA,QACJ;AAAA,QACA;AAAA,UACI,UAAU;AAAA,YACN,UAAU;AAAA,YACV,WAAW;AAAA,UACf;AAAA,QACJ;AAAA,QACA;AAAA,UACI,kBAAkB;AAAA,YACd,UAAU;AAAA,YACV,WAAW;AAAA,UACf;AAAA,QACJ;AAAA,QACA;AAAA,UACI,mBAAmB;AAAA,YACf,UAAU;AAAA,YACV,WAAW;AAAA,UACf;AAAA,QACJ;AAAA,QACA;AAAA,UACI,UAAU;AAAA,YACN,UAAU;AAAA,YACV,WAAW;AAAA,UACf;AAAA,QACJ;AAAA,QACA;AAAA,UACI,kBAAkB;AAAA,YACd,UAAU;AAAA,YACV,WAAW;AAAA,UACf;AAAA,QACJ;AAAA,QACA;AAAA,UACI,mBAAmB;AAAA,YACf,UAAU;AAAA,YACV,WAAW;AAAA,UACf;AAAA,QACJ;AAAA,QACA;AAAA,UACI,aAAa;AAAA,YACT,UAAU;AAAA,YACV,WAAW;AAAA,UACf;AAAA,QACJ;AAAA,QACA;AAAA,UACI,SAAS;AAAA,YACL,UAAU;AAAA,YACV,WAAW;AAAA,UACf;AAAA,QACJ;AAAA,MACJ;AAAA,IACJ;AAAA,EACJ;AACJ;;;AC1jFO,IAAI,UAAU;AAAA,EACjB,OAAO;AAAA,IACH;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACJ;AAAA,EACA,OAAO;AAAA,IACH;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACJ;AAAA,EACA,OAAO;AAAA,IACH;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACJ;AAAA,EACA,OAAO;AAAA,IACH;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACJ;AAAA,EACA,OAAO;AAAA,IACH;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACJ;AAAA,EACA,OAAO;AAAA,IACH;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACJ;AAAA,EACA,OAAO;AAAA,IACH;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACJ;AAAA,EACA,OAAO;AAAA,IACH;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACJ;AAAA,EACA,OAAO;AAAA,IACH;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACJ;AAAA,EACA,OAAO;AAAA,IACH;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACJ;AAAA,EACA,OAAO;AAAA,IACH;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACJ;AAAA,EACA,OAAO;AAAA,IACH;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACJ;AAAA,EACA,OAAO;AAAA,IACH;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACJ;AAAA,EACA,OAAO;AAAA,IACH;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACJ;AAAA,EACA,OAAO;AAAA,IACH;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACJ;AAAA,EACA,OAAO;AAAA,IACH;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACJ;AAAA,EACA,OAAO;AAAA,IACH;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACJ;AAAA,EACA,OAAO;AAAA,IACH;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACJ;AAAA,EACA,OAAO;AAAA,IACH;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACJ;AAAA,EACA,OAAO;AAAA,IACH;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACJ;AAAA,EACA,OAAO;AAAA,IACH;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACJ;AAAA,EACA,OAAO;AAAA,IACH;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACJ;AAAA,EACA,OAAO;AAAA,IACH;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACJ;AAAA,EACA,OAAO;AAAA,IACH;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACJ;AAAA,EACA,OAAO;AAAA,IACH;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACJ;AAAA,EACA,OAAO;AAAA,IACH;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACJ;AAAA,EACA,OAAO;AAAA,IACH;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACJ;AAAA,EACA,OAAO;AAAA,IACH;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACJ;AAAA,EACA,OAAO;AAAA,IACH;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACJ;AAAA,EACA,OAAO;AAAA,IACH;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACJ;AAAA,EACA,OAAO;AAAA,IACH;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACJ;AAAA,EACA,MAAM;AAAA,IACF;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACJ;AAAA,EACA,MAAM;AAAA,IACF;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACJ;AAAA,EACA,MAAM;AAAA,IACF;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACJ;AAAA,EACA,MAAM;AAAA,IACF;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACJ;AACJ;;;AClzCO,IAAI,mCAAmC;AACvC,SAAS,UAAU,WAAW,SAAS,KAAK;AAC/C,MAAI,QAAQ,QAAQ;AAAE,UAAM;AAAA,EAAO;AACnC,MAAI,CAAC,WAAW;AACZ,UAAM,IAAI,IAAI,OAAO;AAAA,EACzB;AACJ;AAEA,IAAI,6BAA6B;AACjC,IAAI;AACJ,SAAS,cAAc;AACnB,MAAI,IAAI;AACR,MAAI,CAAC,gBAAgB;AACjB,QAAI,mBAAmB,MAAM,KAAK,KAAS,aAAa,iBAAiB,aAAa,EAAE,CAAC,OAAO,QAAQ,OAAO,SAAS,SAAS,GAAG,qBAAqB,QAAQ,OAAO,SAAS,SAAS,GAAG,SAAS,MAAM,GAAG;AAC/M,QAAI,iBAAiB,KAAS,aAAa,iBAAiB,aAAa,EAAE,MAAM,GAAG,CAAC;AACrF,QAAIA,QAAO,KAAS,aAAa,iBAAiB,aAAa,EAAE,MAAM,CAAC;AACxE,QAAI,UAAUA,MAAK,IAAI,SAAU,GAAG;AAChC,UAAI,MAAM,OAAO,KAAK,CAAC,EAAE,CAAC;AAC1B,UAAI,QAAQ,EAAE,GAAG;AACjB,aAAO;AAAA,QACH,WAAW;AAAA,QACX,SAAS,MAAM;AAAA,QACf,UAAU,CAAC,MAAM;AAAA,QACjB,QAAQ,MAAM,WAAW,SAAS,OAAO;AAAA,MAC7C;AAAA,IACJ,GAAG,CAAC,CAAC;AACL,qBAAiB;AAAA,MACb;AAAA,MACA,gBAAgB,eAAe,OAAO,SAAU,KAAK,GAAG;AACpD,YAAI,MAAM,OAAO,KAAK,CAAC,EAAE,CAAC;AAC1B,YAAI,QAAQ,EAAE,GAAG;AACjB,YAAI,IAAI,MAAM,CAAC,CAAC,IAAI,MAAM,OAAO,MAAM,GAAG;AAC1C,eAAO;AAAA,MACX,GAAG,CAAC,CAAC;AAAA,MACL,iBAAiB,cAAc,cAAc,CAAC,GAAG,iBAAiB,IAAI,GAAG,gBAAgB,IAAI,SAAU,GAAG;AACtG,eAAO,IAAI,KAAK,OAAO,EAAE,QAAQ,MAAM,GAAG,CAAC,EAAE,SAAS,EAAE,SAAS;AAAA,MACrE,CAAC,GAAG,IAAI;AAAA,IACZ;AAAA,EACJ;AACA,SAAO;AACX;AACA,SAAS,UAAU,QAAQ,yBAAyB,gBAAgB;AAChE,MAAI,KAAK,wBAAwB,MAAM,GAAG,GAAG,WAAW,GAAG,CAAC,GAAG,SAAS,GAAG,CAAC,GAAG,SAAS,GAAG,CAAC;AAC5F,MAAI,UAAU;AACd,MAAI,UAAU,OAAO,CAAC,MAAM,KAAK;AAC7B,QAAI,gBAAgB,OAAO,CAAC,MAAM;AAClC,QAAI,eAAe,gBACb,eAAe,OAAO,MAAM,CAAC,CAAC,IAC9B,eAAe,OAAO,MAAM,CAAC,CAAC;AACpC,QAAI,yBAAyB,aACxB,IAAI,SAAU,GAAG;AAAE,aAAO,QAAQ,CAAC,KAAK,CAAC,CAAC;AAAA,IAAG,CAAC,EAC9C,OAAO,SAAU,KAAK,MAAM;AAAE,aAAO,cAAc,cAAc,CAAC,GAAG,KAAK,IAAI,GAAG,MAAM,IAAI;AAAA,IAAG,GAAG,CAAC,CAAC;AACxG,gBAAY,UAAU,EAAE,uBAAuB,QAAQ,OAAO,UAAU,EAAE,IAAI,MAC1E;AAAA,EACR,OACK;AACD,gBAAY,UAAU,OAAO,SACvB,WAAW,OAAO,WAAW,OAAO,SACpC;AAAA,EACV;AACA,cAAY,UAAU,OAAO,SAAS,WAAW,OAAO,WAAW,OAAO,SAAS;AACnF,cAAY,UAAU,OAAO,WACvB,aAAa,OAAO,aAAa,OAAO,WACxC;AACN,SAAO;AACX;AACA,SAAS,aAAa,KAAK;AACvB,SAAO,CAAC,IAAI,UAAU,IAAI,QAAQ,IAAI,MAAM,EAAE,OAAO,OAAO,EAAE,KAAK,GAAG;AAC1E;AACA,SAAS,2BAA2B,SAAS,WAAWA,OAAM;AAC1D,WAAS,KAAK,GAAG,KAAKA,MAAK,SAAS,KAAK,GAAG,QAAQ,MAAM;AACtD,QAAI,IAAI,GAAG,EAAE;AACb,QAAI,UAAU,UAAU,SAAS,EAAE,SAASA,MAAK,cAAc,KAC3D,UAAU,WAAW,EAAE,WAAWA,MAAK,cAAc;AACzD,QAAI,CAAC,EAAE,UAAU,CAAC,SAAS;AACvB,gBACI,UAAU,SAAS,EAAE,WAAWA,MAAK,cAAc,KAC/C,UAAU,WAAW,EAAE,SAASA,MAAK,cAAc;AAAA,IAC/D;AACA,QAAI,SAAS;AACT,UAAI,WAAW,EAAE,WAAW;AAC5B,UAAIA,MAAK,gBAAgB,QAAQ,aAAa,OAAO,CAAC,IAAI,MACtDA,MAAK,gBAAgB,QAAQ,aAAa,SAAS,CAAC,IAAI,IAAI;AAC5D,eAAO,WAAW;AAAA,MACtB;AACA,aAAO;AAAA,IACX;AAAA,EACJ;AACA,QAAM,IAAI,MAAM,4BAA4B;AAChD;AACO,SAAS,qBAAqB,SAAS,WAAW;AACrD,MAAI,gBAAgB,IAAI,KAAK,OAAO,OAAO,EAAE,SAAS;AACtD,MAAI,kBAAkB,IAAI,KAAK,OAAO,SAAS,EAAE,SAAS;AAC1D,MAAI,aAAa;AAAA,IACb,UAAU,cAAc;AAAA,IACxB,QAAQ,cAAc,UAAU;AAAA,IAChC,QAAQ,cAAc,UAAU;AAAA,EACpC;AACA,MAAI,eAAe;AAAA,IACf,UAAU,gBAAgB;AAAA,IAC1B,QAAQ,gBAAgB,UAAU;AAAA,IAClC,QAAQ,gBAAgB,UAAU;AAAA,EACtC;AACA,MAAI,mBAAmB;AACvB,MAAIA,QAAO,YAAY;AACvB,MAAI,WAAW,aAAa,aAAa,UAAU;AAC/C,wBAAoB,2BAA2B;AAAA,MAC3C,UAAU,cAAc;AAAA,MACxB,QAAQ;AAAA,MACR,QAAQ;AAAA,IACZ,GAAG;AAAA,MACC,UAAU,gBAAgB;AAAA,MAC1B,QAAQ;AAAA,MACR,QAAQ;AAAA,IACZ,GAAGA,KAAI;AAAA,EACX;AACA,MAAI,WAAW,WAAW,aAAa,QAAQ;AAC3C,wBAAoB,2BAA2B;AAAA,MAC3C,UAAU,cAAc;AAAA,MACxB,QAAQ,WAAW;AAAA,MACnB,QAAQ;AAAA,IACZ,GAAG;AAAA,MACC,UAAU,gBAAgB;AAAA,MAC1B,QAAQ,aAAa;AAAA,MACrB,QAAQ;AAAA,IACZ,GAAGA,KAAI;AAAA,EACX;AACA,MAAI,WAAW,WAAW,aAAa,QAAQ;AAC3C,wBAAoB,2BAA2B,YAAY,cAAcA,KAAI;AAAA,EACjF;AACA,SAAO;AACX;AACO,SAAS,cAAc,kBAAkB,kBAAkB,WAAW;AACzE,MAAI,cAAc,QAAQ;AAAE,gBAAY;AAAA,EAA4B;AACpE,MAAI,iBAAiB;AACrB,MAAI,SAAS;AAAA,IACT,sBAAsB;AAAA,IACtB,WAAW,CAAC;AAAA,EAChB;AACA,mBAAiB,QAAQ,SAAU,SAAS,GAAG;AAC3C,QAAI,CAAC,OAAO,UAAU,OAAO,GAAG;AAC5B,aAAO,UAAU,OAAO,IAAI,CAAC;AAAA,IACjC;AACA,qBAAiB,QAAQ,SAAU,WAAW;AAI1C,UAAI,WAAW,qBAAqB,SAAS,SAAS,IAAI,IAAI,IAAI;AAClE,aAAO,UAAU,OAAO,EAAE,SAAS,IAAI;AACvC,UAAI,WAAW,gBAAgB;AAC3B,yBAAiB;AACjB,eAAO,uBAAuB;AAC9B,eAAO,yBAAyB;AAAA,MACpC;AAAA,IACJ,CAAC;AAAA,EACL,CAAC;AACD,MAAI,kBAAkB,WAAW;AAC7B,WAAO,uBAAuB;AAC9B,WAAO,yBAAyB;AAAA,EACpC;AACA,SAAO;AACX;;;AC7JO,SAAS,eAAe,kBAAkB,kBAAkB,kBAAkB;AACjF,MAAI;AACJ,MAAI;AACJ,MAAI,qBAAqB,CAAC;AAC1B,MAAI,uBAAuB,iBAAiB,OAAO,SAAU,KAAK,GAAG;AACjE,QAAI,oBAAoB,EAAE,QAAQ,kCAAkC,EAAE;AACtE,uBAAmB,KAAK,iBAAiB;AACzC,QAAI,iBAAiB,IAAI;AACzB,WAAO;AAAA,EACX,GAAG,CAAC,CAAC;AACL,MAAI,SAAS,cAAc,oBAAoB,gBAAgB;AAC/D,MAAI,OAAO,0BAA0B,OAAO,sBAAsB;AAC9D,kBAAc,OAAO;AACrB,gBACI,qBAAqB,OAAO,oBAAoB,EAAE,MAAM,OAAO,qBAAqB,MAAM,KAAK;AAAA,EACvG;AACA,MAAI,CAAC,aAAa;AACd,WAAO,EAAE,QAAQ,iBAAiB,EAAE;AAAA,EACxC;AACA,SAAO;AAAA,IACH,QAAQ;AAAA,IACR;AAAA,EACJ;AACJ;;;AC7BO,SAAS,mBAAmB,MAAM,QAAQ;AAE7C,MAAI,aAAa,OAAO,YAAY;AACpC,YAAU,SAAS,QAAW,sBAAsB;AACpD,MAAI,gBAAgB;AACpB,SAAO;AACX;;;ACPO,SAAS,4BAA4B,QAAQ;AAChD,SAAO,KAAK,oBAAoB,MAAM,EAAE,CAAC;AAC7C;;;ACAO,SAAS,sCAAsC,QAAQ,YAAY,UAAU;AAChF,YAAU,OAAO,QAAQ,KAAK,MAAM,IAAI,wDAAwD;AAChG,MAAI,YAAY;AAChB,WAAS,KAAK,GAAG,eAAe,YAAY,KAAK,aAAa,QAAQ,MAAM;AACxE,QAAI,OAAO,aAAa,EAAE;AAC1B,iBAAa,IAAI,OAAO,IAAI;AAAA,EAChC;AACA,WAAS,KAAK,GAAG,aAAa,UAAU,KAAK,WAAW,QAAQ,MAAM;AAClE,QAAI,KAAK,WAAW,EAAE;AACtB,QAAI,MAAM,GAAG,KAAK,QAAQ,GAAG;AAC7B,iBAAa,IAAI,OAAO,GAAG;AAC3B,QAAI,UAAU,IAAI;AACd,mBAAa,IAAI,OAAO,KAAK;AAAA,IACjC;AAAA,EACJ;AACA,MAAI,cAAc,MAAM;AACpB,WAAO,4BAA4B,MAAM;AAAA,EAC7C;AACA,MAAI,eAAe,OAAO,QAAQ,KAAK;AACvC,MAAI;AACJ,MAAI,iBAAiB,IAAI;AACrB,gBAAY,SAAS;AAAA,EACzB,OACK;AACD,QAAI,eAAe,OAAO,MAAM,GAAG,YAAY;AAC/C,QAAI,gBAAgB,OAAO,MAAM,YAAY;AAC7C,gBAAY,eAAe,YAAY;AAAA,EAC3C;AACA,SAAO,4BAA4B,SAAS;AAChD;;;AC1BO,SAAS,oBAAoB,kBAAkB,QAAQ;AAC1D,MAAI,YAAY;AAChB,SAAO,MAAM;AACT,QAAI,iBAAiB,QAAQ,SAAS,IAAI,IAAI;AAC1C,aAAO;AAAA,IACX;AACA,QAAI,MAAM,UAAU,YAAY,GAAG;AACnC,QAAI,CAAC,CAAC,KAAK;AACP,aAAO;AAAA,IACX;AACA,QAAI,OAAO,KAAK,UAAU,MAAM,CAAC,MAAM,KAAK;AACxC,aAAO;AAAA,IACX;AACA,gBAAY,UAAU,MAAM,GAAG,GAAG;AAAA,EACtC;AACJ;;;ACZO,SAAS,cAAc,kBAAkB,kBAAkB,kBAAkB;AAChF,MAAI,SAAS,EAAE,QAAQ,GAAG;AAC1B,WAAS,KAAK,GAAG,qBAAqB,kBAAkB,KAAK,mBAAmB,QAAQ,MAAM;AAC1F,QAAI,SAAS,mBAAmB,EAAE;AAClC,QAAI,oBAAoB,OAAO,QAAQ,kCAAkC,EAAE;AAC3E,QAAI,kBAAkB,oBAAoB,kBAAkB,iBAAiB;AAC7E,QAAI,iBAAiB;AACjB,aAAO,SAAS;AAChB,UAAI,WAAW,mBAAmB;AAC9B,eAAO,YAAY,OAAO,MAAM,kBAAkB,QAAQ,OAAO,MAAM;AAAA,MAC3E;AACA,aAAO;AAAA,IACX;AAAA,EACJ;AACA,SAAO,SAAS,iBAAiB;AACjC,SAAO;AACX;;;ACvBO,SAAS,2BAA2B,WAAW;AAClD,YAAU,cAAc,UAAU,YAAY,GAAG,oCAAoC;AACrF,YAAU,UAAU,MAAM,GAAG,CAAC,MAAM,OAAO,qDAAqD;AAChG,MAAI,aAAa,CAAC;AAClB,MAAI,WAAW,CAAC;AAChB,MAAI;AACJ,MAAI,OAAO,UAAU;AACrB,MAAI,IAAI;AACR,SAAO,IAAI,MAAM;AACb,QAAI,IAAI,UAAU,QAAQ,KAAK,CAAC;AAChC,QAAI,MAAM;AACV,QAAI,MAAM,IAAI;AACV,YAAM,OAAO;AAAA,IACjB,OACK;AACD,YAAM,IAAI;AAAA,IACd;AACA,QAAI,SAAS,UAAU,MAAM,GAAG,IAAI,GAAG;AACvC,cAAU,OAAO,GAAG,iDAAiD;AACrE,QAAI,YAAY,UAAa,OAAO,GAAG;AACnC,UAAI,WAAW,QAAQ,MAAM,MAAM,IAAI;AACnC,mBAAW,KAAK,MAAM;AAAA,MAC1B;AAAA,IACJ,WACS,QAAQ,GAAG;AAChB,gBAAU,EAAE,KAAK,QAAQ,OAAO,GAAG;AACnC,UAAI,SAAS,KAAK,SAAUC,IAAG;AAAE,eAAOA,GAAE,SAAS,YAAY,QAAQ,YAAY,SAAS,SAAS,QAAQ;AAAA,MAAM,CAAC,MAAM,QAAW;AACjI,iBAAS,KAAK,OAAO;AAAA,MACzB;AAAA,IACJ,YACU,YAAY,QAAQ,YAAY,SAAS,SAAS,QAAQ,WAAW,IAAI;AAC/E,cAAQ,QAAQ;AAAA,IACpB,OACK;AACD,gBAAU,YAAY,QAAW,gCAAgC;AACjE,cAAQ,SAAS,MAAM;AAAA,IAC3B;AACA,SAAK,MAAM;AAAA,EACf;AACA,SAAO,EAAE,YAAwB,SAAmB;AACxD;;;AChCO,SAAS,cAAc,kBAAkB,kBAAkB,SAAS,uBAAuB,YAAY,kBAAkB;AAC5H,MAAI;AACJ,MAAI,UAAU,QAAQ;AACtB,MAAI;AACJ,MAAI,YAAY,UAAU;AACtB,QAAI,cAAc,MAAM,KAAK,gBAAgB,GAAG,kBAAkB,gBAAgB;AAAA,EACtF,OACK;AACD,QAAI,eAAe,MAAM,KAAK,gBAAgB,GAAG,kBAAkB,gBAAgB;AAAA,EACvF;AACA,MAAI,KAAK,MAAM;AACX,QAAI;AAAA,MACA,QAAQ,iBAAiB;AAAA,MACzB,WAAW;AAAA,IACf;AAAA,EACJ;AACA,MAAI,cAAc,EAAE;AACpB,MAAI,kBAAkB,WAAW,WAAW;AAM5C,MAAI,SAAS,EAAE,QAAQ,MAAM,YAAY,YAAY;AACrD,MAAI;AACJ,MAAI;AACJ,MAAI,EAAE,WAAW;AACb,iBAAa,2BAA2B,EAAE,SAAS;AACnD,eAAW,WAAW;AAAA,EAC1B,OACK;AACD,eAAW,CAAC;AAAA,EAChB;AACA,MAAI,oBAAoB,CAAC;AACzB,MAAI,UAAU,SAAUC,MAAK;AAEzB,QAAI,iBAAiB,KAAK,oBAAoB,QAAQ,oBAAoB,SAAS,SAAS,gBAAgBA,IAAG,OAAO,QAAQ,OAAO,SAAS,KAAK,CAAC;AACpJ,cAAU,MAAM,QAAQ,aAAa,GAAG,qBAAqB,OAAOA,MAAK,mBAAmB,CAAC;AAC7F,QAAI,QAAQ,cAAc,CAAC;AAC3B,cAAU,UAAU,UAAa,OAAO,UAAU,UAAU,qCAAqC;AACjG,QAAI,mBAAmB;AACvB,QAAI,QAAQ,SAAS,KAAK,SAAU,GAAG;AAAE,aAAO,EAAE,QAAQA;AAAA,IAAK,CAAC;AAChE,QAAI,OAAO;AACP,UAAI,iBAAiB,MAAM;AAC3B,UAAI,mBAAmB,IAAI;AACvB,YAAI,cAAc,QAAQ,cAAc,IAAI,IAAI;AAC5C,kBAAQ;AACR,6BAAmB;AAAA,YACf,KAAKA;AAAA,YACL;AAAA,UACJ;AAAA,QACJ;AAAA,MACJ,WACS,cAAc,QAAQ,MAAM,IAAI,IAAI;AACzC,gBAAQ;AACR,2BAAmB;AAAA,UACf,KAAKA;AAAA,UACL;AAAA,QACJ;AAAA,MACJ;AAAA,IACJ;AACA,QAAI,eAAe,QAAQA,IAAG;AAC9B,cAAU,gBAAgB,QAAQ,OAAO,iBAAiB,UAAU,4CAA4C;AAChH,QAAI,OAAO,iBAAiB,UAAU;AAClC,UAAI,OAAOA,KAAI,YAAY;AAC3B,qBAAe,mBAAmB,MAAM,YAAY;AACpD,UAAI,iBAAiB,IAAI;AACrB,uBAAe;AAAA,MACnB;AAAA,IACJ;AACA,QAAI,iBAAiB,SAAS,cAAc,QAAQ,YAAY,IAAI,IAAI;AACpE,cAAQ;AACR,yBAAmB;AAAA,IACvB;AACA,QAAI,kBAAkB;AAClB,wBAAkB,KAAK,gBAAgB;AAAA,IAC3C;AACA,WAAOA,IAAG,IAAI;AAAA,EAClB;AACA,WAAS,KAAK,GAAG,0BAA0B,uBAAuB,KAAK,wBAAwB,QAAQ,MAAM;AACzG,QAAI,MAAM,wBAAwB,EAAE;AACpC,YAAQ,GAAG;AAAA,EACf;AACA,MAAI,sBAAsB,CAAC;AAC3B,MAAI,kBAAkB,SAAS,GAAG;AAC9B,0BAAsB,CAAC;AACvB,kBAAc,sCAAsC,aAAa,qBAAqB,iBAAiB;AAAA,EAC3G;AACA,SAAO,SAAS;AAChB,SAAO;AACX;;;AC5FO,SAAS,uBAAuB,kBAAkB,kBAAkB;AACvE,MAAI,SAAS,CAAC;AACd,WAAS,KAAK,GAAG,qBAAqB,kBAAkB,KAAK,mBAAmB,QAAQ,MAAM;AAC1F,QAAI,SAAS,mBAAmB,EAAE;AAClC,QAAI,oBAAoB,OAAO,QAAQ,kCAAkC,EAAE;AAC3E,QAAI,kBAAkB,oBAAoB,kBAAkB,iBAAiB;AAC7E,QAAI,iBAAiB;AACjB,aAAO,KAAK,eAAe;AAAA,IAC/B;AAAA,EACJ;AACA,SAAO;AACX;;;AChBO,SAAS,MAAM,kBAAkB,kBAAkB,eAAe,MAAM;AAC3E,SAAO,cAAc,kBAAkB,uBAAuB,gBAAgB,GAAG;AAAA,IAC7E,gBAAgB,SAAS,QAAQ,SAAS,SAAS,SAAS,KAAK,cAAc;AAAA,EACnF,GAAG,CAAC,GAAG,CAAC,GAAG,WAAY;AAAE,WAAO;AAAA,EAAe,CAAC,EAAE;AACtD;", "names": ["data", "k", "key"]}
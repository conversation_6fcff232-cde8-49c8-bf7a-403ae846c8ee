<script lang="ts">
  import type { Locale } from "$lib";
  import { fetchWithAuth, preventDefault } from "$lib";
  import { Modal } from "$lib/components";

  interface Props {
    locale: Locale;
    show: boolean;
    onHide: () => void;
    communeId: string;
    onMemberAdded: () => void;
  }

  const i18n = {
    en: {
      addMember: "Add Member",
      memberAddedSuccess: "Member added successfully!",
      email: "Email",
      enterEmail: "Enter user email",
      cancel: "Cancel",
      add: "Add",
      adding: "Adding...",
      provideEmail: "Please provide an email address.",
      failedToAdd: "Failed to add member",
      unexpectedError: "An unexpected error occurred. Please try again.",
    },
    ru: {
      addMember: "Добавить участника",
      memberAddedSuccess: "Участник успешно добавлен!",
      email: "Email",
      enterEmail: "Введите email пользователя",
      cancel: "Отмена",
      add: "Добавить",
      adding: "Добавление...",
      provideEmail: "Пожалуйста, укажите email адрес.",
      failedToAdd: "Не удалось добавить участника",
      unexpectedError: "Произошла непредвиденная ошибка. Пожалуйста, попробуйте снова.",
    },
  };

  const { show, onHide, communeId, locale, onMemberAdded }: Props = $props();

  const t = $derived(i18n[locale]);

  let userId = $state("");
  let isSubmitting = $state(false);
  let error = $state<string | null>(null);
  let success = $state(false);

  async function handleSubmit() {
    if (!userId.trim()) {
      error = t.provideEmail;
      return;
    }

    isSubmitting = true;
    error = null;
    success = false;

    try {
      const response = await fetchWithAuth(`/api/commune/${communeId}/member`, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          actorType: "user",
          actorId: userId,
        }),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.message || t.failedToAdd);
      }

      success = true;
      userId = "";
      onMemberAdded();

      // Close modal after a short delay
      setTimeout(() => {
        onHide();
      }, 1500);
    } catch (err) {
      error = err instanceof Error ? err.message : t.unexpectedError;
      console.error(err);
    } finally {
      isSubmitting = false;
    }
  }

  function handleClose() {
    userId = "";
    error = null;
    success = false;
    onHide();
  }
</script>

<Modal
  {show}
  title={t.addMember}
  onClose={handleClose}
  onSubmit={handleSubmit}
  submitText={isSubmitting ? t.adding : t.add}
  cancelText={t.cancel}
  submitDisabled={!userId.trim() || isSubmitting || success}
  cancelDisabled={isSubmitting}
  {isSubmitting}
>
  {#if error}
    <div class="alert alert-danger mb-3">
      {error}
    </div>
  {/if}

  {#if success}
    <div class="alert alert-success mb-3">
      {t.memberAddedSuccess}
    </div>
  {/if}

  <form onsubmit={preventDefault(handleSubmit)}>
    <div class="mb-3">
      <label for="userId" class="form-label">{t.email}</label>
      <input
        type="text"
        class="form-control"
        id="userId"
        placeholder={t.enterEmail}
        bind:value={userId}
        disabled={isSubmitting || success}
        required
      />
    </div>
  </form>
</Modal>

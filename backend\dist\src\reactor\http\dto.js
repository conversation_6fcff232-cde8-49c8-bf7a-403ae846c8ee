"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
Object.defineProperty(exports, "__esModule", { value: true });
exports.UpdateLens = exports.CreateLens = exports.DeleteComment = exports.AnonimifyComment = exports.CommentRating = exports.UpdateCommentRating = exports.UpdateComment = exports.CreateComment = exports.GetCommentsResponse = exports.Comment = exports.GetCommentsEntityType = exports.DeletePost = exports.PostUsefulness = exports.UpdatePostUsefulness = exports.PostRating = exports.UpdatePostRating = exports.UpdatePost = exports.CreatePost = exports.GetPostsResponse = exports.Post = exports.GetPosts = exports.EntityType = void 0;
const prisma = __importStar(require("@prisma/client"));
const zod_1 = require("../../zod");
const postUsefulness = zod_1.z.number().int().min(0).max(10);
exports.EntityType = zod_1.z.enum(["post", "comment"]);
exports.GetPosts = zod_1.z.object({
    pagination: zod_1.z.object({
        page: zod_1.ZodHelper.pagination.page,
        size: zod_1.ZodHelper.pagination.size,
    }),
});
exports.Post = zod_1.z.object({
    id: zod_1.ZodHelper.Uuid,
    author: zod_1.z.object({
        id: zod_1.ZodHelper.Uuid,
        name: zod_1.ZodHelper.Localizations,
        avatar: zod_1.z.string().nullable(),
    }),
    rating: zod_1.z.object({
        likes: zod_1.z.number().int().nonnegative(),
        dislikes: zod_1.z.number().int().nonnegative(),
        status: zod_1.z.nativeEnum(prisma.ReactorRatingType).nullable(),
    }),
    usefulness: zod_1.z.object({
        value: postUsefulness.nullable(),
        count: zod_1.z.number().int().nonnegative(),
        totalValue: zod_1.z.number().min(0).max(10).nullable(),
    }),
    title: zod_1.ZodHelper.Localizations,
    body: zod_1.ZodHelper.Localizations,
    tags: zod_1.z.array(zod_1.ZodHelper.Uuid),
    createdAt: zod_1.z.date(),
    updatedAt: zod_1.z.date(),
});
exports.GetPostsResponse = zod_1.z.object({
    items: zod_1.z.array(exports.Post),
    total: zod_1.z.number().int().nonnegative(),
});
exports.CreatePost = zod_1.z.object({
    title: zod_1.ZodHelper.Localizations,
    body: zod_1.ZodHelper.Localizations,
    tags: zod_1.z.array(zod_1.ZodHelper.Uuid),
});
exports.UpdatePost = zod_1.z
    .object({
    title: zod_1.ZodHelper.Localizations,
    body: zod_1.ZodHelper.Localizations,
    tags: zod_1.z.array(zod_1.ZodHelper.Uuid),
})
    .partial();
exports.UpdatePostRating = zod_1.z.object({
    type: zod_1.z.nativeEnum(prisma.ReactorRatingType),
});
exports.PostRating = zod_1.z.object({
    likes: zod_1.z.number().int().nonnegative(),
    dislikes: zod_1.z.number().int().nonnegative(),
    status: zod_1.z.nativeEnum(prisma.ReactorRatingType).nullable(),
});
exports.UpdatePostUsefulness = zod_1.z.object({
    value: postUsefulness.nullable(),
});
exports.PostUsefulness = zod_1.z.object({
    value: postUsefulness.nullable(),
    count: zod_1.z.number().int().nonnegative(),
    totalValue: zod_1.z.number().min(0).max(10).nullable(),
});
exports.DeletePost = zod_1.z.object({
    reason: zod_1.z.string().nullable(),
});
exports.GetCommentsEntityType = zod_1.z.enum(["post"]);
exports.Comment = zod_1.z.object({
    id: zod_1.ZodHelper.Uuid,
    path: zod_1.z.string().nonempty(),
    author: zod_1.z
        .object({
        id: zod_1.ZodHelper.Uuid,
        name: zod_1.ZodHelper.Localizations,
        avatar: zod_1.z.string().nullable(),
    })
        .nullable(),
    isAnonymous: zod_1.z.boolean(),
    anonimityReason: zod_1.z.string().nullable(),
    rating: zod_1.z.object({
        likes: zod_1.z.number().int().nonnegative(),
        dislikes: zod_1.z.number().int().nonnegative(),
        status: zod_1.z.nativeEnum(prisma.ReactorRatingType).nullable(),
    }),
    body: zod_1.ZodHelper.Localizations.nullable(),
    childrenCount: zod_1.z.number().int().nonnegative(),
    deleteReason: zod_1.z.string().nonempty().nullable(),
    createdAt: zod_1.z.date(),
    updatedAt: zod_1.z.date(),
    deletedAt: zod_1.z.date().nullable(),
});
exports.GetCommentsResponse = zod_1.z.object({
    items: zod_1.z.array(exports.Comment),
    total: zod_1.z.number().int().nonnegative(),
});
exports.CreateComment = zod_1.z.object({
    entityType: exports.EntityType,
    entityId: zod_1.ZodHelper.Uuid,
    body: zod_1.ZodHelper.Localizations,
});
exports.UpdateComment = zod_1.z
    .object({
    body: zod_1.ZodHelper.Localizations,
})
    .partial();
exports.UpdateCommentRating = zod_1.z.object({
    type: zod_1.z.nativeEnum(prisma.ReactorRatingType),
});
exports.CommentRating = zod_1.z.object({
    likes: zod_1.z.number().int().nonnegative(),
    dislikes: zod_1.z.number().int().nonnegative(),
    status: zod_1.z.nativeEnum(prisma.ReactorRatingType).nullable(),
});
exports.AnonimifyComment = zod_1.z.object({
    reason: zod_1.z.string().nullable(),
});
exports.DeleteComment = zod_1.z.object({
    reason: zod_1.z.string().nullable(),
});
exports.CreateLens = zod_1.z.object({
    name: zod_1.z.string().nonempty(),
    code: zod_1.z.string().nonempty(),
});
exports.UpdateLens = zod_1.z
    .object({
    name: zod_1.z.string().nonempty(),
    code: zod_1.z.string().nonempty(),
})
    .partial();
//# sourceMappingURL=dto.js.map
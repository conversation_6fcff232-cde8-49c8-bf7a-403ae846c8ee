import type { Locale } from "./dto";
import type { Localization } from "./types";

import { match } from "@formatjs/intl-localematcher";

const DEFAULT_LOCALE = "en";

export function findLocalization(
  userLocales: readonly string[],
  localizations: Localization[],
): string | null {
  const locales = localizations.map((localization) => localization.locale);

  try {
    const matchedLocale = match(userLocales, locales, DEFAULT_LOCALE);

    const matchedLocalization = localizations.find(
      (localization) => localization.locale === matchedLocale,
    );

    return matchedLocalization?.value ?? localizations[0]?.value ?? null;
  } catch (error) {
    console.error("Error during locale negotiation:", error);

    return null;
  }
}

export function findLocalizationForUserLocales(
  localizations: Localization[],
  currentLocale: Locale | null = null,
): string | null {
  if (typeof window === "undefined") {
    return localizations[0]?.value ?? null;
  }

  const locales = [...window.navigator.languages];

  if (currentLocale) {
    locales.unshift(currentLocale);
  }

  return findLocalization(locales, localizations);
}

// reactor-post
model ReactorPost {
    @@map("reactor_posts")

    id String @id @db.Uuid @default(uuid(7))

    tags Tag[] @relation("reactor_post_tags")

    authorId String @db.Uuid @map("author_id")
    author   User   @relation("user_reactor_posts", fields: [authorId], references: [id])

    hubId String?     @map("hub_id") @db.Uuid
    hub   ReactorHub? @relation("reactor_hub_posts", fields: [hubId], references: [id])

    groupId String?       @map("group_id") @db.Uuid
    group   ReactorGroup? @relation("reactor_group_posts", fields: [groupId], references: [id])

    title Localization[] @relation("reactor_post_title")
    body  Localization[] @relation("reactor_post_body")

    isAnonymous     Boolean @map("is_anonymous")     @default(false)
    anonimityReason String? @map("anonimity_reason")

    deleteReason String? @map("delete_reason")

    createdAt DateTime  @map("created_at") @db.Timestamptz(3) @default(now())
    updatedAt DateTime  @map("updated_at") @db.Timestamptz(3) @updatedAt
    deletedAt DateTime? @map("deleted_at") @db.Timestamptz(3)

    @@index([authorId])
    @@index([hubId])
    @@index([groupId])
}

model ReactorPostInternalNumber {
    @@map("reactor_post_internal_numbers")

    id String @id @db.Uuid @default(uuid(7))

    postId String @map("post_id") @db.Uuid @unique

    internalNumber Int @map("internal_number")

    createdAt DateTime  @map("created_at") @db.Timestamptz(3) @default(now())
    updatedAt DateTime  @map("updated_at") @db.Timestamptz(3) @updatedAt
}

// reactor-comment
model ReactorComment {
    @@map("reactor_comments")

    id String @id @db.Uuid @default(uuid(7))

    authorId String @db.Uuid @map("author_id")
    author   User   @relation("user_reactor_comments", fields: [authorId], references: [id])

    postId String @map("post_id") @db.Uuid

    path           String
    internalNumber Int    @map("internal_number")

    isAnonymous     Boolean @map("is_anonymous")     @default(false)
    anonimityReason String? @map("anonimity_reason")

    body Localization[] @relation("reactor_comment_body")

    deleteReason String? @map("delete_reason")

    createdAt DateTime  @map("created_at") @db.Timestamptz(3) @default(now())
    updatedAt DateTime  @map("updated_at") @db.Timestamptz(3) @updatedAt
    deletedAt DateTime? @map("deleted_at") @db.Timestamptz(3)

    @@unique([postId, internalNumber])
    @@unique([postId, path])

    @@index([postId, path])
}

// reactor-entity-type
enum ReactorEntityType {
    @@map("reactor_entity_type")

    post
    comment
}

enum ReactorRatingType {
    @@map("reactor_rating_type")

    like
    dislike
}

model ReactorRating {
    @@map("reactor_ratings")

    id String @id @db.Uuid @default(uuid(7))

    userId String @map("user_id") @db.Uuid
    user   User   @relation("user_reactor_ratings", fields: [userId], references: [id])

    entityType ReactorEntityType @map("entity_type")
    entityId   String            @map("entity_id")   @db.Uuid

    type ReactorRatingType

    createdAt DateTime @map("created_at") @db.Timestamptz(3) @default(now())
    updatedAt DateTime @map("updated_at") @db.Timestamptz(3) @updatedAt

    @@unique([userId, entityType, entityId])

    @@index([userId])
    @@index([entityType, entityId])
}

model ReactorUsefulness {
    @@map("reactor_usefulnesses")

    id String @id @db.Uuid @default(uuid(7))

    userId String @map("user_id") @db.Uuid
    user   User   @relation("user_reactor_usefulnesses", fields: [userId], references: [id])

    entityType ReactorEntityType @map("entity_type")
    entityId   String            @map("entity_id")   @db.Uuid

    value Int

    createdAt DateTime @map("created_at") @db.Timestamptz(3) @default(now())
    updatedAt DateTime @map("updated_at") @db.Timestamptz(3) @updatedAt

    @@unique([userId, entityType, entityId])

    @@index([userId])
    @@index([entityType, entityId])
}

// reactor-hub
model ReactorHub {
    @@map("reactor_hubs")

    id String @id @db.Uuid @default(uuid(7))

    headUserId String @map("head_user_id") @db.Uuid
    headUser   User   @relation("user_reactor_hubs", fields: [headUserId], references: [id])

    name        Localization[] @relation("reactor_hub_name")
    description Localization[] @relation("reactor_hub_description")

    groups ReactorGroup[] @relation("reactor_hub_groups")
    posts ReactorPost[]  @relation("reactor_hub_posts")

    createdAt DateTime  @map("created_at") @db.Timestamptz(3) @default(now())
    updatedAt DateTime  @map("updated_at") @db.Timestamptz(3) @updatedAt
    deletedAt DateTime? @map("deleted_at") @db.Timestamptz(3)
}

// reactor-group
model ReactorGroup {
    @@map("reactor_groups")

    id String @id @db.Uuid @default(uuid(7))

    hubId String     @map("hub_id") @db.Uuid
    hub   ReactorHub @relation("reactor_hub_groups", fields: [hubId], references: [id])

    headUserId String @map("head_user_id") @db.Uuid
    headUser   User   @relation("user_reactor_groups", fields: [headUserId], references: [id])

    name        Localization[] @relation("reactor_group_name")
    description Localization[] @relation("reactor_group_description")

    posts ReactorPost[] @relation("reactor_group_posts")

    createdAt DateTime  @map("created_at") @db.Timestamptz(3) @default(now())
    updatedAt DateTime  @map("updated_at") @db.Timestamptz(3) @updatedAt
    deletedAt DateTime? @map("deleted_at") @db.Timestamptz(3)

    @@index([hubId])
}

// reactor-lens
model ReactorLens {
    @@map("reactor_lenses")

    id String @id @db.Uuid @default(uuid(7))

    userId String @map("user_id") @db.Uuid
    user   User   @relation("user_reactor_lenses", fields: [userId], references: [id])

    name String

    code String
    sql  String

    createdAt DateTime  @map("created_at") @db.Timestamptz(3) @default(now())
    updatedAt DateTime  @map("updated_at") @db.Timestamptz(3) @updatedAt
    deletedAt DateTime? @map("deleted_at") @db.Timestamptz(3)
}

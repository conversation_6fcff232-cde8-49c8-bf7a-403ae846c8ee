{"version": 3, "sources": ["../../../../node_modules/svelte/src/constants.js", "../../../../node_modules/svelte/src/internal/shared/errors.js", "../../../../node_modules/svelte/src/internal/shared/utils.js", "../../../../node_modules/svelte/src/internal/client/constants.js", "../../../../node_modules/svelte/src/internal/client/errors.js", "../../../../node_modules/svelte/src/internal/client/reactivity/equality.js", "../../../../node_modules/svelte/src/internal/shared/warnings.js", "../../../../node_modules/svelte/src/internal/shared/clone.js", "../../../../node_modules/svelte/src/internal/client/dev/tracing.js", "../../../../node_modules/svelte/src/internal/client/context.js", "../../../../node_modules/svelte/src/internal/client/reactivity/deriveds.js", "../../../../node_modules/svelte/src/internal/client/reactivity/sources.js", "../../../../node_modules/svelte/src/internal/client/reactivity/effects.js", "../../../../node_modules/svelte/src/internal/client/dom/task.js", "../../../../node_modules/svelte/src/internal/client/error-handling.js", "../../../../node_modules/svelte/src/internal/client/runtime.js", "../../../../node_modules/svelte/src/internal/client/proxy.js", "../../../../node_modules/svelte/src/internal/client/dev/equality.js", "../../../../node_modules/svelte/src/internal/client/dom/operations.js", "../../../../node_modules/svelte/src/internal/client/dom/hydration.js", "../../../../node_modules/svelte/src/internal/client/dom/elements/misc.js", "../../../../node_modules/svelte/src/internal/client/dom/elements/bindings/shared.js", "../../../../node_modules/svelte/src/internal/client/dom/elements/events.js"], "sourcesContent": ["export const EACH_ITEM_REACTIVE = 1;\nexport const EACH_INDEX_REACTIVE = 1 << 1;\n/** See EachBlock interface metadata.is_controlled for an explanation what this is */\nexport const EACH_IS_CONTROLLED = 1 << 2;\nexport const EACH_IS_ANIMATED = 1 << 3;\nexport const EACH_ITEM_IMMUTABLE = 1 << 4;\n\nexport const PROPS_IS_IMMUTABLE = 1;\nexport const PROPS_IS_RUNES = 1 << 1;\nexport const PROPS_IS_UPDATED = 1 << 2;\nexport const PROPS_IS_BINDABLE = 1 << 3;\nexport const PROPS_IS_LAZY_INITIAL = 1 << 4;\n\nexport const TRANSITION_IN = 1;\nexport const TRANSITION_OUT = 1 << 1;\nexport const TRANSITION_GLOBAL = 1 << 2;\n\nexport const TEMPLATE_FRAGMENT = 1;\nexport const TEMPLATE_USE_IMPORT_NODE = 1 << 1;\nexport const TEMPLATE_USE_SVG = 1 << 2;\nexport const TEMPLATE_USE_MATHML = 1 << 3;\n\nexport const HYDRATION_START = '[';\n/** used to indicate that an `{:else}...` block was rendered */\nexport const HYDRATION_START_ELSE = '[!';\nexport const HYDRATION_END = ']';\nexport const HYDRATION_ERROR = {};\n\nexport const ELEMENT_IS_NAMESPACED = 1;\nexport const ELEMENT_PRESERVE_ATTRIBUTE_CASE = 1 << 1;\n\nexport const UNINITIALIZED = Symbol();\n\n// Dev-time component properties\nexport const FILENAME = Symbol('filename');\nexport const HMR = Symbol('hmr');\n\nexport const NAMESPACE_HTML = 'http://www.w3.org/1999/xhtml';\nexport const NAMESPACE_SVG = 'http://www.w3.org/2000/svg';\nexport const NAMESPACE_MATHML = 'http://www.w3.org/1998/Math/MathML';\n\n// we use a list of ignorable runtime warnings because not every runtime warning\n// can be ignored and we want to keep the validation for svelte-ignore in place\nexport const IGNORABLE_RUNTIME_WARNINGS = /** @type {const} */ ([\n\t'state_snapshot_uncloneable',\n\t'binding_property_non_reactive',\n\t'hydration_attribute_changed',\n\t'hydration_html_changed',\n\t'ownership_invalid_binding',\n\t'ownership_invalid_mutation'\n]);\n\n/**\n * Whitespace inside one of these elements will not result in\n * a whitespace node being created in any circumstances. (This\n * list is almost certainly very incomplete)\n * TODO this is currently unused\n */\nexport const ELEMENTS_WITHOUT_TEXT = ['audio', 'datalist', 'dl', 'optgroup', 'select', 'video'];\n\nexport const ATTACHMENT_KEY = '@attach';\n", "/* This file is generated by scripts/process-messages/index.js. Do not edit! */\n\nimport { DEV } from 'esm-env';\n\n/**\n * Cannot use `{@render children(...)}` if the parent component uses `let:` directives. Consider using a named snippet instead\n * @returns {never}\n */\nexport function invalid_default_snippet() {\n\tif (DEV) {\n\t\tconst error = new Error(`invalid_default_snippet\\nCannot use \\`{@render children(...)}\\` if the parent component uses \\`let:\\` directives. Consider using a named snippet instead\\nhttps://svelte.dev/e/invalid_default_snippet`);\n\n\t\terror.name = 'Svelte error';\n\n\t\tthrow error;\n\t} else {\n\t\tthrow new Error(`https://svelte.dev/e/invalid_default_snippet`);\n\t}\n}\n\n/**\n * A snippet function was passed invalid arguments. Snippets should only be instantiated via `{@render ...}`\n * @returns {never}\n */\nexport function invalid_snippet_arguments() {\n\tif (DEV) {\n\t\tconst error = new Error(`invalid_snippet_arguments\\nA snippet function was passed invalid arguments. Snippets should only be instantiated via \\`{@render ...}\\`\\nhttps://svelte.dev/e/invalid_snippet_arguments`);\n\n\t\terror.name = 'Svelte error';\n\n\t\tthrow error;\n\t} else {\n\t\tthrow new Error(`https://svelte.dev/e/invalid_snippet_arguments`);\n\t}\n}\n\n/**\n * `%name%(...)` can only be used during component initialisation\n * @param {string} name\n * @returns {never}\n */\nexport function lifecycle_outside_component(name) {\n\tif (DEV) {\n\t\tconst error = new Error(`lifecycle_outside_component\\n\\`${name}(...)\\` can only be used during component initialisation\\nhttps://svelte.dev/e/lifecycle_outside_component`);\n\n\t\terror.name = 'Svelte error';\n\n\t\tthrow error;\n\t} else {\n\t\tthrow new Error(`https://svelte.dev/e/lifecycle_outside_component`);\n\t}\n}\n\n/**\n * Attempted to render a snippet without a `{@render}` block. This would cause the snippet code to be stringified instead of its content being rendered to the DOM. To fix this, change `{snippet}` to `{@render snippet()}`.\n * @returns {never}\n */\nexport function snippet_without_render_tag() {\n\tif (DEV) {\n\t\tconst error = new Error(`snippet_without_render_tag\\nAttempted to render a snippet without a \\`{@render}\\` block. This would cause the snippet code to be stringified instead of its content being rendered to the DOM. To fix this, change \\`{snippet}\\` to \\`{@render snippet()}\\`.\\nhttps://svelte.dev/e/snippet_without_render_tag`);\n\n\t\terror.name = 'Svelte error';\n\n\t\tthrow error;\n\t} else {\n\t\tthrow new Error(`https://svelte.dev/e/snippet_without_render_tag`);\n\t}\n}\n\n/**\n * `%name%` is not a store with a `subscribe` method\n * @param {string} name\n * @returns {never}\n */\nexport function store_invalid_shape(name) {\n\tif (DEV) {\n\t\tconst error = new Error(`store_invalid_shape\\n\\`${name}\\` is not a store with a \\`subscribe\\` method\\nhttps://svelte.dev/e/store_invalid_shape`);\n\n\t\terror.name = 'Svelte error';\n\n\t\tthrow error;\n\t} else {\n\t\tthrow new Error(`https://svelte.dev/e/store_invalid_shape`);\n\t}\n}\n\n/**\n * The `this` prop on `<svelte:element>` must be a string, if defined\n * @returns {never}\n */\nexport function svelte_element_invalid_this_value() {\n\tif (DEV) {\n\t\tconst error = new Error(`svelte_element_invalid_this_value\\nThe \\`this\\` prop on \\`<svelte:element>\\` must be a string, if defined\\nhttps://svelte.dev/e/svelte_element_invalid_this_value`);\n\n\t\terror.name = 'Svelte error';\n\n\t\tthrow error;\n\t} else {\n\t\tthrow new Error(`https://svelte.dev/e/svelte_element_invalid_this_value`);\n\t}\n}", "// Store the references to globals in case someone tries to monkey patch these, causing the below\n// to de-opt (this occurs often when using popular extensions).\nexport var is_array = Array.isArray;\nexport var index_of = Array.prototype.indexOf;\nexport var array_from = Array.from;\nexport var object_keys = Object.keys;\nexport var define_property = Object.defineProperty;\nexport var get_descriptor = Object.getOwnPropertyDescriptor;\nexport var get_descriptors = Object.getOwnPropertyDescriptors;\nexport var object_prototype = Object.prototype;\nexport var array_prototype = Array.prototype;\nexport var get_prototype_of = Object.getPrototypeOf;\nexport var is_extensible = Object.isExtensible;\n\n/**\n * @param {any} thing\n * @returns {thing is Function}\n */\nexport function is_function(thing) {\n\treturn typeof thing === 'function';\n}\n\nexport const noop = () => {};\n\n// Adapted from https://github.com/then/is-promise/blob/master/index.js\n// Distributed under MIT License https://github.com/then/is-promise/blob/master/LICENSE\n\n/**\n * @template [T=any]\n * @param {any} value\n * @returns {value is PromiseLike<T>}\n */\nexport function is_promise(value) {\n\treturn typeof value?.then === 'function';\n}\n\n/** @param {Function} fn */\nexport function run(fn) {\n\treturn fn();\n}\n\n/** @param {Array<() => void>} arr */\nexport function run_all(arr) {\n\tfor (var i = 0; i < arr.length; i++) {\n\t\tarr[i]();\n\t}\n}\n\n/**\n * TODO replace with Promise.withResolvers once supported widely enough\n * @template T\n */\nexport function deferred() {\n\t/** @type {(value: T) => void} */\n\tvar resolve;\n\n\t/** @type {(reason: any) => void} */\n\tvar reject;\n\n\t/** @type {Promise<T>} */\n\tvar promise = new Promise((res, rej) => {\n\t\tresolve = res;\n\t\treject = rej;\n\t});\n\n\t// @ts-expect-error\n\treturn { promise, resolve, reject };\n}\n\n/**\n * @template V\n * @param {V} value\n * @param {V | (() => V)} fallback\n * @param {boolean} [lazy]\n * @returns {V}\n */\nexport function fallback(value, fallback, lazy = false) {\n\treturn value === undefined\n\t\t? lazy\n\t\t\t? /** @type {() => V} */ (fallback)()\n\t\t\t: /** @type {V} */ (fallback)\n\t\t: value;\n}\n\n/**\n * When encountering a situation like `let [a, b, c] = $derived(blah())`,\n * we need to stash an intermediate value that `a`, `b`, and `c` derive\n * from, in case it's an iterable\n * @template T\n * @param {ArrayLike<T> | Iterable<T>} value\n * @param {number} [n]\n * @returns {Array<T>}\n */\nexport function to_array(value, n) {\n\t// return arrays unchanged\n\tif (Array.isArray(value)) {\n\t\treturn value;\n\t}\n\n\t// if value is not iterable, or `n` is unspecified (indicates a rest\n\t// element, which means we're not concerned about unbounded iterables)\n\t// convert to an array with `Array.from`\n\tif (n === undefined || !(Symbol.iterator in value)) {\n\t\treturn Array.from(value);\n\t}\n\n\t// otherwise, populate an array with `n` values\n\n\t/** @type {T[]} */\n\tconst array = [];\n\n\tfor (const element of value) {\n\t\tarray.push(element);\n\t\tif (array.length === n) break;\n\t}\n\n\treturn array;\n}\n", "export const DERIVED = 1 << 1;\nexport const EFFECT = 1 << 2;\nexport const RENDER_EFFECT = 1 << 3;\nexport const BLOCK_EFFECT = 1 << 4;\nexport const BRANCH_EFFECT = 1 << 5;\nexport const ROOT_EFFECT = 1 << 6;\nexport const BOUNDARY_EFFECT = 1 << 7;\nexport const UNOWNED = 1 << 8;\nexport const DISCONNECTED = 1 << 9;\nexport const CLEAN = 1 << 10;\nexport const DIRTY = 1 << 11;\nexport const MAYBE_DIRTY = 1 << 12;\nexport const INERT = 1 << 13;\nexport const DESTROYED = 1 << 14;\nexport const EFFECT_RAN = 1 << 15;\n/** 'Transparent' effects do not create a transition boundary */\nexport const EFFECT_TRANSPARENT = 1 << 16;\nexport const INSPECT_EFFECT = 1 << 17;\nexport const HEAD_EFFECT = 1 << 18;\nexport const EFFECT_PRESERVED = 1 << 19;\nexport const EFFECT_IS_UPDATING = 1 << 20;\nexport const USER_EFFECT = 1 << 21;\n\nexport const STATE_SYMBOL = Symbol('$state');\nexport const LEGACY_PROPS = Symbol('legacy props');\nexport const LOADING_ATTR_SYMBOL = Symbol('');\nexport const PROXY_PATH_SYMBOL = Symbol('proxy path');\n\n/** allow users to ignore aborted signal errors if `reason.name === 'StaleReactionError` */\nexport const STALE_REACTION = new (class StaleReactionError extends Error {\n\tname = 'StaleReactionError';\n\tmessage = 'The reaction that called `getAbortSignal()` was re-run or destroyed';\n})();\n\nexport const ELEMENT_NODE = 1;\nexport const TEXT_NODE = 3;\nexport const COMMENT_NODE = 8;\nexport const DOCUMENT_FRAGMENT_NODE = 11;\n", "/* This file is generated by scripts/process-messages/index.js. Do not edit! */\n\nimport { DEV } from 'esm-env';\n\n/**\n * Using `bind:value` together with a checkbox input is not allowed. Use `bind:checked` instead\n * @returns {never}\n */\nexport function bind_invalid_checkbox_value() {\n\tif (DEV) {\n\t\tconst error = new Error(`bind_invalid_checkbox_value\\nUsing \\`bind:value\\` together with a checkbox input is not allowed. Use \\`bind:checked\\` instead\\nhttps://svelte.dev/e/bind_invalid_checkbox_value`);\n\n\t\terror.name = 'Svelte error';\n\n\t\tthrow error;\n\t} else {\n\t\tthrow new Error(`https://svelte.dev/e/bind_invalid_checkbox_value`);\n\t}\n}\n\n/**\n * Component %component% has an export named `%key%` that a consumer component is trying to access using `bind:%key%`, which is disallowed. Instead, use `bind:this` (e.g. `<%name% bind:this={component} />`) and then access the property on the bound component instance (e.g. `component.%key%`)\n * @param {string} component\n * @param {string} key\n * @param {string} name\n * @returns {never}\n */\nexport function bind_invalid_export(component, key, name) {\n\tif (DEV) {\n\t\tconst error = new Error(`bind_invalid_export\\nComponent ${component} has an export named \\`${key}\\` that a consumer component is trying to access using \\`bind:${key}\\`, which is disallowed. Instead, use \\`bind:this\\` (e.g. \\`<${name} bind:this={component} />\\`) and then access the property on the bound component instance (e.g. \\`component.${key}\\`)\\nhttps://svelte.dev/e/bind_invalid_export`);\n\n\t\terror.name = 'Svelte error';\n\n\t\tthrow error;\n\t} else {\n\t\tthrow new Error(`https://svelte.dev/e/bind_invalid_export`);\n\t}\n}\n\n/**\n * A component is attempting to bind to a non-bindable property `%key%` belonging to %component% (i.e. `<%name% bind:%key%={...}>`). To mark a property as bindable: `let { %key% = $bindable() } = $props()`\n * @param {string} key\n * @param {string} component\n * @param {string} name\n * @returns {never}\n */\nexport function bind_not_bindable(key, component, name) {\n\tif (DEV) {\n\t\tconst error = new Error(`bind_not_bindable\\nA component is attempting to bind to a non-bindable property \\`${key}\\` belonging to ${component} (i.e. \\`<${name} bind:${key}={...}>\\`). To mark a property as bindable: \\`let { ${key} = $bindable() } = $props()\\`\\nhttps://svelte.dev/e/bind_not_bindable`);\n\n\t\terror.name = 'Svelte error';\n\n\t\tthrow error;\n\t} else {\n\t\tthrow new Error(`https://svelte.dev/e/bind_not_bindable`);\n\t}\n}\n\n/**\n * Calling `%method%` on a component instance (of %component%) is no longer valid in Svelte 5\n * @param {string} method\n * @param {string} component\n * @returns {never}\n */\nexport function component_api_changed(method, component) {\n\tif (DEV) {\n\t\tconst error = new Error(`component_api_changed\\nCalling \\`${method}\\` on a component instance (of ${component}) is no longer valid in Svelte 5\\nhttps://svelte.dev/e/component_api_changed`);\n\n\t\terror.name = 'Svelte error';\n\n\t\tthrow error;\n\t} else {\n\t\tthrow new Error(`https://svelte.dev/e/component_api_changed`);\n\t}\n}\n\n/**\n * Attempted to instantiate %component% with `new %name%`, which is no longer valid in Svelte 5. If this component is not under your control, set the `compatibility.componentApi` compiler option to `4` to keep it working.\n * @param {string} component\n * @param {string} name\n * @returns {never}\n */\nexport function component_api_invalid_new(component, name) {\n\tif (DEV) {\n\t\tconst error = new Error(`component_api_invalid_new\\nAttempted to instantiate ${component} with \\`new ${name}\\`, which is no longer valid in Svelte 5. If this component is not under your control, set the \\`compatibility.componentApi\\` compiler option to \\`4\\` to keep it working.\\nhttps://svelte.dev/e/component_api_invalid_new`);\n\n\t\terror.name = 'Svelte error';\n\n\t\tthrow error;\n\t} else {\n\t\tthrow new Error(`https://svelte.dev/e/component_api_invalid_new`);\n\t}\n}\n\n/**\n * A derived value cannot reference itself recursively\n * @returns {never}\n */\nexport function derived_references_self() {\n\tif (DEV) {\n\t\tconst error = new Error(`derived_references_self\\nA derived value cannot reference itself recursively\\nhttps://svelte.dev/e/derived_references_self`);\n\n\t\terror.name = 'Svelte error';\n\n\t\tthrow error;\n\t} else {\n\t\tthrow new Error(`https://svelte.dev/e/derived_references_self`);\n\t}\n}\n\n/**\n * Keyed each block has duplicate key `%value%` at indexes %a% and %b%\n * @param {string} a\n * @param {string} b\n * @param {string | undefined | null} [value]\n * @returns {never}\n */\nexport function each_key_duplicate(a, b, value) {\n\tif (DEV) {\n\t\tconst error = new Error(`each_key_duplicate\\n${value\n\t\t\t? `Keyed each block has duplicate key \\`${value}\\` at indexes ${a} and ${b}`\n\t\t\t: `Keyed each block has duplicate key at indexes ${a} and ${b}`}\\nhttps://svelte.dev/e/each_key_duplicate`);\n\n\t\terror.name = 'Svelte error';\n\n\t\tthrow error;\n\t} else {\n\t\tthrow new Error(`https://svelte.dev/e/each_key_duplicate`);\n\t}\n}\n\n/**\n * `%rune%` cannot be used inside an effect cleanup function\n * @param {string} rune\n * @returns {never}\n */\nexport function effect_in_teardown(rune) {\n\tif (DEV) {\n\t\tconst error = new Error(`effect_in_teardown\\n\\`${rune}\\` cannot be used inside an effect cleanup function\\nhttps://svelte.dev/e/effect_in_teardown`);\n\n\t\terror.name = 'Svelte error';\n\n\t\tthrow error;\n\t} else {\n\t\tthrow new Error(`https://svelte.dev/e/effect_in_teardown`);\n\t}\n}\n\n/**\n * Effect cannot be created inside a `$derived` value that was not itself created inside an effect\n * @returns {never}\n */\nexport function effect_in_unowned_derived() {\n\tif (DEV) {\n\t\tconst error = new Error(`effect_in_unowned_derived\\nEffect cannot be created inside a \\`$derived\\` value that was not itself created inside an effect\\nhttps://svelte.dev/e/effect_in_unowned_derived`);\n\n\t\terror.name = 'Svelte error';\n\n\t\tthrow error;\n\t} else {\n\t\tthrow new Error(`https://svelte.dev/e/effect_in_unowned_derived`);\n\t}\n}\n\n/**\n * `%rune%` can only be used inside an effect (e.g. during component initialisation)\n * @param {string} rune\n * @returns {never}\n */\nexport function effect_orphan(rune) {\n\tif (DEV) {\n\t\tconst error = new Error(`effect_orphan\\n\\`${rune}\\` can only be used inside an effect (e.g. during component initialisation)\\nhttps://svelte.dev/e/effect_orphan`);\n\n\t\terror.name = 'Svelte error';\n\n\t\tthrow error;\n\t} else {\n\t\tthrow new Error(`https://svelte.dev/e/effect_orphan`);\n\t}\n}\n\n/**\n * Maximum update depth exceeded. This can happen when a reactive block or effect repeatedly sets a new value. Svelte limits the number of nested updates to prevent infinite loops\n * @returns {never}\n */\nexport function effect_update_depth_exceeded() {\n\tif (DEV) {\n\t\tconst error = new Error(`effect_update_depth_exceeded\\nMaximum update depth exceeded. This can happen when a reactive block or effect repeatedly sets a new value. Svelte limits the number of nested updates to prevent infinite loops\\nhttps://svelte.dev/e/effect_update_depth_exceeded`);\n\n\t\terror.name = 'Svelte error';\n\n\t\tthrow error;\n\t} else {\n\t\tthrow new Error(`https://svelte.dev/e/effect_update_depth_exceeded`);\n\t}\n}\n\n/**\n * `getAbortSignal()` can only be called inside an effect or derived\n * @returns {never}\n */\nexport function get_abort_signal_outside_reaction() {\n\tif (DEV) {\n\t\tconst error = new Error(`get_abort_signal_outside_reaction\\n\\`getAbortSignal()\\` can only be called inside an effect or derived\\nhttps://svelte.dev/e/get_abort_signal_outside_reaction`);\n\n\t\terror.name = 'Svelte error';\n\n\t\tthrow error;\n\t} else {\n\t\tthrow new Error(`https://svelte.dev/e/get_abort_signal_outside_reaction`);\n\t}\n}\n\n/**\n * Failed to hydrate the application\n * @returns {never}\n */\nexport function hydration_failed() {\n\tif (DEV) {\n\t\tconst error = new Error(`hydration_failed\\nFailed to hydrate the application\\nhttps://svelte.dev/e/hydration_failed`);\n\n\t\terror.name = 'Svelte error';\n\n\t\tthrow error;\n\t} else {\n\t\tthrow new Error(`https://svelte.dev/e/hydration_failed`);\n\t}\n}\n\n/**\n * Could not `{@render}` snippet due to the expression being `null` or `undefined`. Consider using optional chaining `{@render snippet?.()}`\n * @returns {never}\n */\nexport function invalid_snippet() {\n\tif (DEV) {\n\t\tconst error = new Error(`invalid_snippet\\nCould not \\`{@render}\\` snippet due to the expression being \\`null\\` or \\`undefined\\`. Consider using optional chaining \\`{@render snippet?.()}\\`\\nhttps://svelte.dev/e/invalid_snippet`);\n\n\t\terror.name = 'Svelte error';\n\n\t\tthrow error;\n\t} else {\n\t\tthrow new Error(`https://svelte.dev/e/invalid_snippet`);\n\t}\n}\n\n/**\n * `%name%(...)` cannot be used in runes mode\n * @param {string} name\n * @returns {never}\n */\nexport function lifecycle_legacy_only(name) {\n\tif (DEV) {\n\t\tconst error = new Error(`lifecycle_legacy_only\\n\\`${name}(...)\\` cannot be used in runes mode\\nhttps://svelte.dev/e/lifecycle_legacy_only`);\n\n\t\terror.name = 'Svelte error';\n\n\t\tthrow error;\n\t} else {\n\t\tthrow new Error(`https://svelte.dev/e/lifecycle_legacy_only`);\n\t}\n}\n\n/**\n * Cannot do `bind:%key%={undefined}` when `%key%` has a fallback value\n * @param {string} key\n * @returns {never}\n */\nexport function props_invalid_value(key) {\n\tif (DEV) {\n\t\tconst error = new Error(`props_invalid_value\\nCannot do \\`bind:${key}={undefined}\\` when \\`${key}\\` has a fallback value\\nhttps://svelte.dev/e/props_invalid_value`);\n\n\t\terror.name = 'Svelte error';\n\n\t\tthrow error;\n\t} else {\n\t\tthrow new Error(`https://svelte.dev/e/props_invalid_value`);\n\t}\n}\n\n/**\n * Rest element properties of `$props()` such as `%property%` are readonly\n * @param {string} property\n * @returns {never}\n */\nexport function props_rest_readonly(property) {\n\tif (DEV) {\n\t\tconst error = new Error(`props_rest_readonly\\nRest element properties of \\`$props()\\` such as \\`${property}\\` are readonly\\nhttps://svelte.dev/e/props_rest_readonly`);\n\n\t\terror.name = 'Svelte error';\n\n\t\tthrow error;\n\t} else {\n\t\tthrow new Error(`https://svelte.dev/e/props_rest_readonly`);\n\t}\n}\n\n/**\n * The `%rune%` rune is only available inside `.svelte` and `.svelte.js/ts` files\n * @param {string} rune\n * @returns {never}\n */\nexport function rune_outside_svelte(rune) {\n\tif (DEV) {\n\t\tconst error = new Error(`rune_outside_svelte\\nThe \\`${rune}\\` rune is only available inside \\`.svelte\\` and \\`.svelte.js/ts\\` files\\nhttps://svelte.dev/e/rune_outside_svelte`);\n\n\t\terror.name = 'Svelte error';\n\n\t\tthrow error;\n\t} else {\n\t\tthrow new Error(`https://svelte.dev/e/rune_outside_svelte`);\n\t}\n}\n\n/**\n * Property descriptors defined on `$state` objects must contain `value` and always be `enumerable`, `configurable` and `writable`.\n * @returns {never}\n */\nexport function state_descriptors_fixed() {\n\tif (DEV) {\n\t\tconst error = new Error(`state_descriptors_fixed\\nProperty descriptors defined on \\`$state\\` objects must contain \\`value\\` and always be \\`enumerable\\`, \\`configurable\\` and \\`writable\\`.\\nhttps://svelte.dev/e/state_descriptors_fixed`);\n\n\t\terror.name = 'Svelte error';\n\n\t\tthrow error;\n\t} else {\n\t\tthrow new Error(`https://svelte.dev/e/state_descriptors_fixed`);\n\t}\n}\n\n/**\n * Cannot set prototype of `$state` object\n * @returns {never}\n */\nexport function state_prototype_fixed() {\n\tif (DEV) {\n\t\tconst error = new Error(`state_prototype_fixed\\nCannot set prototype of \\`$state\\` object\\nhttps://svelte.dev/e/state_prototype_fixed`);\n\n\t\terror.name = 'Svelte error';\n\n\t\tthrow error;\n\t} else {\n\t\tthrow new Error(`https://svelte.dev/e/state_prototype_fixed`);\n\t}\n}\n\n/**\n * Updating state inside `$derived(...)`, `$inspect(...)` or a template expression is forbidden. If the value should not be reactive, declare it without `$state`\n * @returns {never}\n */\nexport function state_unsafe_mutation() {\n\tif (DEV) {\n\t\tconst error = new Error(`state_unsafe_mutation\\nUpdating state inside \\`$derived(...)\\`, \\`$inspect(...)\\` or a template expression is forbidden. If the value should not be reactive, declare it without \\`$state\\`\\nhttps://svelte.dev/e/state_unsafe_mutation`);\n\n\t\terror.name = 'Svelte error';\n\n\t\tthrow error;\n\t} else {\n\t\tthrow new Error(`https://svelte.dev/e/state_unsafe_mutation`);\n\t}\n}", "/** @import { Equals } from '#client' */\n\n/** @type {Equals} */\nexport function equals(value) {\n\treturn value === this.v;\n}\n\n/**\n * @param {unknown} a\n * @param {unknown} b\n * @returns {boolean}\n */\nexport function safe_not_equal(a, b) {\n\treturn a != a\n\t\t? b == b\n\t\t: a !== b || (a !== null && typeof a === 'object') || typeof a === 'function';\n}\n\n/**\n * @param {unknown} a\n * @param {unknown} b\n * @returns {boolean}\n */\nexport function not_equal(a, b) {\n\treturn a !== b;\n}\n\n/** @type {Equals} */\nexport function safe_equals(value) {\n\treturn !safe_not_equal(value, this.v);\n}\n", "/* This file is generated by scripts/process-messages/index.js. Do not edit! */\n\nimport { DEV } from 'esm-env';\n\nvar bold = 'font-weight: bold';\nvar normal = 'font-weight: normal';\n\n/**\n * `<svelte:element this=\"%tag%\">` is a void element — it cannot have content\n * @param {string} tag\n */\nexport function dynamic_void_element_content(tag) {\n\tif (DEV) {\n\t\tconsole.warn(`%c[svelte] dynamic_void_element_content\\n%c\\`<svelte:element this=\"${tag}\">\\` is a void element — it cannot have content\\nhttps://svelte.dev/e/dynamic_void_element_content`, bold, normal);\n\t} else {\n\t\tconsole.warn(`https://svelte.dev/e/dynamic_void_element_content`);\n\t}\n}\n\n/**\n * The following properties cannot be cloned with `$state.snapshot` — the return value contains the originals:\n * \n * %properties%\n * @param {string | undefined | null} [properties]\n */\nexport function state_snapshot_uncloneable(properties) {\n\tif (DEV) {\n\t\tconsole.warn(\n\t\t\t`%c[svelte] state_snapshot_uncloneable\\n%c${properties\n\t\t\t\t? `The following properties cannot be cloned with \\`$state.snapshot\\` — the return value contains the originals:\n\n${properties}`\n\t\t\t\t: 'Value cannot be cloned with `$state.snapshot` — the original value was returned'}\\nhttps://svelte.dev/e/state_snapshot_uncloneable`,\n\t\t\tbold,\n\t\t\tnormal\n\t\t);\n\t} else {\n\t\tconsole.warn(`https://svelte.dev/e/state_snapshot_uncloneable`);\n\t}\n}", "/** @import { Snapshot } from './types' */\nimport { DEV } from 'esm-env';\nimport * as w from './warnings.js';\nimport { get_prototype_of, is_array, object_prototype } from './utils.js';\n\n/**\n * In dev, we keep track of which properties could not be cloned. In prod\n * we don't bother, but we keep a dummy array around so that the\n * signature stays the same\n * @type {string[]}\n */\nconst empty = [];\n\n/**\n * @template T\n * @param {T} value\n * @param {boolean} [skip_warning]\n * @returns {Snapshot<T>}\n */\nexport function snapshot(value, skip_warning = false) {\n\tif (DEV && !skip_warning) {\n\t\t/** @type {string[]} */\n\t\tconst paths = [];\n\n\t\tconst copy = clone(value, new Map(), '', paths);\n\t\tif (paths.length === 1 && paths[0] === '') {\n\t\t\t// value could not be cloned\n\t\t\tw.state_snapshot_uncloneable();\n\t\t} else if (paths.length > 0) {\n\t\t\t// some properties could not be cloned\n\t\t\tconst slice = paths.length > 10 ? paths.slice(0, 7) : paths.slice(0, 10);\n\t\t\tconst excess = paths.length - slice.length;\n\n\t\t\tlet uncloned = slice.map((path) => `- <value>${path}`).join('\\n');\n\t\t\tif (excess > 0) uncloned += `\\n- ...and ${excess} more`;\n\n\t\t\tw.state_snapshot_uncloneable(uncloned);\n\t\t}\n\n\t\treturn copy;\n\t}\n\n\treturn clone(value, new Map(), '', empty);\n}\n\n/**\n * @template T\n * @param {T} value\n * @param {Map<T, Snapshot<T>>} cloned\n * @param {string} path\n * @param {string[]} paths\n * @param {null | T} original The original value, if `value` was produced from a `toJSON` call\n * @returns {Snapshot<T>}\n */\nfunction clone(value, cloned, path, paths, original = null) {\n\tif (typeof value === 'object' && value !== null) {\n\t\tvar unwrapped = cloned.get(value);\n\t\tif (unwrapped !== undefined) return unwrapped;\n\n\t\tif (value instanceof Map) return /** @type {Snapshot<T>} */ (new Map(value));\n\t\tif (value instanceof Set) return /** @type {Snapshot<T>} */ (new Set(value));\n\n\t\tif (is_array(value)) {\n\t\t\tvar copy = /** @type {Snapshot<any>} */ (Array(value.length));\n\t\t\tcloned.set(value, copy);\n\n\t\t\tif (original !== null) {\n\t\t\t\tcloned.set(original, copy);\n\t\t\t}\n\n\t\t\tfor (var i = 0; i < value.length; i += 1) {\n\t\t\t\tvar element = value[i];\n\t\t\t\tif (i in value) {\n\t\t\t\t\tcopy[i] = clone(element, cloned, DEV ? `${path}[${i}]` : path, paths);\n\t\t\t\t}\n\t\t\t}\n\n\t\t\treturn copy;\n\t\t}\n\n\t\tif (get_prototype_of(value) === object_prototype) {\n\t\t\t/** @type {Snapshot<any>} */\n\t\t\tcopy = {};\n\t\t\tcloned.set(value, copy);\n\n\t\t\tif (original !== null) {\n\t\t\t\tcloned.set(original, copy);\n\t\t\t}\n\n\t\t\tfor (var key in value) {\n\t\t\t\t// @ts-expect-error\n\t\t\t\tcopy[key] = clone(value[key], cloned, DEV ? `${path}.${key}` : path, paths);\n\t\t\t}\n\n\t\t\treturn copy;\n\t\t}\n\n\t\tif (value instanceof Date) {\n\t\t\treturn /** @type {Snapshot<T>} */ (structuredClone(value));\n\t\t}\n\n\t\tif (typeof (/** @type {T & { toJSON?: any } } */ (value).toJSON) === 'function') {\n\t\t\treturn clone(\n\t\t\t\t/** @type {T & { toJSON(): any } } */ (value).toJSON(),\n\t\t\t\tcloned,\n\t\t\t\tDEV ? `${path}.toJSON()` : path,\n\t\t\t\tpaths,\n\t\t\t\t// Associate the instance with the toJSON clone\n\t\t\t\tvalue\n\t\t\t);\n\t\t}\n\t}\n\n\tif (value instanceof EventTarget) {\n\t\t// can't be cloned\n\t\treturn /** @type {Snapshot<T>} */ (value);\n\t}\n\n\ttry {\n\t\treturn /** @type {Snapshot<T>} */ (structuredClone(value));\n\t} catch (e) {\n\t\tif (DEV) {\n\t\t\tpaths.push(path);\n\t\t}\n\n\t\treturn /** @type {Snapshot<T>} */ (value);\n\t}\n}\n", "/** @import { Derived, Reaction, Value } from '#client' */\nimport { UNINITIALIZED } from '../../../constants.js';\nimport { snapshot } from '../../shared/clone.js';\nimport { define_property } from '../../shared/utils.js';\nimport { DERIVED, PROXY_PATH_SYMBOL, STATE_SYMBOL } from '#client/constants';\nimport { effect_tracking } from '../reactivity/effects.js';\nimport { active_reaction, captured_signals, set_captured_signals, untrack } from '../runtime.js';\n\n/**\n * @typedef {{\n *   traces: Error[];\n * }} TraceEntry\n */\n\n/** @type {{ reaction: Reaction | null, entries: Map<Value, TraceEntry> } | null} */\nexport let tracing_expressions = null;\n\n/**\n * @param {Value} signal\n * @param {TraceEntry} [entry]\n */\nfunction log_entry(signal, entry) {\n\tconst value = signal.v;\n\n\tif (value === UNINITIALIZED) {\n\t\treturn;\n\t}\n\n\tconst type = (signal.f & DERIVED) !== 0 ? '$derived' : '$state';\n\tconst current_reaction = /** @type {Reaction} */ (active_reaction);\n\tconst dirty = signal.wv > current_reaction.wv || current_reaction.wv === 0;\n\tconst style = dirty\n\t\t? 'color: CornflowerBlue; font-weight: bold'\n\t\t: 'color: grey; font-weight: normal';\n\n\t// eslint-disable-next-line no-console\n\tconsole.groupCollapsed(\n\t\tsignal.label ? `%c${type}%c ${signal.label}` : `%c${type}%c`,\n\t\tstyle,\n\t\tdirty ? 'font-weight: normal' : style,\n\t\ttypeof value === 'object' && value !== null && STATE_SYMBOL in value\n\t\t\t? snapshot(value, true)\n\t\t\t: value\n\t);\n\n\tif (type === '$derived') {\n\t\tconst deps = new Set(/** @type {Derived} */ (signal).deps);\n\t\tfor (const dep of deps) {\n\t\t\tlog_entry(dep);\n\t\t}\n\t}\n\n\tif (signal.created) {\n\t\t// eslint-disable-next-line no-console\n\t\tconsole.log(signal.created);\n\t}\n\n\tif (dirty && signal.updated) {\n\t\t// eslint-disable-next-line no-console\n\t\tconsole.log(signal.updated);\n\t}\n\n\tif (entry) {\n\t\tfor (var trace of entry.traces) {\n\t\t\t// eslint-disable-next-line no-console\n\t\t\tconsole.log(trace);\n\t\t}\n\t}\n\n\t// eslint-disable-next-line no-console\n\tconsole.groupEnd();\n}\n\n/**\n * @template T\n * @param {() => string} label\n * @param {() => T} fn\n */\nexport function trace(label, fn) {\n\tvar previously_tracing_expressions = tracing_expressions;\n\n\ttry {\n\t\ttracing_expressions = { entries: new Map(), reaction: active_reaction };\n\n\t\tvar start = performance.now();\n\t\tvar value = fn();\n\t\tvar time = (performance.now() - start).toFixed(2);\n\n\t\tvar prefix = untrack(label);\n\n\t\tif (!effect_tracking()) {\n\t\t\t// eslint-disable-next-line no-console\n\t\t\tconsole.log(`${prefix} %cran outside of an effect (${time}ms)`, 'color: grey');\n\t\t} else if (tracing_expressions.entries.size === 0) {\n\t\t\t// eslint-disable-next-line no-console\n\t\t\tconsole.log(`${prefix} %cno reactive dependencies (${time}ms)`, 'color: grey');\n\t\t} else {\n\t\t\t// eslint-disable-next-line no-console\n\t\t\tconsole.group(`${prefix} %c(${time}ms)`, 'color: grey');\n\n\t\t\tvar entries = tracing_expressions.entries;\n\n\t\t\tuntrack(() => {\n\t\t\t\tfor (const [signal, traces] of entries) {\n\t\t\t\t\tlog_entry(signal, traces);\n\t\t\t\t}\n\t\t\t});\n\n\t\t\ttracing_expressions = null;\n\n\t\t\t// eslint-disable-next-line no-console\n\t\t\tconsole.groupEnd();\n\t\t}\n\n\t\treturn value;\n\t} finally {\n\t\ttracing_expressions = previously_tracing_expressions;\n\t}\n}\n\n/**\n * @param {string} label\n */\nexport function get_stack(label) {\n\tlet error = Error();\n\tconst stack = error.stack;\n\n\tif (stack) {\n\t\tconst lines = stack.split('\\n');\n\t\tconst new_lines = ['\\n'];\n\n\t\tfor (let i = 0; i < lines.length; i++) {\n\t\t\tconst line = lines[i];\n\n\t\t\tif (line === 'Error') {\n\t\t\t\tcontinue;\n\t\t\t}\n\t\t\tif (line.includes('validate_each_keys')) {\n\t\t\t\treturn null;\n\t\t\t}\n\t\t\tif (line.includes('svelte/src/internal')) {\n\t\t\t\tcontinue;\n\t\t\t}\n\t\t\tnew_lines.push(line);\n\t\t}\n\n\t\tif (new_lines.length === 1) {\n\t\t\treturn null;\n\t\t}\n\n\t\tdefine_property(error, 'stack', {\n\t\t\tvalue: new_lines.join('\\n')\n\t\t});\n\n\t\tdefine_property(error, 'name', {\n\t\t\t// 'Error' suffix is required for stack traces to be rendered properly\n\t\t\tvalue: `${label}Error`\n\t\t});\n\t}\n\treturn error;\n}\n\n/**\n * @param {Value} source\n * @param {string} label\n */\nexport function tag(source, label) {\n\tsource.label = label;\n\ttag_proxy(source.v, label);\n\n\treturn source;\n}\n\n/**\n * @param {unknown} value\n * @param {string} label\n */\nexport function tag_proxy(value, label) {\n\t// @ts-expect-error\n\tvalue?.[PROXY_PATH_SYMBOL]?.(label);\n\treturn value;\n}\n\n/**\n * @param {unknown} value\n */\nexport function label(value) {\n\tif (typeof value === 'symbol') return `Symbol(${value.description})`;\n\tif (typeof value === 'function') return '<function>';\n\tif (typeof value === 'object' && value) return '<object>';\n\treturn String(value);\n}\n", "/** @import { ComponentContext, DevStackEntry } from '#client' */\n\nimport { DEV } from 'esm-env';\nimport { lifecycle_outside_component } from '../shared/errors.js';\nimport { source } from './reactivity/sources.js';\nimport {\n\tactive_effect,\n\tactive_reaction,\n\tset_active_effect,\n\tset_active_reaction\n} from './runtime.js';\nimport { create_user_effect, teardown } from './reactivity/effects.js';\nimport { legacy_mode_flag } from '../flags/index.js';\nimport { FILENAME } from '../../constants.js';\n\n/** @type {ComponentContext | null} */\nexport let component_context = null;\n\n/** @param {ComponentContext | null} context */\nexport function set_component_context(context) {\n\tcomponent_context = context;\n}\n\n/** @type {DevStackEntry | null} */\nexport let dev_stack = null;\n\n/** @param {DevStackEntry | null} stack */\nexport function set_dev_stack(stack) {\n\tdev_stack = stack;\n}\n\n/**\n * Execute a callback with a new dev stack entry\n * @param {() => any} callback - Function to execute\n * @param {DevStackEntry['type']} type - Type of block/component\n * @param {any} component - Component function\n * @param {number} line - Line number\n * @param {number} column - Column number\n * @param {Record<string, any>} [additional] - Any additional properties to add to the dev stack entry\n * @returns {any}\n */\nexport function add_svelte_meta(callback, type, component, line, column, additional) {\n\tconst parent = dev_stack;\n\n\tdev_stack = {\n\t\ttype,\n\t\tfile: component[FILENAME],\n\t\tline,\n\t\tcolumn,\n\t\tparent,\n\t\t...additional\n\t};\n\n\ttry {\n\t\treturn callback();\n\t} finally {\n\t\tdev_stack = parent;\n\t}\n}\n\n/**\n * The current component function. Different from current component context:\n * ```html\n * <!-- App.svelte -->\n * <Foo>\n *   <Bar /> <!-- context == Foo.svelte, function == App.svelte -->\n * </Foo>\n * ```\n * @type {ComponentContext['function']}\n */\nexport let dev_current_component_function = null;\n\n/** @param {ComponentContext['function']} fn */\nexport function set_dev_current_component_function(fn) {\n\tdev_current_component_function = fn;\n}\n\n/**\n * Retrieves the context that belongs to the closest parent component with the specified `key`.\n * Must be called during component initialisation.\n *\n * @template T\n * @param {any} key\n * @returns {T}\n */\nexport function getContext(key) {\n\tconst context_map = get_or_init_context_map('getContext');\n\tconst result = /** @type {T} */ (context_map.get(key));\n\treturn result;\n}\n\n/**\n * Associates an arbitrary `context` object with the current component and the specified `key`\n * and returns that object. The context is then available to children of the component\n * (including slotted content) with `getContext`.\n *\n * Like lifecycle functions, this must be called during component initialisation.\n *\n * @template T\n * @param {any} key\n * @param {T} context\n * @returns {T}\n */\nexport function setContext(key, context) {\n\tconst context_map = get_or_init_context_map('setContext');\n\tcontext_map.set(key, context);\n\treturn context;\n}\n\n/**\n * Checks whether a given `key` has been set in the context of a parent component.\n * Must be called during component initialisation.\n *\n * @param {any} key\n * @returns {boolean}\n */\nexport function hasContext(key) {\n\tconst context_map = get_or_init_context_map('hasContext');\n\treturn context_map.has(key);\n}\n\n/**\n * Retrieves the whole context map that belongs to the closest parent component.\n * Must be called during component initialisation. Useful, for example, if you\n * programmatically create a component and want to pass the existing context to it.\n *\n * @template {Map<any, any>} [T=Map<any, any>]\n * @returns {T}\n */\nexport function getAllContexts() {\n\tconst context_map = get_or_init_context_map('getAllContexts');\n\treturn /** @type {T} */ (context_map);\n}\n\n/**\n * @param {Record<string, unknown>} props\n * @param {any} runes\n * @param {Function} [fn]\n * @returns {void}\n */\nexport function push(props, runes = false, fn) {\n\tvar ctx = (component_context = {\n\t\tp: component_context,\n\t\tc: null,\n\t\td: false,\n\t\te: null,\n\t\tm: false,\n\t\ts: props,\n\t\tx: null,\n\t\tl: null\n\t});\n\n\tif (legacy_mode_flag && !runes) {\n\t\tcomponent_context.l = {\n\t\t\ts: null,\n\t\t\tu: null,\n\t\t\tr1: [],\n\t\t\tr2: source(false)\n\t\t};\n\t}\n\n\tteardown(() => {\n\t\t/** @type {ComponentContext} */ (ctx).d = true;\n\t});\n\n\tif (DEV) {\n\t\t// component function\n\t\tcomponent_context.function = fn;\n\t\tdev_current_component_function = fn;\n\t}\n}\n\n/**\n * @template {Record<string, any>} T\n * @param {T} [component]\n * @returns {T}\n */\nexport function pop(component) {\n\tconst context_stack_item = component_context;\n\tif (context_stack_item !== null) {\n\t\tif (component !== undefined) {\n\t\t\tcontext_stack_item.x = component;\n\t\t}\n\t\tconst component_effects = context_stack_item.e;\n\t\tif (component_effects !== null) {\n\t\t\tvar previous_effect = active_effect;\n\t\t\tvar previous_reaction = active_reaction;\n\t\t\tcontext_stack_item.e = null;\n\t\t\ttry {\n\t\t\t\tfor (var i = 0; i < component_effects.length; i++) {\n\t\t\t\t\tvar component_effect = component_effects[i];\n\t\t\t\t\tset_active_effect(component_effect.effect);\n\t\t\t\t\tset_active_reaction(component_effect.reaction);\n\t\t\t\t\tcreate_user_effect(component_effect.fn);\n\t\t\t\t}\n\t\t\t} finally {\n\t\t\t\tset_active_effect(previous_effect);\n\t\t\t\tset_active_reaction(previous_reaction);\n\t\t\t}\n\t\t}\n\t\tcomponent_context = context_stack_item.p;\n\t\tif (DEV) {\n\t\t\tdev_current_component_function = context_stack_item.p?.function ?? null;\n\t\t}\n\t\tcontext_stack_item.m = true;\n\t}\n\t// Micro-optimization: Don't set .a above to the empty object\n\t// so it can be garbage-collected when the return here is unused\n\treturn component || /** @type {T} */ ({});\n}\n\n/** @returns {boolean} */\nexport function is_runes() {\n\treturn !legacy_mode_flag || (component_context !== null && component_context.l === null);\n}\n\n/**\n * @param {string} name\n * @returns {Map<unknown, unknown>}\n */\nfunction get_or_init_context_map(name) {\n\tif (component_context === null) {\n\t\tlifecycle_outside_component(name);\n\t}\n\n\treturn (component_context.c ??= new Map(get_parent_context(component_context) || undefined));\n}\n\n/**\n * @param {ComponentContext} component_context\n * @returns {Map<unknown, unknown> | null}\n */\nfunction get_parent_context(component_context) {\n\tlet parent = component_context.p;\n\twhile (parent !== null) {\n\t\tconst context_map = parent.c;\n\t\tif (context_map !== null) {\n\t\t\treturn context_map;\n\t\t}\n\t\tparent = parent.p;\n\t}\n\treturn null;\n}\n", "/** @import { Derived, Effect } from '#client' */\nimport { DEV } from 'esm-env';\nimport { CLEAN, DERIVED, DIRTY, EFFECT_PRESERVED, MAYBE_DIRTY, UNOWNED } from '#client/constants';\nimport {\n\tactive_reaction,\n\tactive_effect,\n\tset_signal_status,\n\tskip_reaction,\n\tupdate_reaction,\n\tincrement_write_version,\n\tset_active_effect,\n\tpush_reaction_value,\n\tis_destroying_effect\n} from '../runtime.js';\nimport { equals, safe_equals } from './equality.js';\nimport * as e from '../errors.js';\nimport { destroy_effect } from './effects.js';\nimport { inspect_effects, set_inspect_effects } from './sources.js';\nimport { get_stack } from '../dev/tracing.js';\nimport { tracing_mode_flag } from '../../flags/index.js';\nimport { component_context } from '../context.js';\n\n/**\n * @template V\n * @param {() => V} fn\n * @returns {Derived<V>}\n */\n/*#__NO_SIDE_EFFECTS__*/\nexport function derived(fn) {\n\tvar flags = DERIVED | DIRTY;\n\tvar parent_derived =\n\t\tactive_reaction !== null && (active_reaction.f & DERIVED) !== 0\n\t\t\t? /** @type {Derived} */ (active_reaction)\n\t\t\t: null;\n\n\tif (active_effect === null || (parent_derived !== null && (parent_derived.f & UNOWNED) !== 0)) {\n\t\tflags |= UNOWNED;\n\t} else {\n\t\t// Since deriveds are evaluated lazily, any effects created inside them are\n\t\t// created too late to ensure that the parent effect is added to the tree\n\t\tactive_effect.f |= EFFECT_PRESERVED;\n\t}\n\n\t/** @type {Derived<V>} */\n\tconst signal = {\n\t\tctx: component_context,\n\t\tdeps: null,\n\t\teffects: null,\n\t\tequals,\n\t\tf: flags,\n\t\tfn,\n\t\treactions: null,\n\t\trv: 0,\n\t\tv: /** @type {V} */ (null),\n\t\twv: 0,\n\t\tparent: parent_derived ?? active_effect,\n\t\tac: null\n\t};\n\n\tif (DEV && tracing_mode_flag) {\n\t\tsignal.created = get_stack('CreatedAt');\n\t}\n\n\treturn signal;\n}\n\n/**\n * @template V\n * @param {() => V} fn\n * @returns {Derived<V>}\n */\n/*#__NO_SIDE_EFFECTS__*/\nexport function user_derived(fn) {\n\tconst d = derived(fn);\n\n\tpush_reaction_value(d);\n\n\treturn d;\n}\n\n/**\n * @template V\n * @param {() => V} fn\n * @returns {Derived<V>}\n */\n/*#__NO_SIDE_EFFECTS__*/\nexport function derived_safe_equal(fn) {\n\tconst signal = derived(fn);\n\tsignal.equals = safe_equals;\n\treturn signal;\n}\n\n/**\n * @param {Derived} derived\n * @returns {void}\n */\nexport function destroy_derived_effects(derived) {\n\tvar effects = derived.effects;\n\n\tif (effects !== null) {\n\t\tderived.effects = null;\n\n\t\tfor (var i = 0; i < effects.length; i += 1) {\n\t\t\tdestroy_effect(/** @type {Effect} */ (effects[i]));\n\t\t}\n\t}\n}\n\n/**\n * The currently updating deriveds, used to detect infinite recursion\n * in dev mode and provide a nicer error than 'too much recursion'\n * @type {Derived[]}\n */\nlet stack = [];\n\n/**\n * @param {Derived} derived\n * @returns {Effect | null}\n */\nfunction get_derived_parent_effect(derived) {\n\tvar parent = derived.parent;\n\twhile (parent !== null) {\n\t\tif ((parent.f & DERIVED) === 0) {\n\t\t\treturn /** @type {Effect} */ (parent);\n\t\t}\n\t\tparent = parent.parent;\n\t}\n\treturn null;\n}\n\n/**\n * @template T\n * @param {Derived} derived\n * @returns {T}\n */\nexport function execute_derived(derived) {\n\tvar value;\n\tvar prev_active_effect = active_effect;\n\n\tset_active_effect(get_derived_parent_effect(derived));\n\n\tif (DEV) {\n\t\tlet prev_inspect_effects = inspect_effects;\n\t\tset_inspect_effects(new Set());\n\t\ttry {\n\t\t\tif (stack.includes(derived)) {\n\t\t\t\te.derived_references_self();\n\t\t\t}\n\n\t\t\tstack.push(derived);\n\n\t\t\tdestroy_derived_effects(derived);\n\t\t\tvalue = update_reaction(derived);\n\t\t} finally {\n\t\t\tset_active_effect(prev_active_effect);\n\t\t\tset_inspect_effects(prev_inspect_effects);\n\t\t\tstack.pop();\n\t\t}\n\t} else {\n\t\ttry {\n\t\t\tdestroy_derived_effects(derived);\n\t\t\tvalue = update_reaction(derived);\n\t\t} finally {\n\t\t\tset_active_effect(prev_active_effect);\n\t\t}\n\t}\n\n\treturn value;\n}\n\n/**\n * @param {Derived} derived\n * @returns {void}\n */\nexport function update_derived(derived) {\n\tvar value = execute_derived(derived);\n\n\tif (!derived.equals(value)) {\n\t\tderived.v = value;\n\t\tderived.wv = increment_write_version();\n\t}\n\n\t// don't mark derived clean if we're reading it inside a\n\t// cleanup function, or it will cache a stale value\n\tif (is_destroying_effect) return;\n\n\tvar status =\n\t\t(skip_reaction || (derived.f & UNOWNED) !== 0) && derived.deps !== null ? MAYBE_DIRTY : CLEAN;\n\n\tset_signal_status(derived, status);\n}\n", "/** @import { Derived, Effect, Source, Value } from '#client' */\nimport { DEV } from 'esm-env';\nimport {\n\tactive_reaction,\n\tactive_effect,\n\tuntracked_writes,\n\tget,\n\tschedule_effect,\n\tset_untracked_writes,\n\tset_signal_status,\n\tuntrack,\n\tincrement_write_version,\n\tupdate_effect,\n\tsource_ownership,\n\tcheck_dirtiness,\n\tuntracking,\n\tis_destroying_effect,\n\tpush_reaction_value\n} from '../runtime.js';\nimport { equals, safe_equals } from './equality.js';\nimport {\n\tCLEAN,\n\tDERIVED,\n\tDIRTY,\n\tBRANCH_EFFECT,\n\tINSPECT_EFFECT,\n\tUNOWNED,\n\tMAYBE_DIRTY,\n\tBLOCK_EFFECT,\n\tROOT_EFFECT\n} from '#client/constants';\nimport * as e from '../errors.js';\nimport { legacy_mode_flag, tracing_mode_flag } from '../../flags/index.js';\nimport { get_stack, tag_proxy } from '../dev/tracing.js';\nimport { component_context, is_runes } from '../context.js';\nimport { proxy } from '../proxy.js';\nimport { execute_derived } from './deriveds.js';\n\nexport let inspect_effects = new Set();\n\n/** @type {Map<Source, any>} */\nexport const old_values = new Map();\n\n/**\n * @param {Set<any>} v\n */\nexport function set_inspect_effects(v) {\n\tinspect_effects = v;\n}\n\n/**\n * @template V\n * @param {V} v\n * @param {Error | null} [stack]\n * @returns {Source<V>}\n */\n// TODO rename this to `state` throughout the codebase\nexport function source(v, stack) {\n\t/** @type {Value} */\n\tvar signal = {\n\t\tf: 0, // TODO ideally we could skip this altogether, but it causes type errors\n\t\tv,\n\t\treactions: null,\n\t\tequals,\n\t\trv: 0,\n\t\twv: 0\n\t};\n\n\tif (DEV && tracing_mode_flag) {\n\t\tsignal.created = stack ?? get_stack('CreatedAt');\n\t\tsignal.updated = null;\n\t\tsignal.set_during_effect = false;\n\t\tsignal.trace = null;\n\t}\n\n\treturn signal;\n}\n\n/**\n * @template V\n * @param {V} v\n * @param {Error | null} [stack]\n */\n/*#__NO_SIDE_EFFECTS__*/\nexport function state(v, stack) {\n\tconst s = source(v, stack);\n\n\tpush_reaction_value(s);\n\n\treturn s;\n}\n\n/**\n * @template V\n * @param {V} initial_value\n * @param {boolean} [immutable]\n * @returns {Source<V>}\n */\n/*#__NO_SIDE_EFFECTS__*/\nexport function mutable_source(initial_value, immutable = false, trackable = true) {\n\tconst s = source(initial_value);\n\tif (!immutable) {\n\t\ts.equals = safe_equals;\n\t}\n\n\t// bind the signal to the component context, in case we need to\n\t// track updates to trigger beforeUpdate/afterUpdate callbacks\n\tif (legacy_mode_flag && trackable && component_context !== null && component_context.l !== null) {\n\t\t(component_context.l.s ??= []).push(s);\n\t}\n\n\treturn s;\n}\n\n/**\n * @template V\n * @param {Value<V>} source\n * @param {V} value\n */\nexport function mutate(source, value) {\n\tset(\n\t\tsource,\n\t\tuntrack(() => get(source))\n\t);\n\treturn value;\n}\n\n/**\n * @template V\n * @param {Source<V>} source\n * @param {V} value\n * @param {boolean} [should_proxy]\n * @returns {V}\n */\nexport function set(source, value, should_proxy = false) {\n\tif (\n\t\tactive_reaction !== null &&\n\t\t// since we are untracking the function inside `$inspect.with` we need to add this check\n\t\t// to ensure we error if state is set inside an inspect effect\n\t\t(!untracking || (active_reaction.f & INSPECT_EFFECT) !== 0) &&\n\t\tis_runes() &&\n\t\t(active_reaction.f & (DERIVED | BLOCK_EFFECT | INSPECT_EFFECT)) !== 0 &&\n\t\t!(source_ownership?.reaction === active_reaction && source_ownership.sources.includes(source))\n\t) {\n\t\te.state_unsafe_mutation();\n\t}\n\n\tlet new_value = should_proxy ? proxy(value) : value;\n\n\tif (DEV) {\n\t\ttag_proxy(new_value, /** @type {string} */ (source.label));\n\t}\n\n\treturn internal_set(source, new_value);\n}\n\n/**\n * @template V\n * @param {Source<V>} source\n * @param {V} value\n * @returns {V}\n */\nexport function internal_set(source, value) {\n\tif (!source.equals(value)) {\n\t\tvar old_value = source.v;\n\n\t\tif (is_destroying_effect) {\n\t\t\told_values.set(source, value);\n\t\t} else {\n\t\t\told_values.set(source, old_value);\n\t\t}\n\n\t\tsource.v = value;\n\n\t\tif (DEV && tracing_mode_flag) {\n\t\t\tsource.updated = get_stack('UpdatedAt');\n\n\t\t\tif (active_effect !== null) {\n\t\t\t\tsource.set_during_effect = true;\n\t\t\t}\n\t\t}\n\n\t\tif ((source.f & DERIVED) !== 0) {\n\t\t\t// if we are assigning to a dirty derived we set it to clean/maybe dirty but we also eagerly execute it to track the dependencies\n\t\t\tif ((source.f & DIRTY) !== 0) {\n\t\t\t\texecute_derived(/** @type {Derived} */ (source));\n\t\t\t}\n\t\t\tset_signal_status(source, (source.f & UNOWNED) === 0 ? CLEAN : MAYBE_DIRTY);\n\t\t}\n\n\t\tsource.wv = increment_write_version();\n\n\t\tmark_reactions(source, DIRTY);\n\n\t\t// It's possible that the current reaction might not have up-to-date dependencies\n\t\t// whilst it's actively running. So in the case of ensuring it registers the reaction\n\t\t// properly for itself, we need to ensure the current effect actually gets\n\t\t// scheduled. i.e: `$effect(() => x++)`\n\t\tif (\n\t\t\tis_runes() &&\n\t\t\tactive_effect !== null &&\n\t\t\t(active_effect.f & CLEAN) !== 0 &&\n\t\t\t(active_effect.f & (BRANCH_EFFECT | ROOT_EFFECT)) === 0\n\t\t) {\n\t\t\tif (untracked_writes === null) {\n\t\t\t\tset_untracked_writes([source]);\n\t\t\t} else {\n\t\t\t\tuntracked_writes.push(source);\n\t\t\t}\n\t\t}\n\n\t\tif (DEV && inspect_effects.size > 0) {\n\t\t\tconst inspects = Array.from(inspect_effects);\n\n\t\t\tfor (const effect of inspects) {\n\t\t\t\t// Mark clean inspect-effects as maybe dirty and then check their dirtiness\n\t\t\t\t// instead of just updating the effects - this way we avoid overfiring.\n\t\t\t\tif ((effect.f & CLEAN) !== 0) {\n\t\t\t\t\tset_signal_status(effect, MAYBE_DIRTY);\n\t\t\t\t}\n\t\t\t\tif (check_dirtiness(effect)) {\n\t\t\t\t\tupdate_effect(effect);\n\t\t\t\t}\n\t\t\t}\n\n\t\t\tinspect_effects.clear();\n\t\t}\n\t}\n\n\treturn value;\n}\n\n/**\n * @template {number | bigint} T\n * @param {Source<T>} source\n * @param {1 | -1} [d]\n * @returns {T}\n */\nexport function update(source, d = 1) {\n\tvar value = get(source);\n\tvar result = d === 1 ? value++ : value--;\n\n\tset(source, value);\n\n\t// @ts-expect-error\n\treturn result;\n}\n\n/**\n * @template {number | bigint} T\n * @param {Source<T>} source\n * @param {1 | -1} [d]\n * @returns {T}\n */\nexport function update_pre(source, d = 1) {\n\tvar value = get(source);\n\n\t// @ts-expect-error\n\treturn set(source, d === 1 ? ++value : --value);\n}\n\n/**\n * @param {Value} signal\n * @param {number} status should be DIRTY or MAYBE_DIRTY\n * @returns {void}\n */\nfunction mark_reactions(signal, status) {\n\tvar reactions = signal.reactions;\n\tif (reactions === null) return;\n\n\tvar runes = is_runes();\n\tvar length = reactions.length;\n\n\tfor (var i = 0; i < length; i++) {\n\t\tvar reaction = reactions[i];\n\t\tvar flags = reaction.f;\n\n\t\t// Skip any effects that are already dirty\n\t\tif ((flags & DIRTY) !== 0) continue;\n\n\t\t// In legacy mode, skip the current effect to prevent infinite loops\n\t\tif (!runes && reaction === active_effect) continue;\n\n\t\t// Inspect effects need to run immediately, so that the stack trace makes sense\n\t\tif (DEV && (flags & INSPECT_EFFECT) !== 0) {\n\t\t\tinspect_effects.add(reaction);\n\t\t\tcontinue;\n\t\t}\n\n\t\tset_signal_status(reaction, status);\n\n\t\t// If the signal a) was previously clean or b) is an unowned derived, then mark it\n\t\tif ((flags & (CLEAN | UNOWNED)) !== 0) {\n\t\t\tif ((flags & DERIVED) !== 0) {\n\t\t\t\tmark_reactions(/** @type {Derived} */ (reaction), MAYBE_DIRTY);\n\t\t\t} else {\n\t\t\t\tschedule_effect(/** @type {Effect} */ (reaction));\n\t\t\t}\n\t\t}\n\t}\n}\n", "/** @import { ComponentContext, ComponentContextLegacy, Derived, Effect, TemplateNode, TransitionManager } from '#client' */\nimport {\n\tcheck_dirtiness,\n\tactive_effect,\n\tactive_reaction,\n\tupdate_effect,\n\tget,\n\tis_destroying_effect,\n\tremove_reactions,\n\tschedule_effect,\n\tset_active_reaction,\n\tset_is_destroying_effect,\n\tset_signal_status,\n\tuntrack,\n\tuntracking\n} from '../runtime.js';\nimport {\n\tDIRTY,\n\tBRANCH_EFFECT,\n\tRENDER_EFFECT,\n\tEFFECT,\n\tDESTROYED,\n\tINERT,\n\tEFFECT_RAN,\n\tBLOCK_EFFECT,\n\tROOT_EFFECT,\n\tEFFECT_TRANSPARENT,\n\tDERIVED,\n\tUNOWNED,\n\tCLEAN,\n\tINSPECT_EFFECT,\n\tHEAD_EFFECT,\n\tMAYBE_DIRTY,\n\tEFFECT_PRESERVED,\n\tBOUNDARY_EFFECT,\n\tSTALE_REACTION,\n\tUSER_EFFECT\n} from '#client/constants';\nimport { set } from './sources.js';\nimport * as e from '../errors.js';\nimport { DEV } from 'esm-env';\nimport { define_property } from '../../shared/utils.js';\nimport { get_next_sibling } from '../dom/operations.js';\nimport { derived } from './deriveds.js';\nimport { component_context, dev_current_component_function, dev_stack } from '../context.js';\n\n/**\n * @param {'$effect' | '$effect.pre' | '$inspect'} rune\n */\nexport function validate_effect(rune) {\n\tif (active_effect === null && active_reaction === null) {\n\t\te.effect_orphan(rune);\n\t}\n\n\tif (active_reaction !== null && (active_reaction.f & UNOWNED) !== 0 && active_effect === null) {\n\t\te.effect_in_unowned_derived();\n\t}\n\n\tif (is_destroying_effect) {\n\t\te.effect_in_teardown(rune);\n\t}\n}\n\n/**\n * @param {Effect} effect\n * @param {Effect} parent_effect\n */\nfunction push_effect(effect, parent_effect) {\n\tvar parent_last = parent_effect.last;\n\tif (parent_last === null) {\n\t\tparent_effect.last = parent_effect.first = effect;\n\t} else {\n\t\tparent_last.next = effect;\n\t\teffect.prev = parent_last;\n\t\tparent_effect.last = effect;\n\t}\n}\n\n/**\n * @param {number} type\n * @param {null | (() => void | (() => void))} fn\n * @param {boolean} sync\n * @param {boolean} push\n * @returns {Effect}\n */\nfunction create_effect(type, fn, sync, push = true) {\n\tvar parent = active_effect;\n\n\tif (DEV) {\n\t\t// Ensure the parent is never an inspect effect\n\t\twhile (parent !== null && (parent.f & INSPECT_EFFECT) !== 0) {\n\t\t\tparent = parent.parent;\n\t\t}\n\t}\n\n\t/** @type {Effect} */\n\tvar effect = {\n\t\tctx: component_context,\n\t\tdeps: null,\n\t\tnodes_start: null,\n\t\tnodes_end: null,\n\t\tf: type | DIRTY,\n\t\tfirst: null,\n\t\tfn,\n\t\tlast: null,\n\t\tnext: null,\n\t\tparent,\n\t\tb: parent && parent.b,\n\t\tprev: null,\n\t\tteardown: null,\n\t\ttransitions: null,\n\t\twv: 0,\n\t\tac: null\n\t};\n\n\tif (DEV) {\n\t\teffect.component_function = dev_current_component_function;\n\t}\n\n\tif (sync) {\n\t\ttry {\n\t\t\tupdate_effect(effect);\n\t\t\teffect.f |= EFFECT_RAN;\n\t\t} catch (e) {\n\t\t\tdestroy_effect(effect);\n\t\t\tthrow e;\n\t\t}\n\t} else if (fn !== null) {\n\t\tschedule_effect(effect);\n\t}\n\n\t// if an effect has no dependencies, no DOM and no teardown function,\n\t// don't bother adding it to the effect tree\n\tvar inert =\n\t\tsync &&\n\t\teffect.deps === null &&\n\t\teffect.first === null &&\n\t\teffect.nodes_start === null &&\n\t\teffect.teardown === null &&\n\t\t(effect.f & (EFFECT_PRESERVED | BOUNDARY_EFFECT)) === 0;\n\n\tif (!inert && push) {\n\t\tif (parent !== null) {\n\t\t\tpush_effect(effect, parent);\n\t\t}\n\n\t\t// if we're in a derived, add the effect there too\n\t\tif (active_reaction !== null && (active_reaction.f & DERIVED) !== 0) {\n\t\t\tvar derived = /** @type {Derived} */ (active_reaction);\n\t\t\t(derived.effects ??= []).push(effect);\n\t\t}\n\t}\n\n\treturn effect;\n}\n\n/**\n * Internal representation of `$effect.tracking()`\n * @returns {boolean}\n */\nexport function effect_tracking() {\n\treturn active_reaction !== null && !untracking;\n}\n\n/**\n * @param {() => void} fn\n */\nexport function teardown(fn) {\n\tconst effect = create_effect(RENDER_EFFECT, null, false);\n\tset_signal_status(effect, CLEAN);\n\teffect.teardown = fn;\n\treturn effect;\n}\n\n/**\n * Internal representation of `$effect(...)`\n * @param {() => void | (() => void)} fn\n */\nexport function user_effect(fn) {\n\tvalidate_effect('$effect');\n\n\t// Non-nested `$effect(...)` in a component should be deferred\n\t// until the component is mounted\n\tvar defer =\n\t\tactive_effect !== null &&\n\t\t(active_effect.f & BRANCH_EFFECT) !== 0 &&\n\t\tcomponent_context !== null &&\n\t\t!component_context.m;\n\n\tif (DEV) {\n\t\tdefine_property(fn, 'name', {\n\t\t\tvalue: '$effect'\n\t\t});\n\t}\n\n\tif (defer) {\n\t\tvar context = /** @type {ComponentContext} */ (component_context);\n\t\t(context.e ??= []).push({\n\t\t\tfn,\n\t\t\teffect: active_effect,\n\t\t\treaction: active_reaction\n\t\t});\n\t} else {\n\t\treturn create_user_effect(fn);\n\t}\n}\n\n/**\n * @param {() => void | (() => void)} fn\n */\nexport function create_user_effect(fn) {\n\treturn create_effect(EFFECT | USER_EFFECT, fn, false);\n}\n\n/**\n * Internal representation of `$effect.pre(...)`\n * @param {() => void | (() => void)} fn\n * @returns {Effect}\n */\nexport function user_pre_effect(fn) {\n\tvalidate_effect('$effect.pre');\n\tif (DEV) {\n\t\tdefine_property(fn, 'name', {\n\t\t\tvalue: '$effect.pre'\n\t\t});\n\t}\n\treturn create_effect(RENDER_EFFECT | USER_EFFECT, fn, true);\n}\n\n/** @param {() => void | (() => void)} fn */\nexport function inspect_effect(fn) {\n\treturn create_effect(INSPECT_EFFECT, fn, true);\n}\n\n/**\n * Internal representation of `$effect.root(...)`\n * @param {() => void | (() => void)} fn\n * @returns {() => void}\n */\nexport function effect_root(fn) {\n\tconst effect = create_effect(ROOT_EFFECT, fn, true);\n\n\treturn () => {\n\t\tdestroy_effect(effect);\n\t};\n}\n\n/**\n * An effect root whose children can transition out\n * @param {() => void} fn\n * @returns {(options?: { outro?: boolean }) => Promise<void>}\n */\nexport function component_root(fn) {\n\tconst effect = create_effect(ROOT_EFFECT, fn, true);\n\n\treturn (options = {}) => {\n\t\treturn new Promise((fulfil) => {\n\t\t\tif (options.outro) {\n\t\t\t\tpause_effect(effect, () => {\n\t\t\t\t\tdestroy_effect(effect);\n\t\t\t\t\tfulfil(undefined);\n\t\t\t\t});\n\t\t\t} else {\n\t\t\t\tdestroy_effect(effect);\n\t\t\t\tfulfil(undefined);\n\t\t\t}\n\t\t});\n\t};\n}\n\n/**\n * @param {() => void | (() => void)} fn\n * @returns {Effect}\n */\nexport function effect(fn) {\n\treturn create_effect(EFFECT, fn, false);\n}\n\n/**\n * Internal representation of `$: ..`\n * @param {() => any} deps\n * @param {() => void | (() => void)} fn\n */\nexport function legacy_pre_effect(deps, fn) {\n\tvar context = /** @type {ComponentContextLegacy} */ (component_context);\n\n\t/** @type {{ effect: null | Effect, ran: boolean }} */\n\tvar token = { effect: null, ran: false };\n\tcontext.l.r1.push(token);\n\n\ttoken.effect = render_effect(() => {\n\t\tdeps();\n\n\t\t// If this legacy pre effect has already run before the end of the reset, then\n\t\t// bail out to emulate the same behavior.\n\t\tif (token.ran) return;\n\n\t\ttoken.ran = true;\n\t\tset(context.l.r2, true);\n\t\tuntrack(fn);\n\t});\n}\n\nexport function legacy_pre_effect_reset() {\n\tvar context = /** @type {ComponentContextLegacy} */ (component_context);\n\n\trender_effect(() => {\n\t\tif (!get(context.l.r2)) return;\n\n\t\t// Run dirty `$:` statements\n\t\tfor (var token of context.l.r1) {\n\t\t\tvar effect = token.effect;\n\n\t\t\t// If the effect is CLEAN, then make it MAYBE_DIRTY. This ensures we traverse through\n\t\t\t// the effects dependencies and correctly ensure each dependency is up-to-date.\n\t\t\tif ((effect.f & CLEAN) !== 0) {\n\t\t\t\tset_signal_status(effect, MAYBE_DIRTY);\n\t\t\t}\n\n\t\t\tif (check_dirtiness(effect)) {\n\t\t\t\tupdate_effect(effect);\n\t\t\t}\n\n\t\t\ttoken.ran = false;\n\t\t}\n\n\t\tcontext.l.r2.v = false; // set directly to avoid rerunning this effect\n\t});\n}\n\n/**\n * @param {() => void | (() => void)} fn\n * @returns {Effect}\n */\nexport function render_effect(fn) {\n\treturn create_effect(RENDER_EFFECT, fn, true);\n}\n\n/**\n * @param {(...expressions: any) => void | (() => void)} fn\n * @param {Array<() => any>} thunks\n * @param {<T>(fn: () => T) => Derived<T>} d\n * @returns {Effect}\n */\nexport function template_effect(fn, thunks = [], d = derived) {\n\tif (DEV) {\n\t\t// wrap the effect so that we can decorate stack trace with `in {expression}`\n\t\t// (TODO maybe there's a better approach?)\n\t\treturn render_effect(() => {\n\t\t\tvar outer = /** @type {Effect} */ (active_effect);\n\t\t\tvar inner = () => fn(...deriveds.map(get));\n\n\t\t\tdefine_property(outer.fn, 'name', { value: '{expression}' });\n\t\t\tdefine_property(inner, 'name', { value: '{expression}' });\n\n\t\t\tconst deriveds = thunks.map(d);\n\t\t\tblock(inner);\n\t\t});\n\t}\n\n\tconst deriveds = thunks.map(d);\n\treturn block(() => fn(...deriveds.map(get)));\n}\n\n/**\n * @param {(() => void)} fn\n * @param {number} flags\n */\nexport function block(fn, flags = 0) {\n\tvar effect = create_effect(RENDER_EFFECT | BLOCK_EFFECT | flags, fn, true);\n\tif (DEV) {\n\t\teffect.dev_stack = dev_stack;\n\t}\n\treturn effect;\n}\n\n/**\n * @param {(() => void)} fn\n * @param {boolean} [push]\n */\nexport function branch(fn, push = true) {\n\treturn create_effect(RENDER_EFFECT | BRANCH_EFFECT, fn, true, push);\n}\n\n/**\n * @param {Effect} effect\n */\nexport function execute_effect_teardown(effect) {\n\tvar teardown = effect.teardown;\n\tif (teardown !== null) {\n\t\tconst previously_destroying_effect = is_destroying_effect;\n\t\tconst previous_reaction = active_reaction;\n\t\tset_is_destroying_effect(true);\n\t\tset_active_reaction(null);\n\t\ttry {\n\t\t\tteardown.call(null);\n\t\t} finally {\n\t\t\tset_is_destroying_effect(previously_destroying_effect);\n\t\t\tset_active_reaction(previous_reaction);\n\t\t}\n\t}\n}\n\n/**\n * @param {Effect} signal\n * @param {boolean} remove_dom\n * @returns {void}\n */\nexport function destroy_effect_children(signal, remove_dom = false) {\n\tvar effect = signal.first;\n\tsignal.first = signal.last = null;\n\n\twhile (effect !== null) {\n\t\teffect.ac?.abort(STALE_REACTION);\n\n\t\tvar next = effect.next;\n\n\t\tif ((effect.f & ROOT_EFFECT) !== 0) {\n\t\t\t// this is now an independent root\n\t\t\teffect.parent = null;\n\t\t} else {\n\t\t\tdestroy_effect(effect, remove_dom);\n\t\t}\n\n\t\teffect = next;\n\t}\n}\n\n/**\n * @param {Effect} signal\n * @returns {void}\n */\nexport function destroy_block_effect_children(signal) {\n\tvar effect = signal.first;\n\n\twhile (effect !== null) {\n\t\tvar next = effect.next;\n\t\tif ((effect.f & BRANCH_EFFECT) === 0) {\n\t\t\tdestroy_effect(effect);\n\t\t}\n\t\teffect = next;\n\t}\n}\n\n/**\n * @param {Effect} effect\n * @param {boolean} [remove_dom]\n * @returns {void}\n */\nexport function destroy_effect(effect, remove_dom = true) {\n\tvar removed = false;\n\n\tif (\n\t\t(remove_dom || (effect.f & HEAD_EFFECT) !== 0) &&\n\t\teffect.nodes_start !== null &&\n\t\teffect.nodes_end !== null\n\t) {\n\t\tremove_effect_dom(effect.nodes_start, /** @type {TemplateNode} */ (effect.nodes_end));\n\t\tremoved = true;\n\t}\n\n\tdestroy_effect_children(effect, remove_dom && !removed);\n\tremove_reactions(effect, 0);\n\tset_signal_status(effect, DESTROYED);\n\n\tvar transitions = effect.transitions;\n\n\tif (transitions !== null) {\n\t\tfor (const transition of transitions) {\n\t\t\ttransition.stop();\n\t\t}\n\t}\n\n\texecute_effect_teardown(effect);\n\n\tvar parent = effect.parent;\n\n\t// If the parent doesn't have any children, then skip this work altogether\n\tif (parent !== null && parent.first !== null) {\n\t\tunlink_effect(effect);\n\t}\n\n\tif (DEV) {\n\t\teffect.component_function = null;\n\t}\n\n\t// `first` and `child` are nulled out in destroy_effect_children\n\t// we don't null out `parent` so that error propagation can work correctly\n\teffect.next =\n\t\teffect.prev =\n\t\teffect.teardown =\n\t\teffect.ctx =\n\t\teffect.deps =\n\t\teffect.fn =\n\t\teffect.nodes_start =\n\t\teffect.nodes_end =\n\t\teffect.ac =\n\t\t\tnull;\n}\n\n/**\n *\n * @param {TemplateNode | null} node\n * @param {TemplateNode} end\n */\nexport function remove_effect_dom(node, end) {\n\twhile (node !== null) {\n\t\t/** @type {TemplateNode | null} */\n\t\tvar next = node === end ? null : /** @type {TemplateNode} */ (get_next_sibling(node));\n\n\t\tnode.remove();\n\t\tnode = next;\n\t}\n}\n\n/**\n * Detach an effect from the effect tree, freeing up memory and\n * reducing the amount of work that happens on subsequent traversals\n * @param {Effect} effect\n */\nexport function unlink_effect(effect) {\n\tvar parent = effect.parent;\n\tvar prev = effect.prev;\n\tvar next = effect.next;\n\n\tif (prev !== null) prev.next = next;\n\tif (next !== null) next.prev = prev;\n\n\tif (parent !== null) {\n\t\tif (parent.first === effect) parent.first = next;\n\t\tif (parent.last === effect) parent.last = prev;\n\t}\n}\n\n/**\n * When a block effect is removed, we don't immediately destroy it or yank it\n * out of the DOM, because it might have transitions. Instead, we 'pause' it.\n * It stays around (in memory, and in the DOM) until outro transitions have\n * completed, and if the state change is reversed then we _resume_ it.\n * A paused effect does not update, and the DOM subtree becomes inert.\n * @param {Effect} effect\n * @param {() => void} [callback]\n */\nexport function pause_effect(effect, callback) {\n\t/** @type {TransitionManager[]} */\n\tvar transitions = [];\n\n\tpause_children(effect, transitions, true);\n\n\trun_out_transitions(transitions, () => {\n\t\tdestroy_effect(effect);\n\t\tif (callback) callback();\n\t});\n}\n\n/**\n * @param {TransitionManager[]} transitions\n * @param {() => void} fn\n */\nexport function run_out_transitions(transitions, fn) {\n\tvar remaining = transitions.length;\n\tif (remaining > 0) {\n\t\tvar check = () => --remaining || fn();\n\t\tfor (var transition of transitions) {\n\t\t\ttransition.out(check);\n\t\t}\n\t} else {\n\t\tfn();\n\t}\n}\n\n/**\n * @param {Effect} effect\n * @param {TransitionManager[]} transitions\n * @param {boolean} local\n */\nexport function pause_children(effect, transitions, local) {\n\tif ((effect.f & INERT) !== 0) return;\n\teffect.f ^= INERT;\n\n\tif (effect.transitions !== null) {\n\t\tfor (const transition of effect.transitions) {\n\t\t\tif (transition.is_global || local) {\n\t\t\t\ttransitions.push(transition);\n\t\t\t}\n\t\t}\n\t}\n\n\tvar child = effect.first;\n\n\twhile (child !== null) {\n\t\tvar sibling = child.next;\n\t\tvar transparent = (child.f & EFFECT_TRANSPARENT) !== 0 || (child.f & BRANCH_EFFECT) !== 0;\n\t\t// TODO we don't need to call pause_children recursively with a linked list in place\n\t\t// it's slightly more involved though as we have to account for `transparent` changing\n\t\t// through the tree.\n\t\tpause_children(child, transitions, transparent ? local : false);\n\t\tchild = sibling;\n\t}\n}\n\n/**\n * The opposite of `pause_effect`. We call this if (for example)\n * `x` becomes falsy then truthy: `{#if x}...{/if}`\n * @param {Effect} effect\n */\nexport function resume_effect(effect) {\n\tresume_children(effect, true);\n}\n\n/**\n * @param {Effect} effect\n * @param {boolean} local\n */\nfunction resume_children(effect, local) {\n\tif ((effect.f & INERT) === 0) return;\n\teffect.f ^= INERT;\n\n\tvar child = effect.first;\n\n\twhile (child !== null) {\n\t\tvar sibling = child.next;\n\t\tvar transparent = (child.f & EFFECT_TRANSPARENT) !== 0 || (child.f & BRANCH_EFFECT) !== 0;\n\t\t// TODO we don't need to call resume_children recursively with a linked list in place\n\t\t// it's slightly more involved though as we have to account for `transparent` changing\n\t\t// through the tree.\n\t\tresume_children(child, transparent ? local : false);\n\t\tchild = sibling;\n\t}\n\n\tif (effect.transitions !== null) {\n\t\tfor (const transition of effect.transitions) {\n\t\t\tif (transition.is_global || local) {\n\t\t\t\ttransition.in();\n\t\t\t}\n\t\t}\n\t}\n}\n", "import { run_all } from '../../shared/utils.js';\n\n// Fallback for when requestIdleCallback is not available\nconst request_idle_callback =\n\ttypeof requestIdleCallback === 'undefined'\n\t\t? (/** @type {() => void} */ cb) => setTimeout(cb, 1)\n\t\t: requestIdleCallback;\n\n/** @type {Array<() => void>} */\nlet micro_tasks = [];\n\n/** @type {Array<() => void>} */\nlet idle_tasks = [];\n\nfunction run_micro_tasks() {\n\tvar tasks = micro_tasks;\n\tmicro_tasks = [];\n\trun_all(tasks);\n}\n\nfunction run_idle_tasks() {\n\tvar tasks = idle_tasks;\n\tidle_tasks = [];\n\trun_all(tasks);\n}\n\n/**\n * @param {() => void} fn\n */\nexport function queue_micro_task(fn) {\n\tif (micro_tasks.length === 0) {\n\t\tqueueMicrotask(run_micro_tasks);\n\t}\n\n\tmicro_tasks.push(fn);\n}\n\n/**\n * @param {() => void} fn\n */\nexport function queue_idle_task(fn) {\n\tif (idle_tasks.length === 0) {\n\t\trequest_idle_callback(run_idle_tasks);\n\t}\n\n\tidle_tasks.push(fn);\n}\n\n/**\n * Synchronously run any queued tasks.\n */\nexport function flush_tasks() {\n\tif (micro_tasks.length > 0) {\n\t\trun_micro_tasks();\n\t}\n\n\tif (idle_tasks.length > 0) {\n\t\trun_idle_tasks();\n\t}\n}\n", "/** @import { Effect } from '#client' */\n/** @import { Boundary } from './dom/blocks/boundary.js' */\nimport { DEV } from 'esm-env';\nimport { FILENAME } from '../../constants.js';\nimport { is_firefox } from './dom/operations.js';\nimport { BOUNDARY_EFFECT, EFFECT_RAN } from './constants.js';\nimport { define_property, get_descriptor } from '../shared/utils.js';\nimport { active_effect } from './runtime.js';\n\n/**\n * @param {unknown} error\n */\nexport function handle_error(error) {\n\tvar effect = /** @type {Effect} */ (active_effect);\n\n\tif (DEV && error instanceof Error) {\n\t\tadjust_error(error, effect);\n\t}\n\n\tif ((effect.f & EFFECT_RAN) === 0) {\n\t\t// if the error occurred while creating this subtree, we let it\n\t\t// bubble up until it hits a boundary that can handle it\n\t\tif ((effect.f & BOUNDARY_EFFECT) === 0) {\n\t\t\tthrow error;\n\t\t}\n\n\t\t// @ts-expect-error\n\t\teffect.fn(error);\n\t} else {\n\t\t// otherwise we bubble up the effect tree ourselves\n\t\tinvoke_error_boundary(error, effect);\n\t}\n}\n\n/**\n * @param {unknown} error\n * @param {Effect | null} effect\n */\nexport function invoke_error_boundary(error, effect) {\n\twhile (effect !== null) {\n\t\tif ((effect.f & BOUNDARY_EFFECT) !== 0) {\n\t\t\ttry {\n\t\t\t\t/** @type {Boundary} */ (effect.b).error(error);\n\t\t\t\treturn;\n\t\t\t} catch {}\n\t\t}\n\n\t\teffect = effect.parent;\n\t}\n\n\tthrow error;\n}\n\n/** @type {WeakSet<Error>} */\nconst adjusted_errors = new WeakSet();\n\n/**\n * Add useful information to the error message/stack in development\n * @param {Error} error\n * @param {Effect} effect\n */\nfunction adjust_error(error, effect) {\n\tif (adjusted_errors.has(error)) return;\n\tadjusted_errors.add(error);\n\n\tconst message_descriptor = get_descriptor(error, 'message');\n\n\t// if the message was already changed and it's not configurable we can't change it\n\t// or it will throw a different error swallowing the original error\n\tif (message_descriptor && !message_descriptor.configurable) return;\n\n\tvar indent = is_firefox ? '  ' : '\\t';\n\tvar component_stack = `\\n${indent}in ${effect.fn?.name || '<unknown>'}`;\n\tvar context = effect.ctx;\n\n\twhile (context !== null) {\n\t\tcomponent_stack += `\\n${indent}in ${context.function?.[FILENAME].split('/').pop()}`;\n\t\tcontext = context.p;\n\t}\n\n\tdefine_property(error, 'message', {\n\t\tvalue: error.message + `\\n${component_stack}\\n`\n\t});\n\n\tif (error.stack) {\n\t\t// Filter out internal modules\n\t\tdefine_property(error, 'stack', {\n\t\t\tvalue: error.stack\n\t\t\t\t.split('\\n')\n\t\t\t\t.filter((line) => !line.includes('svelte/src/internal'))\n\t\t\t\t.join('\\n')\n\t\t});\n\t}\n}\n", "/** @import { Derived, Effect, Reaction, Signal, Source, Value } from '#client' */\nimport { DEV } from 'esm-env';\nimport { define_property, get_descriptors, get_prototype_of, index_of } from '../shared/utils.js';\nimport {\n\tdestroy_block_effect_children,\n\tdestroy_effect_children,\n\texecute_effect_teardown,\n\tunlink_effect\n} from './reactivity/effects.js';\nimport {\n\tEFFECT,\n\tDIRTY,\n\tMAYBE_DIRTY,\n\tCLEAN,\n\tDERIVED,\n\tUNOWNED,\n\tDESTROYED,\n\tINERT,\n\tBRANCH_EFFECT,\n\tSTATE_SYMBOL,\n\tBLOCK_EFFECT,\n\tROOT_EFFECT,\n\tDISCONNECTED,\n\tEFFECT_IS_UPDATING,\n\tSTALE_REACTION,\n\tUSER_EFFECT\n} from './constants.js';\nimport { flush_tasks } from './dom/task.js';\nimport { internal_set, old_values } from './reactivity/sources.js';\nimport { destroy_derived_effects, update_derived } from './reactivity/deriveds.js';\nimport * as e from './errors.js';\n\nimport { tracing_mode_flag } from '../flags/index.js';\nimport { tracing_expressions, get_stack } from './dev/tracing.js';\nimport {\n\tcomponent_context,\n\tdev_current_component_function,\n\tdev_stack,\n\tis_runes,\n\tset_component_context,\n\tset_dev_current_component_function,\n\tset_dev_stack\n} from './context.js';\nimport { handle_error, invoke_error_boundary } from './error-handling.js';\n\nlet is_flushing = false;\n\n/** @type {Effect | null} */\nlet last_scheduled_effect = null;\n\nlet is_updating_effect = false;\n\nexport let is_destroying_effect = false;\n\n/** @param {boolean} value */\nexport function set_is_destroying_effect(value) {\n\tis_destroying_effect = value;\n}\n\n// Handle effect queues\n\n/** @type {Effect[]} */\nlet queued_root_effects = [];\n\n/** @type {Effect[]} Stack of effects, dev only */\nlet dev_effect_stack = [];\n// Handle signal reactivity tree dependencies and reactions\n\n/** @type {null | Reaction} */\nexport let active_reaction = null;\n\nexport let untracking = false;\n\n/** @param {null | Reaction} reaction */\nexport function set_active_reaction(reaction) {\n\tactive_reaction = reaction;\n}\n\n/** @type {null | Effect} */\nexport let active_effect = null;\n\n/** @param {null | Effect} effect */\nexport function set_active_effect(effect) {\n\tactive_effect = effect;\n}\n\n/**\n * When sources are created within a reaction, reading and writing\n * them within that reaction should not cause a re-run\n * @type {null | { reaction: Reaction, sources: Source[] }}\n */\nexport let source_ownership = null;\n\n/** @param {Value} value */\nexport function push_reaction_value(value) {\n\tif (active_reaction !== null && active_reaction.f & EFFECT_IS_UPDATING) {\n\t\tif (source_ownership === null) {\n\t\t\tsource_ownership = { reaction: active_reaction, sources: [value] };\n\t\t} else {\n\t\t\tsource_ownership.sources.push(value);\n\t\t}\n\t}\n}\n\n/**\n * The dependencies of the reaction that is currently being executed. In many cases,\n * the dependencies are unchanged between runs, and so this will be `null` unless\n * and until a new dependency is accessed — we track this via `skipped_deps`\n * @type {null | Value[]}\n */\nlet new_deps = null;\n\nlet skipped_deps = 0;\n\n/**\n * Tracks writes that the effect it's executed in doesn't listen to yet,\n * so that the dependency can be added to the effect later on if it then reads it\n * @type {null | Source[]}\n */\nexport let untracked_writes = null;\n\n/** @param {null | Source[]} value */\nexport function set_untracked_writes(value) {\n\tuntracked_writes = value;\n}\n\n/**\n * @type {number} Used by sources and deriveds for handling updates.\n * Version starts from 1 so that unowned deriveds differentiate between a created effect and a run one for tracing\n **/\nlet write_version = 1;\n\n/** @type {number} Used to version each read of a source of derived to avoid duplicating depedencies inside a reaction */\nlet read_version = 0;\n\n// If we are working with a get() chain that has no active container,\n// to prevent memory leaks, we skip adding the reaction.\nexport let skip_reaction = false;\n// Handle collecting all signals which are read during a specific time frame\n/** @type {Set<Value> | null} */\nexport let captured_signals = null;\n\n/** @param {Set<Value> | null} value */\nexport function set_captured_signals(value) {\n\tcaptured_signals = value;\n}\n\nexport function increment_write_version() {\n\treturn ++write_version;\n}\n\n/**\n * Determines whether a derived or effect is dirty.\n * If it is MAYBE_DIRTY, will set the status to CLEAN\n * @param {Reaction} reaction\n * @returns {boolean}\n */\nexport function check_dirtiness(reaction) {\n\tvar flags = reaction.f;\n\n\tif ((flags & DIRTY) !== 0) {\n\t\treturn true;\n\t}\n\n\tif ((flags & MAYBE_DIRTY) !== 0) {\n\t\tvar dependencies = reaction.deps;\n\t\tvar is_unowned = (flags & UNOWNED) !== 0;\n\n\t\tif (dependencies !== null) {\n\t\t\tvar i;\n\t\t\tvar dependency;\n\t\t\tvar is_disconnected = (flags & DISCONNECTED) !== 0;\n\t\t\tvar is_unowned_connected = is_unowned && active_effect !== null && !skip_reaction;\n\t\t\tvar length = dependencies.length;\n\n\t\t\t// If we are working with a disconnected or an unowned signal that is now connected (due to an active effect)\n\t\t\t// then we need to re-connect the reaction to the dependency\n\t\t\tif (is_disconnected || is_unowned_connected) {\n\t\t\t\tvar derived = /** @type {Derived} */ (reaction);\n\t\t\t\tvar parent = derived.parent;\n\n\t\t\t\tfor (i = 0; i < length; i++) {\n\t\t\t\t\tdependency = dependencies[i];\n\n\t\t\t\t\t// We always re-add all reactions (even duplicates) if the derived was\n\t\t\t\t\t// previously disconnected, however we don't if it was unowned as we\n\t\t\t\t\t// de-duplicate dependencies in that case\n\t\t\t\t\tif (is_disconnected || !dependency?.reactions?.includes(derived)) {\n\t\t\t\t\t\t(dependency.reactions ??= []).push(derived);\n\t\t\t\t\t}\n\t\t\t\t}\n\n\t\t\t\tif (is_disconnected) {\n\t\t\t\t\tderived.f ^= DISCONNECTED;\n\t\t\t\t}\n\t\t\t\t// If the unowned derived is now fully connected to the graph again (it's unowned and reconnected, has a parent\n\t\t\t\t// and the parent is not unowned), then we can mark it as connected again, removing the need for the unowned\n\t\t\t\t// flag\n\t\t\t\tif (is_unowned_connected && parent !== null && (parent.f & UNOWNED) === 0) {\n\t\t\t\t\tderived.f ^= UNOWNED;\n\t\t\t\t}\n\t\t\t}\n\n\t\t\tfor (i = 0; i < length; i++) {\n\t\t\t\tdependency = dependencies[i];\n\n\t\t\t\tif (check_dirtiness(/** @type {Derived} */ (dependency))) {\n\t\t\t\t\tupdate_derived(/** @type {Derived} */ (dependency));\n\t\t\t\t}\n\n\t\t\t\tif (dependency.wv > reaction.wv) {\n\t\t\t\t\treturn true;\n\t\t\t\t}\n\t\t\t}\n\t\t}\n\n\t\t// Unowned signals should never be marked as clean unless they\n\t\t// are used within an active_effect without skip_reaction\n\t\tif (!is_unowned || (active_effect !== null && !skip_reaction)) {\n\t\t\tset_signal_status(reaction, CLEAN);\n\t\t}\n\t}\n\n\treturn false;\n}\n\n/**\n * @param {Value} signal\n * @param {Effect} effect\n * @param {boolean} [root]\n */\nfunction schedule_possible_effect_self_invalidation(signal, effect, root = true) {\n\tvar reactions = signal.reactions;\n\tif (reactions === null) return;\n\n\tfor (var i = 0; i < reactions.length; i++) {\n\t\tvar reaction = reactions[i];\n\n\t\tif (\n\t\t\tsource_ownership?.reaction === active_reaction &&\n\t\t\tsource_ownership.sources.includes(signal)\n\t\t) {\n\t\t\tcontinue;\n\t\t}\n\n\t\tif ((reaction.f & DERIVED) !== 0) {\n\t\t\tschedule_possible_effect_self_invalidation(/** @type {Derived} */ (reaction), effect, false);\n\t\t} else if (effect === reaction) {\n\t\t\tif (root) {\n\t\t\t\tset_signal_status(reaction, DIRTY);\n\t\t\t} else if ((reaction.f & CLEAN) !== 0) {\n\t\t\t\tset_signal_status(reaction, MAYBE_DIRTY);\n\t\t\t}\n\t\t\tschedule_effect(/** @type {Effect} */ (reaction));\n\t\t}\n\t}\n}\n\n/** @param {Reaction} reaction */\nexport function update_reaction(reaction) {\n\tvar previous_deps = new_deps;\n\tvar previous_skipped_deps = skipped_deps;\n\tvar previous_untracked_writes = untracked_writes;\n\tvar previous_reaction = active_reaction;\n\tvar previous_skip_reaction = skip_reaction;\n\tvar previous_reaction_sources = source_ownership;\n\tvar previous_component_context = component_context;\n\tvar previous_untracking = untracking;\n\n\tvar flags = reaction.f;\n\n\tnew_deps = /** @type {null | Value[]} */ (null);\n\tskipped_deps = 0;\n\tuntracked_writes = null;\n\tskip_reaction =\n\t\t(flags & UNOWNED) !== 0 && (untracking || !is_updating_effect || active_reaction === null);\n\tactive_reaction = (flags & (BRANCH_EFFECT | ROOT_EFFECT)) === 0 ? reaction : null;\n\n\tsource_ownership = null;\n\tset_component_context(reaction.ctx);\n\tuntracking = false;\n\tread_version++;\n\n\treaction.f |= EFFECT_IS_UPDATING;\n\n\tif (reaction.ac !== null) {\n\t\treaction.ac.abort(STALE_REACTION);\n\t\treaction.ac = null;\n\t}\n\n\ttry {\n\t\tvar result = /** @type {Function} */ (0, reaction.fn)();\n\t\tvar deps = reaction.deps;\n\n\t\tif (new_deps !== null) {\n\t\t\tvar i;\n\n\t\t\tremove_reactions(reaction, skipped_deps);\n\n\t\t\tif (deps !== null && skipped_deps > 0) {\n\t\t\t\tdeps.length = skipped_deps + new_deps.length;\n\t\t\t\tfor (i = 0; i < new_deps.length; i++) {\n\t\t\t\t\tdeps[skipped_deps + i] = new_deps[i];\n\t\t\t\t}\n\t\t\t} else {\n\t\t\t\treaction.deps = deps = new_deps;\n\t\t\t}\n\n\t\t\tif (\n\t\t\t\t!skip_reaction ||\n\t\t\t\t// Deriveds that already have reactions can cleanup, so we still add them as reactions\n\t\t\t\t((flags & DERIVED) !== 0 &&\n\t\t\t\t\t/** @type {import('#client').Derived} */ (reaction).reactions !== null)\n\t\t\t) {\n\t\t\t\tfor (i = skipped_deps; i < deps.length; i++) {\n\t\t\t\t\t(deps[i].reactions ??= []).push(reaction);\n\t\t\t\t}\n\t\t\t}\n\t\t} else if (deps !== null && skipped_deps < deps.length) {\n\t\t\tremove_reactions(reaction, skipped_deps);\n\t\t\tdeps.length = skipped_deps;\n\t\t}\n\n\t\t// If we're inside an effect and we have untracked writes, then we need to\n\t\t// ensure that if any of those untracked writes result in re-invalidation\n\t\t// of the current effect, then that happens accordingly\n\t\tif (\n\t\t\tis_runes() &&\n\t\t\tuntracked_writes !== null &&\n\t\t\t!untracking &&\n\t\t\tdeps !== null &&\n\t\t\t(reaction.f & (DERIVED | MAYBE_DIRTY | DIRTY)) === 0\n\t\t) {\n\t\t\tfor (i = 0; i < /** @type {Source[]} */ (untracked_writes).length; i++) {\n\t\t\t\tschedule_possible_effect_self_invalidation(\n\t\t\t\t\tuntracked_writes[i],\n\t\t\t\t\t/** @type {Effect} */ (reaction)\n\t\t\t\t);\n\t\t\t}\n\t\t}\n\n\t\t// If we are returning to an previous reaction then\n\t\t// we need to increment the read version to ensure that\n\t\t// any dependencies in this reaction aren't marked with\n\t\t// the same version\n\t\tif (previous_reaction !== null && previous_reaction !== reaction) {\n\t\t\tread_version++;\n\n\t\t\tif (untracked_writes !== null) {\n\t\t\t\tif (previous_untracked_writes === null) {\n\t\t\t\t\tprevious_untracked_writes = untracked_writes;\n\t\t\t\t} else {\n\t\t\t\t\tprevious_untracked_writes.push(.../** @type {Source[]} */ (untracked_writes));\n\t\t\t\t}\n\t\t\t}\n\t\t}\n\n\t\treturn result;\n\t} catch (error) {\n\t\thandle_error(error);\n\t} finally {\n\t\tnew_deps = previous_deps;\n\t\tskipped_deps = previous_skipped_deps;\n\t\tuntracked_writes = previous_untracked_writes;\n\t\tactive_reaction = previous_reaction;\n\t\tskip_reaction = previous_skip_reaction;\n\t\tsource_ownership = previous_reaction_sources;\n\t\tset_component_context(previous_component_context);\n\t\tuntracking = previous_untracking;\n\n\t\treaction.f ^= EFFECT_IS_UPDATING;\n\t}\n}\n\n/**\n * @template V\n * @param {Reaction} signal\n * @param {Value<V>} dependency\n * @returns {void}\n */\nfunction remove_reaction(signal, dependency) {\n\tlet reactions = dependency.reactions;\n\tif (reactions !== null) {\n\t\tvar index = index_of.call(reactions, signal);\n\t\tif (index !== -1) {\n\t\t\tvar new_length = reactions.length - 1;\n\t\t\tif (new_length === 0) {\n\t\t\t\treactions = dependency.reactions = null;\n\t\t\t} else {\n\t\t\t\t// Swap with last element and then remove.\n\t\t\t\treactions[index] = reactions[new_length];\n\t\t\t\treactions.pop();\n\t\t\t}\n\t\t}\n\t}\n\t// If the derived has no reactions, then we can disconnect it from the graph,\n\t// allowing it to either reconnect in the future, or be GC'd by the VM.\n\tif (\n\t\treactions === null &&\n\t\t(dependency.f & DERIVED) !== 0 &&\n\t\t// Destroying a child effect while updating a parent effect can cause a dependency to appear\n\t\t// to be unused, when in fact it is used by the currently-updating parent. Checking `new_deps`\n\t\t// allows us to skip the expensive work of disconnecting and immediately reconnecting it\n\t\t(new_deps === null || !new_deps.includes(dependency))\n\t) {\n\t\tset_signal_status(dependency, MAYBE_DIRTY);\n\t\t// If we are working with a derived that is owned by an effect, then mark it as being\n\t\t// disconnected.\n\t\tif ((dependency.f & (UNOWNED | DISCONNECTED)) === 0) {\n\t\t\tdependency.f ^= DISCONNECTED;\n\t\t}\n\t\t// Disconnect any reactions owned by this reaction\n\t\tdestroy_derived_effects(/** @type {Derived} **/ (dependency));\n\t\tremove_reactions(/** @type {Derived} **/ (dependency), 0);\n\t}\n}\n\n/**\n * @param {Reaction} signal\n * @param {number} start_index\n * @returns {void}\n */\nexport function remove_reactions(signal, start_index) {\n\tvar dependencies = signal.deps;\n\tif (dependencies === null) return;\n\n\tfor (var i = start_index; i < dependencies.length; i++) {\n\t\tremove_reaction(signal, dependencies[i]);\n\t}\n}\n\n/**\n * @param {Effect} effect\n * @returns {void}\n */\nexport function update_effect(effect) {\n\tvar flags = effect.f;\n\n\tif ((flags & DESTROYED) !== 0) {\n\t\treturn;\n\t}\n\n\tset_signal_status(effect, CLEAN);\n\n\tvar previous_effect = active_effect;\n\tvar was_updating_effect = is_updating_effect;\n\n\tactive_effect = effect;\n\tis_updating_effect = true;\n\n\tif (DEV) {\n\t\tvar previous_component_fn = dev_current_component_function;\n\t\tset_dev_current_component_function(effect.component_function);\n\t\tvar previous_stack = /** @type {any} */ (dev_stack);\n\t\t// only block effects have a dev stack, keep the current one otherwise\n\t\tset_dev_stack(effect.dev_stack ?? dev_stack);\n\t}\n\n\ttry {\n\t\tif ((flags & BLOCK_EFFECT) !== 0) {\n\t\t\tdestroy_block_effect_children(effect);\n\t\t} else {\n\t\t\tdestroy_effect_children(effect);\n\t\t}\n\n\t\texecute_effect_teardown(effect);\n\t\tvar teardown = update_reaction(effect);\n\t\teffect.teardown = typeof teardown === 'function' ? teardown : null;\n\t\teffect.wv = write_version;\n\n\t\t// In DEV, increment versions of any sources that were written to during the effect,\n\t\t// so that they are correctly marked as dirty when the effect re-runs\n\t\tif (DEV && tracing_mode_flag && (effect.f & DIRTY) !== 0 && effect.deps !== null) {\n\t\t\tfor (var dep of effect.deps) {\n\t\t\t\tif (dep.set_during_effect) {\n\t\t\t\t\tdep.wv = increment_write_version();\n\t\t\t\t\tdep.set_during_effect = false;\n\t\t\t\t}\n\t\t\t}\n\t\t}\n\n\t\tif (DEV) {\n\t\t\tdev_effect_stack.push(effect);\n\t\t}\n\t} finally {\n\t\tis_updating_effect = was_updating_effect;\n\t\tactive_effect = previous_effect;\n\n\t\tif (DEV) {\n\t\t\tset_dev_current_component_function(previous_component_fn);\n\t\t\tset_dev_stack(previous_stack);\n\t\t}\n\t}\n}\n\nfunction log_effect_stack() {\n\t// eslint-disable-next-line no-console\n\tconsole.error(\n\t\t'Last ten effects were: ',\n\t\tdev_effect_stack.slice(-10).map((d) => d.fn)\n\t);\n\tdev_effect_stack = [];\n}\n\nfunction infinite_loop_guard() {\n\ttry {\n\t\te.effect_update_depth_exceeded();\n\t} catch (error) {\n\t\tif (DEV) {\n\t\t\t// stack is garbage, ignore. Instead add a console.error message.\n\t\t\tdefine_property(error, 'stack', {\n\t\t\t\tvalue: ''\n\t\t\t});\n\t\t}\n\t\t// Try and handle the error so it can be caught at a boundary, that's\n\t\t// if there's an effect available from when it was last scheduled\n\t\tif (last_scheduled_effect !== null) {\n\t\t\tif (DEV) {\n\t\t\t\ttry {\n\t\t\t\t\tinvoke_error_boundary(error, last_scheduled_effect);\n\t\t\t\t} catch (e) {\n\t\t\t\t\t// Only log the effect stack if the error is re-thrown\n\t\t\t\t\tlog_effect_stack();\n\t\t\t\t\tthrow e;\n\t\t\t\t}\n\t\t\t} else {\n\t\t\t\tinvoke_error_boundary(error, last_scheduled_effect);\n\t\t\t}\n\t\t} else {\n\t\t\tif (DEV) {\n\t\t\t\tlog_effect_stack();\n\t\t\t}\n\t\t\tthrow error;\n\t\t}\n\t}\n}\n\nfunction flush_queued_root_effects() {\n\tvar was_updating_effect = is_updating_effect;\n\n\ttry {\n\t\tvar flush_count = 0;\n\t\tis_updating_effect = true;\n\n\t\twhile (queued_root_effects.length > 0) {\n\t\t\tif (flush_count++ > 1000) {\n\t\t\t\tinfinite_loop_guard();\n\t\t\t}\n\n\t\t\tvar root_effects = queued_root_effects;\n\t\t\tvar length = root_effects.length;\n\n\t\t\tqueued_root_effects = [];\n\n\t\t\tfor (var i = 0; i < length; i++) {\n\t\t\t\tvar collected_effects = process_effects(root_effects[i]);\n\t\t\t\tflush_queued_effects(collected_effects);\n\t\t\t}\n\t\t\told_values.clear();\n\t\t}\n\t} finally {\n\t\tis_flushing = false;\n\t\tis_updating_effect = was_updating_effect;\n\n\t\tlast_scheduled_effect = null;\n\t\tif (DEV) {\n\t\t\tdev_effect_stack = [];\n\t\t}\n\t}\n}\n\n/**\n * @param {Array<Effect>} effects\n * @returns {void}\n */\nfunction flush_queued_effects(effects) {\n\tvar length = effects.length;\n\tif (length === 0) return;\n\n\tfor (var i = 0; i < length; i++) {\n\t\tvar effect = effects[i];\n\n\t\tif ((effect.f & (DESTROYED | INERT)) === 0) {\n\t\t\tif (check_dirtiness(effect)) {\n\t\t\t\tvar wv = write_version;\n\n\t\t\t\tupdate_effect(effect);\n\n\t\t\t\t// Effects with no dependencies or teardown do not get added to the effect tree.\n\t\t\t\t// Deferred effects (e.g. `$effect(...)`) _are_ added to the tree because we\n\t\t\t\t// don't know if we need to keep them until they are executed. Doing the check\n\t\t\t\t// here (rather than in `update_effect`) allows us to skip the work for\n\t\t\t\t// immediate effects.\n\t\t\t\tif (effect.deps === null && effect.first === null && effect.nodes_start === null) {\n\t\t\t\t\tif (effect.teardown === null) {\n\t\t\t\t\t\t// remove this effect from the graph\n\t\t\t\t\t\tunlink_effect(effect);\n\t\t\t\t\t} else {\n\t\t\t\t\t\t// keep the effect in the graph, but free up some memory\n\t\t\t\t\t\teffect.fn = null;\n\t\t\t\t\t}\n\t\t\t\t}\n\n\t\t\t\t// if state is written in a user effect, abort and re-schedule, lest we run\n\t\t\t\t// effects that should be removed as a result of the state change\n\t\t\t\tif (write_version > wv && (effect.f & USER_EFFECT) !== 0) {\n\t\t\t\t\tbreak;\n\t\t\t\t}\n\t\t\t}\n\t\t}\n\t}\n\n\tfor (; i < length; i += 1) {\n\t\tschedule_effect(effects[i]);\n\t}\n}\n\n/**\n * @param {Effect} signal\n * @returns {void}\n */\nexport function schedule_effect(signal) {\n\tif (!is_flushing) {\n\t\tis_flushing = true;\n\t\tqueueMicrotask(flush_queued_root_effects);\n\t}\n\n\tvar effect = (last_scheduled_effect = signal);\n\n\twhile (effect.parent !== null) {\n\t\teffect = effect.parent;\n\t\tvar flags = effect.f;\n\n\t\tif ((flags & (ROOT_EFFECT | BRANCH_EFFECT)) !== 0) {\n\t\t\tif ((flags & CLEAN) === 0) return;\n\t\t\teffect.f ^= CLEAN;\n\t\t}\n\t}\n\n\tqueued_root_effects.push(effect);\n}\n\n/**\n *\n * This function both runs render effects and collects user effects in topological order\n * from the starting effect passed in. Effects will be collected when they match the filtered\n * bitwise flag passed in only. The collected effects array will be populated with all the user\n * effects to be flushed.\n *\n * @param {Effect} root\n * @returns {Effect[]}\n */\nfunction process_effects(root) {\n\t/** @type {Effect[]} */\n\tvar effects = [];\n\n\t/** @type {Effect | null} */\n\tvar effect = root;\n\n\twhile (effect !== null) {\n\t\tvar flags = effect.f;\n\t\tvar is_branch = (flags & (BRANCH_EFFECT | ROOT_EFFECT)) !== 0;\n\t\tvar is_skippable_branch = is_branch && (flags & CLEAN) !== 0;\n\n\t\tif (!is_skippable_branch && (flags & INERT) === 0) {\n\t\t\tif ((flags & EFFECT) !== 0) {\n\t\t\t\teffects.push(effect);\n\t\t\t} else if (is_branch) {\n\t\t\t\teffect.f ^= CLEAN;\n\t\t\t} else {\n\t\t\t\tif (check_dirtiness(effect)) {\n\t\t\t\t\tupdate_effect(effect);\n\t\t\t\t}\n\t\t\t}\n\n\t\t\t/** @type {Effect | null} */\n\t\t\tvar child = effect.first;\n\n\t\t\tif (child !== null) {\n\t\t\t\teffect = child;\n\t\t\t\tcontinue;\n\t\t\t}\n\t\t}\n\n\t\tvar parent = effect.parent;\n\t\teffect = effect.next;\n\n\t\twhile (effect === null && parent !== null) {\n\t\t\teffect = parent.next;\n\t\t\tparent = parent.parent;\n\t\t}\n\t}\n\n\treturn effects;\n}\n\n/**\n * Synchronously flush any pending updates.\n * Returns void if no callback is provided, otherwise returns the result of calling the callback.\n * @template [T=void]\n * @param {(() => T) | undefined} [fn]\n * @returns {T}\n */\nexport function flushSync(fn) {\n\tvar result;\n\n\tif (fn) {\n\t\tis_flushing = true;\n\t\tflush_queued_root_effects();\n\n\t\tis_flushing = true;\n\t\tresult = fn();\n\t}\n\n\twhile (true) {\n\t\tflush_tasks();\n\n\t\tif (queued_root_effects.length === 0) {\n\t\t\t// this would be reset in `flush_queued_root_effects` but since we are early returning here,\n\t\t\t// we need to reset it here as well in case the first time there's 0 queued root effects\n\t\t\tis_flushing = false;\n\t\t\tlast_scheduled_effect = null;\n\t\t\tif (DEV) {\n\t\t\t\tdev_effect_stack = [];\n\t\t\t}\n\t\t\treturn /** @type {T} */ (result);\n\t\t}\n\n\t\tis_flushing = true;\n\t\tflush_queued_root_effects();\n\t}\n}\n\n/**\n * Returns a promise that resolves once any pending state changes have been applied.\n * @returns {Promise<void>}\n */\nexport async function tick() {\n\tawait Promise.resolve();\n\t// By calling flushSync we guarantee that any pending state changes are applied after one tick.\n\t// TODO look into whether we can make flushing subsequent updates synchronously in the future.\n\tflushSync();\n}\n\n/**\n * @template V\n * @param {Value<V>} signal\n * @returns {V}\n */\nexport function get(signal) {\n\tvar flags = signal.f;\n\tvar is_derived = (flags & DERIVED) !== 0;\n\n\tif (captured_signals !== null) {\n\t\tcaptured_signals.add(signal);\n\t}\n\n\t// Register the dependency on the current reaction signal.\n\tif (active_reaction !== null && !untracking) {\n\t\tif (\n\t\t\tsource_ownership?.reaction !== active_reaction ||\n\t\t\t!source_ownership?.sources.includes(signal)\n\t\t) {\n\t\t\tvar deps = active_reaction.deps;\n\t\t\tif (signal.rv < read_version) {\n\t\t\t\tsignal.rv = read_version;\n\t\t\t\t// If the signal is accessing the same dependencies in the same\n\t\t\t\t// order as it did last time, increment `skipped_deps`\n\t\t\t\t// rather than updating `new_deps`, which creates GC cost\n\t\t\t\tif (new_deps === null && deps !== null && deps[skipped_deps] === signal) {\n\t\t\t\t\tskipped_deps++;\n\t\t\t\t} else if (new_deps === null) {\n\t\t\t\t\tnew_deps = [signal];\n\t\t\t\t} else if (!skip_reaction || !new_deps.includes(signal)) {\n\t\t\t\t\t// Normally we can push duplicated dependencies to `new_deps`, but if we're inside\n\t\t\t\t\t// an unowned derived because skip_reaction is true, then we need to ensure that\n\t\t\t\t\t// we don't have duplicates\n\t\t\t\t\tnew_deps.push(signal);\n\t\t\t\t}\n\t\t\t}\n\t\t}\n\t} else if (\n\t\tis_derived &&\n\t\t/** @type {Derived} */ (signal).deps === null &&\n\t\t/** @type {Derived} */ (signal).effects === null\n\t) {\n\t\tvar derived = /** @type {Derived} */ (signal);\n\t\tvar parent = derived.parent;\n\n\t\tif (parent !== null && (parent.f & UNOWNED) === 0) {\n\t\t\t// If the derived is owned by another derived then mark it as unowned\n\t\t\t// as the derived value might have been referenced in a different context\n\t\t\t// since and thus its parent might not be its true owner anymore\n\t\t\tderived.f ^= UNOWNED;\n\t\t}\n\t}\n\n\tif (is_derived) {\n\t\tderived = /** @type {Derived} */ (signal);\n\n\t\tif (check_dirtiness(derived)) {\n\t\t\tupdate_derived(derived);\n\t\t}\n\t}\n\n\tif (\n\t\tDEV &&\n\t\ttracing_mode_flag &&\n\t\t!untracking &&\n\t\ttracing_expressions !== null &&\n\t\tactive_reaction !== null &&\n\t\ttracing_expressions.reaction === active_reaction\n\t) {\n\t\t// Used when mapping state between special blocks like `each`\n\t\tif (signal.trace) {\n\t\t\tsignal.trace();\n\t\t} else {\n\t\t\tvar trace = get_stack('TracedAt');\n\n\t\t\tif (trace) {\n\t\t\t\tvar entry = tracing_expressions.entries.get(signal);\n\n\t\t\t\tif (entry === undefined) {\n\t\t\t\t\tentry = { traces: [] };\n\t\t\t\t\ttracing_expressions.entries.set(signal, entry);\n\t\t\t\t}\n\n\t\t\t\tvar last = entry.traces[entry.traces.length - 1];\n\n\t\t\t\t// traces can be duplicated, e.g. by `snapshot` invoking both\n\t\t\t\t// both `getOwnPropertyDescriptor` and `get` traps at once\n\t\t\t\tif (trace.stack !== last?.stack) {\n\t\t\t\t\tentry.traces.push(trace);\n\t\t\t\t}\n\t\t\t}\n\t\t}\n\t}\n\n\tif (is_destroying_effect && old_values.has(signal)) {\n\t\treturn old_values.get(signal);\n\t}\n\n\treturn signal.v;\n}\n\n/**\n * Like `get`, but checks for `undefined`. Used for `var` declarations because they can be accessed before being declared\n * @template V\n * @param {Value<V> | undefined} signal\n * @returns {V | undefined}\n */\nexport function safe_get(signal) {\n\treturn signal && get(signal);\n}\n\n/**\n * Capture an array of all the signals that are read when `fn` is called\n * @template T\n * @param {() => T} fn\n */\nfunction capture_signals(fn) {\n\tvar previous_captured_signals = captured_signals;\n\tcaptured_signals = new Set();\n\n\tvar captured = captured_signals;\n\tvar signal;\n\n\ttry {\n\t\tuntrack(fn);\n\t\tif (previous_captured_signals !== null) {\n\t\t\tfor (signal of captured_signals) {\n\t\t\t\tprevious_captured_signals.add(signal);\n\t\t\t}\n\t\t}\n\t} finally {\n\t\tcaptured_signals = previous_captured_signals;\n\t}\n\n\treturn captured;\n}\n\n/**\n * Invokes a function and captures all signals that are read during the invocation,\n * then invalidates them.\n * @param {() => any} fn\n */\nexport function invalidate_inner_signals(fn) {\n\tvar captured = capture_signals(() => untrack(fn));\n\n\tfor (var signal of captured) {\n\t\tinternal_set(signal, signal.v);\n\t}\n}\n\n/**\n * When used inside a [`$derived`](https://svelte.dev/docs/svelte/$derived) or [`$effect`](https://svelte.dev/docs/svelte/$effect),\n * any state read inside `fn` will not be treated as a dependency.\n *\n * ```ts\n * $effect(() => {\n *   // this will run when `data` changes, but not when `time` changes\n *   save(data, {\n *     timestamp: untrack(() => time)\n *   });\n * });\n * ```\n * @template T\n * @param {() => T} fn\n * @returns {T}\n */\nexport function untrack(fn) {\n\tvar previous_untracking = untracking;\n\ttry {\n\t\tuntracking = true;\n\t\treturn fn();\n\t} finally {\n\t\tuntracking = previous_untracking;\n\t}\n}\n\nconst STATUS_MASK = ~(DIRTY | MAYBE_DIRTY | CLEAN);\n\n/**\n * @param {Signal} signal\n * @param {number} status\n * @returns {void}\n */\nexport function set_signal_status(signal, status) {\n\tsignal.f = (signal.f & STATUS_MASK) | status;\n}\n\n/**\n * @param {Record<string, unknown>} obj\n * @param {string[]} keys\n * @returns {Record<string, unknown>}\n */\nexport function exclude_from_object(obj, keys) {\n\t/** @type {Record<string, unknown>} */\n\tvar result = {};\n\n\tfor (var key in obj) {\n\t\tif (!keys.includes(key)) {\n\t\t\tresult[key] = obj[key];\n\t\t}\n\t}\n\n\treturn result;\n}\n\n/**\n * Possibly traverse an object and read all its properties so that they're all reactive in case this is `$state`.\n * Does only check first level of an object for performance reasons (heuristic should be good for 99% of all cases).\n * @param {any} value\n * @returns {void}\n */\nexport function deep_read_state(value) {\n\tif (typeof value !== 'object' || !value || value instanceof EventTarget) {\n\t\treturn;\n\t}\n\n\tif (STATE_SYMBOL in value) {\n\t\tdeep_read(value);\n\t} else if (!Array.isArray(value)) {\n\t\tfor (let key in value) {\n\t\t\tconst prop = value[key];\n\t\t\tif (typeof prop === 'object' && prop && STATE_SYMBOL in prop) {\n\t\t\t\tdeep_read(prop);\n\t\t\t}\n\t\t}\n\t}\n}\n\n/**\n * Deeply traverse an object and read all its properties\n * so that they're all reactive in case this is `$state`\n * @param {any} value\n * @param {Set<any>} visited\n * @returns {void}\n */\nexport function deep_read(value, visited = new Set()) {\n\tif (\n\t\ttypeof value === 'object' &&\n\t\tvalue !== null &&\n\t\t// We don't want to traverse DOM elements\n\t\t!(value instanceof EventTarget) &&\n\t\t!visited.has(value)\n\t) {\n\t\tvisited.add(value);\n\t\t// When working with a possible SvelteDate, this\n\t\t// will ensure we capture changes to it.\n\t\tif (value instanceof Date) {\n\t\t\tvalue.getTime();\n\t\t}\n\t\tfor (let key in value) {\n\t\t\ttry {\n\t\t\t\tdeep_read(value[key], visited);\n\t\t\t} catch (e) {\n\t\t\t\t// continue\n\t\t\t}\n\t\t}\n\t\tconst proto = get_prototype_of(value);\n\t\tif (\n\t\t\tproto !== Object.prototype &&\n\t\t\tproto !== Array.prototype &&\n\t\t\tproto !== Map.prototype &&\n\t\t\tproto !== Set.prototype &&\n\t\t\tproto !== Date.prototype\n\t\t) {\n\t\t\tconst descriptors = get_descriptors(proto);\n\t\t\tfor (let key in descriptors) {\n\t\t\t\tconst get = descriptors[key].get;\n\t\t\t\tif (get) {\n\t\t\t\t\ttry {\n\t\t\t\t\t\tget.call(value);\n\t\t\t\t\t} catch (e) {\n\t\t\t\t\t\t// continue\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t}\n\t\t}\n\t}\n}\n", "/** @import { Source } from '#client' */\nimport { DEV } from 'esm-env';\nimport { get, active_effect, active_reaction, set_active_reaction } from './runtime.js';\nimport {\n\tarray_prototype,\n\tget_descriptor,\n\tget_prototype_of,\n\tis_array,\n\tobject_prototype\n} from '../shared/utils.js';\nimport { state as source, set } from './reactivity/sources.js';\nimport { PROXY_PATH_SYMBOL, STATE_SYMBOL } from '#client/constants';\nimport { UNINITIALIZED } from '../../constants.js';\nimport * as e from './errors.js';\nimport { get_stack, tag } from './dev/tracing.js';\nimport { tracing_mode_flag } from '../flags/index.js';\n\n// TODO move all regexes into shared module?\nconst regex_is_valid_identifier = /^[a-zA-Z_$][a-zA-Z_$0-9]*$/;\n\n/**\n * @template T\n * @param {T} value\n * @returns {T}\n */\nexport function proxy(value) {\n\t// if non-proxyable, or is already a proxy, return `value`\n\tif (typeof value !== 'object' || value === null || STATE_SYMBOL in value) {\n\t\treturn value;\n\t}\n\n\tconst prototype = get_prototype_of(value);\n\n\tif (prototype !== object_prototype && prototype !== array_prototype) {\n\t\treturn value;\n\t}\n\n\t/** @type {Map<any, Source<any>>} */\n\tvar sources = new Map();\n\tvar is_proxied_array = is_array(value);\n\tvar version = source(0);\n\n\tvar stack = DEV && tracing_mode_flag ? get_stack('CreatedAt') : null;\n\tvar reaction = active_reaction;\n\n\t/**\n\t * Executes the proxy in the context of the reaction it was originally created in, if any\n\t * @template T\n\t * @param {() => T} fn\n\t */\n\tvar with_parent = (fn) => {\n\t\tvar previous_reaction = active_reaction;\n\t\tset_active_reaction(reaction);\n\n\t\t/** @type {T} */\n\t\tvar result = fn();\n\n\t\tset_active_reaction(previous_reaction);\n\t\treturn result;\n\t};\n\n\tif (is_proxied_array) {\n\t\t// We need to create the length source eagerly to ensure that\n\t\t// mutations to the array are properly synced with our proxy\n\t\tsources.set('length', source(/** @type {any[]} */ (value).length, stack));\n\t}\n\n\t/** Used in dev for $inspect.trace() */\n\tvar path = '';\n\n\t/** @param {string} new_path */\n\tfunction update_path(new_path) {\n\t\tpath = new_path;\n\n\t\ttag(version, `${path} version`);\n\n\t\t// rename all child sources and child proxies\n\t\tfor (const [prop, source] of sources) {\n\t\t\ttag(source, get_label(path, prop));\n\t\t}\n\t}\n\n\treturn new Proxy(/** @type {any} */ (value), {\n\t\tdefineProperty(_, prop, descriptor) {\n\t\t\tif (\n\t\t\t\t!('value' in descriptor) ||\n\t\t\t\tdescriptor.configurable === false ||\n\t\t\t\tdescriptor.enumerable === false ||\n\t\t\t\tdescriptor.writable === false\n\t\t\t) {\n\t\t\t\t// we disallow non-basic descriptors, because unless they are applied to the\n\t\t\t\t// target object — which we avoid, so that state can be forked — we will run\n\t\t\t\t// afoul of the various invariants\n\t\t\t\t// https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Proxy/Proxy/getOwnPropertyDescriptor#invariants\n\t\t\t\te.state_descriptors_fixed();\n\t\t\t}\n\t\t\tvar s = sources.get(prop);\n\t\t\tif (s === undefined) {\n\t\t\t\ts = with_parent(() => {\n\t\t\t\t\tvar s = source(descriptor.value, stack);\n\t\t\t\t\tsources.set(prop, s);\n\t\t\t\t\tif (DEV && typeof prop === 'string') {\n\t\t\t\t\t\ttag(s, get_label(path, prop));\n\t\t\t\t\t}\n\t\t\t\t\treturn s;\n\t\t\t\t});\n\t\t\t} else {\n\t\t\t\tset(s, descriptor.value, true);\n\t\t\t}\n\n\t\t\treturn true;\n\t\t},\n\n\t\tdeleteProperty(target, prop) {\n\t\t\tvar s = sources.get(prop);\n\n\t\t\tif (s === undefined) {\n\t\t\t\tif (prop in target) {\n\t\t\t\t\tconst s = with_parent(() => source(UNINITIALIZED, stack));\n\t\t\t\t\tsources.set(prop, s);\n\t\t\t\t\tupdate_version(version);\n\n\t\t\t\t\tif (DEV) {\n\t\t\t\t\t\ttag(s, get_label(path, prop));\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t} else {\n\t\t\t\t// When working with arrays, we need to also ensure we update the length when removing\n\t\t\t\t// an indexed property\n\t\t\t\tif (is_proxied_array && typeof prop === 'string') {\n\t\t\t\t\tvar ls = /** @type {Source<number>} */ (sources.get('length'));\n\t\t\t\t\tvar n = Number(prop);\n\n\t\t\t\t\tif (Number.isInteger(n) && n < ls.v) {\n\t\t\t\t\t\tset(ls, n);\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t\tset(s, UNINITIALIZED);\n\t\t\t\tupdate_version(version);\n\t\t\t}\n\n\t\t\treturn true;\n\t\t},\n\n\t\tget(target, prop, receiver) {\n\t\t\tif (prop === STATE_SYMBOL) {\n\t\t\t\treturn value;\n\t\t\t}\n\n\t\t\tif (DEV && prop === PROXY_PATH_SYMBOL) {\n\t\t\t\treturn update_path;\n\t\t\t}\n\n\t\t\tvar s = sources.get(prop);\n\t\t\tvar exists = prop in target;\n\n\t\t\t// create a source, but only if it's an own property and not a prototype property\n\t\t\tif (s === undefined && (!exists || get_descriptor(target, prop)?.writable)) {\n\t\t\t\ts = with_parent(() => {\n\t\t\t\t\tvar p = proxy(exists ? target[prop] : UNINITIALIZED);\n\t\t\t\t\tvar s = source(p, stack);\n\n\t\t\t\t\tif (DEV) {\n\t\t\t\t\t\ttag(s, get_label(path, prop));\n\t\t\t\t\t}\n\n\t\t\t\t\treturn s;\n\t\t\t\t});\n\n\t\t\t\tsources.set(prop, s);\n\t\t\t}\n\n\t\t\tif (s !== undefined) {\n\t\t\t\tvar v = get(s);\n\t\t\t\treturn v === UNINITIALIZED ? undefined : v;\n\t\t\t}\n\n\t\t\treturn Reflect.get(target, prop, receiver);\n\t\t},\n\n\t\tgetOwnPropertyDescriptor(target, prop) {\n\t\t\tvar descriptor = Reflect.getOwnPropertyDescriptor(target, prop);\n\n\t\t\tif (descriptor && 'value' in descriptor) {\n\t\t\t\tvar s = sources.get(prop);\n\t\t\t\tif (s) descriptor.value = get(s);\n\t\t\t} else if (descriptor === undefined) {\n\t\t\t\tvar source = sources.get(prop);\n\t\t\t\tvar value = source?.v;\n\n\t\t\t\tif (source !== undefined && value !== UNINITIALIZED) {\n\t\t\t\t\treturn {\n\t\t\t\t\t\tenumerable: true,\n\t\t\t\t\t\tconfigurable: true,\n\t\t\t\t\t\tvalue,\n\t\t\t\t\t\twritable: true\n\t\t\t\t\t};\n\t\t\t\t}\n\t\t\t}\n\n\t\t\treturn descriptor;\n\t\t},\n\n\t\thas(target, prop) {\n\t\t\tif (prop === STATE_SYMBOL) {\n\t\t\t\treturn true;\n\t\t\t}\n\n\t\t\tvar s = sources.get(prop);\n\t\t\tvar has = (s !== undefined && s.v !== UNINITIALIZED) || Reflect.has(target, prop);\n\n\t\t\tif (\n\t\t\t\ts !== undefined ||\n\t\t\t\t(active_effect !== null && (!has || get_descriptor(target, prop)?.writable))\n\t\t\t) {\n\t\t\t\tif (s === undefined) {\n\t\t\t\t\ts = with_parent(() => {\n\t\t\t\t\t\tvar p = has ? proxy(target[prop]) : UNINITIALIZED;\n\t\t\t\t\t\tvar s = source(p, stack);\n\n\t\t\t\t\t\tif (DEV) {\n\t\t\t\t\t\t\ttag(s, get_label(path, prop));\n\t\t\t\t\t\t}\n\n\t\t\t\t\t\treturn s;\n\t\t\t\t\t});\n\n\t\t\t\t\tsources.set(prop, s);\n\t\t\t\t}\n\n\t\t\t\tvar value = get(s);\n\t\t\t\tif (value === UNINITIALIZED) {\n\t\t\t\t\treturn false;\n\t\t\t\t}\n\t\t\t}\n\n\t\t\treturn has;\n\t\t},\n\n\t\tset(target, prop, value, receiver) {\n\t\t\tvar s = sources.get(prop);\n\t\t\tvar has = prop in target;\n\n\t\t\t// variable.length = value -> clear all signals with index >= value\n\t\t\tif (is_proxied_array && prop === 'length') {\n\t\t\t\tfor (var i = value; i < /** @type {Source<number>} */ (s).v; i += 1) {\n\t\t\t\t\tvar other_s = sources.get(i + '');\n\t\t\t\t\tif (other_s !== undefined) {\n\t\t\t\t\t\tset(other_s, UNINITIALIZED);\n\t\t\t\t\t} else if (i in target) {\n\t\t\t\t\t\t// If the item exists in the original, we need to create a uninitialized source,\n\t\t\t\t\t\t// else a later read of the property would result in a source being created with\n\t\t\t\t\t\t// the value of the original item at that index.\n\t\t\t\t\t\tother_s = with_parent(() => source(UNINITIALIZED, stack));\n\t\t\t\t\t\tsources.set(i + '', other_s);\n\n\t\t\t\t\t\tif (DEV) {\n\t\t\t\t\t\t\ttag(other_s, get_label(path, i));\n\t\t\t\t\t\t}\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t}\n\n\t\t\t// If we haven't yet created a source for this property, we need to ensure\n\t\t\t// we do so otherwise if we read it later, then the write won't be tracked and\n\t\t\t// the heuristics of effects will be different vs if we had read the proxied\n\t\t\t// object property before writing to that property.\n\t\t\tif (s === undefined) {\n\t\t\t\tif (!has || get_descriptor(target, prop)?.writable) {\n\t\t\t\t\ts = with_parent(() => source(undefined, stack));\n\t\t\t\t\tset(s, proxy(value));\n\n\t\t\t\t\tsources.set(prop, s);\n\n\t\t\t\t\tif (DEV) {\n\t\t\t\t\t\ttag(s, get_label(path, prop));\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t} else {\n\t\t\t\thas = s.v !== UNINITIALIZED;\n\n\t\t\t\tvar p = with_parent(() => proxy(value));\n\t\t\t\tset(s, p);\n\t\t\t}\n\n\t\t\tvar descriptor = Reflect.getOwnPropertyDescriptor(target, prop);\n\n\t\t\t// Set the new value before updating any signals so that any listeners get the new value\n\t\t\tif (descriptor?.set) {\n\t\t\t\tdescriptor.set.call(receiver, value);\n\t\t\t}\n\n\t\t\tif (!has) {\n\t\t\t\t// If we have mutated an array directly, we might need to\n\t\t\t\t// signal that length has also changed. Do it before updating metadata\n\t\t\t\t// to ensure that iterating over the array as a result of a metadata update\n\t\t\t\t// will not cause the length to be out of sync.\n\t\t\t\tif (is_proxied_array && typeof prop === 'string') {\n\t\t\t\t\tvar ls = /** @type {Source<number>} */ (sources.get('length'));\n\t\t\t\t\tvar n = Number(prop);\n\n\t\t\t\t\tif (Number.isInteger(n) && n >= ls.v) {\n\t\t\t\t\t\tset(ls, n + 1);\n\t\t\t\t\t}\n\t\t\t\t}\n\n\t\t\t\tupdate_version(version);\n\t\t\t}\n\n\t\t\treturn true;\n\t\t},\n\n\t\townKeys(target) {\n\t\t\tget(version);\n\n\t\t\tvar own_keys = Reflect.ownKeys(target).filter((key) => {\n\t\t\t\tvar source = sources.get(key);\n\t\t\t\treturn source === undefined || source.v !== UNINITIALIZED;\n\t\t\t});\n\n\t\t\tfor (var [key, source] of sources) {\n\t\t\t\tif (source.v !== UNINITIALIZED && !(key in target)) {\n\t\t\t\t\town_keys.push(key);\n\t\t\t\t}\n\t\t\t}\n\n\t\t\treturn own_keys;\n\t\t},\n\n\t\tsetPrototypeOf() {\n\t\t\te.state_prototype_fixed();\n\t\t}\n\t});\n}\n\n/**\n * @param {string} path\n * @param {string | symbol} prop\n */\nfunction get_label(path, prop) {\n\tif (typeof prop === 'symbol') return `${path}[Symbol(${prop.description ?? ''})]`;\n\tif (regex_is_valid_identifier.test(prop)) return `${path}.${prop}`;\n\treturn /^\\d+$/.test(prop) ? `${path}[${prop}]` : `${path}['${prop}']`;\n}\n\n/**\n * @param {Source<number>} signal\n * @param {1 | -1} [d]\n */\nfunction update_version(signal, d = 1) {\n\tset(signal, signal.v + d);\n}\n\n/**\n * @param {any} value\n */\nexport function get_proxied_value(value) {\n\ttry {\n\t\tif (value !== null && typeof value === 'object' && STATE_SYMBOL in value) {\n\t\t\treturn value[STATE_SYMBOL];\n\t\t}\n\t} catch {\n\t\t// the above if check can throw an error if the value in question\n\t\t// is the contentWindow of an iframe on another domain, in which\n\t\t// case we want to just return the value (because it's definitely\n\t\t// not a proxied value) so we don't break any JavaScript interacting\n\t\t// with that iframe (such as various payment companies client side\n\t\t// JavaScript libraries interacting with their iframes on the same\n\t\t// domain)\n\t}\n\n\treturn value;\n}\n\n/**\n * @param {any} a\n * @param {any} b\n */\nexport function is(a, b) {\n\treturn Object.is(get_proxied_value(a), get_proxied_value(b));\n}\n", "import * as w from '../warnings.js';\nimport { get_proxied_value } from '../proxy.js';\n\nexport function init_array_prototype_warnings() {\n\tconst array_prototype = Array.prototype;\n\t// The REPL ends up here over and over, and this prevents it from adding more and more patches\n\t// of the same kind to the prototype, which would slow down everything over time.\n\t// @ts-expect-error\n\tconst cleanup = Array.__svelte_cleanup;\n\tif (cleanup) {\n\t\tcleanup();\n\t}\n\n\tconst { indexOf, lastIndexOf, includes } = array_prototype;\n\n\tarray_prototype.indexOf = function (item, from_index) {\n\t\tconst index = indexOf.call(this, item, from_index);\n\n\t\tif (index === -1) {\n\t\t\tfor (let i = from_index ?? 0; i < this.length; i += 1) {\n\t\t\t\tif (get_proxied_value(this[i]) === item) {\n\t\t\t\t\tw.state_proxy_equality_mismatch('array.indexOf(...)');\n\t\t\t\t\tbreak;\n\t\t\t\t}\n\t\t\t}\n\t\t}\n\n\t\treturn index;\n\t};\n\n\tarray_prototype.lastIndexOf = function (item, from_index) {\n\t\t// we need to specify this.length - 1 because it's probably using something like\n\t\t// `arguments` inside so passing undefined is different from not passing anything\n\t\tconst index = lastIndexOf.call(this, item, from_index ?? this.length - 1);\n\n\t\tif (index === -1) {\n\t\t\tfor (let i = 0; i <= (from_index ?? this.length - 1); i += 1) {\n\t\t\t\tif (get_proxied_value(this[i]) === item) {\n\t\t\t\t\tw.state_proxy_equality_mismatch('array.lastIndexOf(...)');\n\t\t\t\t\tbreak;\n\t\t\t\t}\n\t\t\t}\n\t\t}\n\n\t\treturn index;\n\t};\n\n\tarray_prototype.includes = function (item, from_index) {\n\t\tconst has = includes.call(this, item, from_index);\n\n\t\tif (!has) {\n\t\t\tfor (let i = 0; i < this.length; i += 1) {\n\t\t\t\tif (get_proxied_value(this[i]) === item) {\n\t\t\t\t\tw.state_proxy_equality_mismatch('array.includes(...)');\n\t\t\t\t\tbreak;\n\t\t\t\t}\n\t\t\t}\n\t\t}\n\n\t\treturn has;\n\t};\n\n\t// @ts-expect-error\n\tArray.__svelte_cleanup = () => {\n\t\tarray_prototype.indexOf = indexOf;\n\t\tarray_prototype.lastIndexOf = lastIndexOf;\n\t\tarray_prototype.includes = includes;\n\t};\n}\n\n/**\n * @param {any} a\n * @param {any} b\n * @param {boolean} equal\n * @returns {boolean}\n */\nexport function strict_equals(a, b, equal = true) {\n\t// try-catch needed because this tries to read properties of `a` and `b`,\n\t// which could be disallowed for example in a secure context\n\ttry {\n\t\tif ((a === b) !== (get_proxied_value(a) === get_proxied_value(b))) {\n\t\t\tw.state_proxy_equality_mismatch(equal ? '===' : '!==');\n\t\t}\n\t} catch {}\n\n\treturn (a === b) === equal;\n}\n\n/**\n * @param {any} a\n * @param {any} b\n * @param {boolean} equal\n * @returns {boolean}\n */\nexport function equals(a, b, equal = true) {\n\tif ((a == b) !== (get_proxied_value(a) == get_proxied_value(b))) {\n\t\tw.state_proxy_equality_mismatch(equal ? '==' : '!=');\n\t}\n\n\treturn (a == b) === equal;\n}\n", "/** @import { TemplateNode } from '#client' */\nimport { hydrate_node, hydrating, set_hydrate_node } from './hydration.js';\nimport { DEV } from 'esm-env';\nimport { init_array_prototype_warnings } from '../dev/equality.js';\nimport { get_descriptor, is_extensible } from '../../shared/utils.js';\nimport { TEXT_NODE } from '#client/constants';\n\n// export these for reference in the compiled code, making global name deduplication unnecessary\n/** @type {Window} */\nexport var $window;\n\n/** @type {Document} */\nexport var $document;\n\n/** @type {boolean} */\nexport var is_firefox;\n\n/** @type {() => Node | null} */\nvar first_child_getter;\n/** @type {() => Node | null} */\nvar next_sibling_getter;\n\n/**\n * Initialize these lazily to avoid issues when using the runtime in a server context\n * where these globals are not available while avoiding a separate server entry point\n */\nexport function init_operations() {\n\tif ($window !== undefined) {\n\t\treturn;\n\t}\n\n\t$window = window;\n\t$document = document;\n\tis_firefox = /Firefox/.test(navigator.userAgent);\n\n\tvar element_prototype = Element.prototype;\n\tvar node_prototype = Node.prototype;\n\tvar text_prototype = Text.prototype;\n\n\t// @ts-ignore\n\tfirst_child_getter = get_descriptor(node_prototype, 'firstChild').get;\n\t// @ts-ignore\n\tnext_sibling_getter = get_descriptor(node_prototype, 'nextSibling').get;\n\n\tif (is_extensible(element_prototype)) {\n\t\t// the following assignments improve perf of lookups on DOM nodes\n\t\t// @ts-expect-error\n\t\telement_prototype.__click = undefined;\n\t\t// @ts-expect-error\n\t\telement_prototype.__className = undefined;\n\t\t// @ts-expect-error\n\t\telement_prototype.__attributes = null;\n\t\t// @ts-expect-error\n\t\telement_prototype.__style = undefined;\n\t\t// @ts-expect-error\n\t\telement_prototype.__e = undefined;\n\t}\n\n\tif (is_extensible(text_prototype)) {\n\t\t// @ts-expect-error\n\t\ttext_prototype.__t = undefined;\n\t}\n\n\tif (DEV) {\n\t\t// @ts-expect-error\n\t\telement_prototype.__svelte_meta = null;\n\n\t\tinit_array_prototype_warnings();\n\t}\n}\n\n/**\n * @param {string} value\n * @returns {Text}\n */\nexport function create_text(value = '') {\n\treturn document.createTextNode(value);\n}\n\n/**\n * @template {Node} N\n * @param {N} node\n * @returns {Node | null}\n */\n/*@__NO_SIDE_EFFECTS__*/\nexport function get_first_child(node) {\n\treturn first_child_getter.call(node);\n}\n\n/**\n * @template {Node} N\n * @param {N} node\n * @returns {Node | null}\n */\n/*@__NO_SIDE_EFFECTS__*/\nexport function get_next_sibling(node) {\n\treturn next_sibling_getter.call(node);\n}\n\n/**\n * Don't mark this as side-effect-free, hydration needs to walk all nodes\n * @template {Node} N\n * @param {N} node\n * @param {boolean} is_text\n * @returns {Node | null}\n */\nexport function child(node, is_text) {\n\tif (!hydrating) {\n\t\treturn get_first_child(node);\n\t}\n\n\tvar child = /** @type {TemplateNode} */ (get_first_child(hydrate_node));\n\n\t// Child can be null if we have an element with a single child, like `<p>{text}</p>`, where `text` is empty\n\tif (child === null) {\n\t\tchild = hydrate_node.appendChild(create_text());\n\t} else if (is_text && child.nodeType !== TEXT_NODE) {\n\t\tvar text = create_text();\n\t\tchild?.before(text);\n\t\tset_hydrate_node(text);\n\t\treturn text;\n\t}\n\n\tset_hydrate_node(child);\n\treturn child;\n}\n\n/**\n * Don't mark this as side-effect-free, hydration needs to walk all nodes\n * @param {DocumentFragment | TemplateNode[]} fragment\n * @param {boolean} is_text\n * @returns {Node | null}\n */\nexport function first_child(fragment, is_text) {\n\tif (!hydrating) {\n\t\t// when not hydrating, `fragment` is a `DocumentFragment` (the result of calling `open_frag`)\n\t\tvar first = /** @type {DocumentFragment} */ (get_first_child(/** @type {Node} */ (fragment)));\n\n\t\t// TODO prevent user comments with the empty string when preserveComments is true\n\t\tif (first instanceof Comment && first.data === '') return get_next_sibling(first);\n\n\t\treturn first;\n\t}\n\n\t// if an {expression} is empty during SSR, there might be no\n\t// text node to hydrate — we must therefore create one\n\tif (is_text && hydrate_node?.nodeType !== TEXT_NODE) {\n\t\tvar text = create_text();\n\n\t\thydrate_node?.before(text);\n\t\tset_hydrate_node(text);\n\t\treturn text;\n\t}\n\n\treturn hydrate_node;\n}\n\n/**\n * Don't mark this as side-effect-free, hydration needs to walk all nodes\n * @param {TemplateNode} node\n * @param {number} count\n * @param {boolean} is_text\n * @returns {Node | null}\n */\nexport function sibling(node, count = 1, is_text = false) {\n\tlet next_sibling = hydrating ? hydrate_node : node;\n\tvar last_sibling;\n\n\twhile (count--) {\n\t\tlast_sibling = next_sibling;\n\t\tnext_sibling = /** @type {TemplateNode} */ (get_next_sibling(next_sibling));\n\t}\n\n\tif (!hydrating) {\n\t\treturn next_sibling;\n\t}\n\n\t// if a sibling {expression} is empty during SSR, there might be no\n\t// text node to hydrate — we must therefore create one\n\tif (is_text && next_sibling?.nodeType !== TEXT_NODE) {\n\t\tvar text = create_text();\n\t\t// If the next sibling is `null` and we're handling text then it's because\n\t\t// the SSR content was empty for the text, so we need to generate a new text\n\t\t// node and insert it after the last sibling\n\t\tif (next_sibling === null) {\n\t\t\tlast_sibling?.after(text);\n\t\t} else {\n\t\t\tnext_sibling.before(text);\n\t\t}\n\t\tset_hydrate_node(text);\n\t\treturn text;\n\t}\n\n\tset_hydrate_node(next_sibling);\n\treturn /** @type {TemplateNode} */ (next_sibling);\n}\n\n/**\n * @template {Node} N\n * @param {N} node\n * @returns {void}\n */\nexport function clear_text_content(node) {\n\tnode.textContent = '';\n}\n\n/**\n *\n * @param {string} tag\n * @param {string} [namespace]\n * @param {string} [is]\n * @returns\n */\nexport function create_element(tag, namespace, is) {\n\tlet options = is ? { is } : undefined;\n\tif (namespace) {\n\t\treturn document.createElementNS(namespace, tag, options);\n\t}\n\treturn document.createElement(tag, options);\n}\n\nexport function create_fragment() {\n\treturn document.createDocumentFragment();\n}\n\n/**\n * @param {string} data\n * @returns\n */\nexport function create_comment(data = '') {\n\treturn document.createComment(data);\n}\n\n/**\n * @param {Element} element\n * @param {string} key\n * @param {string} value\n * @returns\n */\nexport function set_attribute(element, key, value = '') {\n\tif (key.startsWith('xlink:')) {\n\t\telement.setAttributeNS('http://www.w3.org/1999/xlink', key, value);\n\t\treturn;\n\t}\n\treturn element.setAttribute(key, value);\n}\n", "/** @import { TemplateNode } from '#client' */\n\nimport { COMMENT_NODE } from '#client/constants';\nimport {\n\tHYDRATION_END,\n\tHYDRATION_ERROR,\n\tHYDRATION_START,\n\tHYDRATION_START_ELSE\n} from '../../../constants.js';\nimport * as w from '../warnings.js';\nimport { get_next_sibling } from './operations.js';\n\n/**\n * Use this variable to guard everything related to hydration code so it can be treeshaken out\n * if the user doesn't use the `hydrate` method and these code paths are therefore not needed.\n */\nexport let hydrating = false;\n\n/** @param {boolean} value */\nexport function set_hydrating(value) {\n\thydrating = value;\n}\n\n/**\n * The node that is currently being hydrated. This starts out as the first node inside the opening\n * <!--[--> comment, and updates each time a component calls `$.child(...)` or `$.sibling(...)`.\n * When entering a block (e.g. `{#if ...}`), `hydrate_node` is the block opening comment; by the\n * time we leave the block it is the closing comment, which serves as the block's anchor.\n * @type {TemplateNode}\n */\nexport let hydrate_node;\n\n/** @param {TemplateNode} node */\nexport function set_hydrate_node(node) {\n\tif (node === null) {\n\t\tw.hydration_mismatch();\n\t\tthrow HYDRATION_ERROR;\n\t}\n\n\treturn (hydrate_node = node);\n}\n\nexport function hydrate_next() {\n\treturn set_hydrate_node(/** @type {TemplateNode} */ (get_next_sibling(hydrate_node)));\n}\n\n/** @param {TemplateNode} node */\nexport function reset(node) {\n\tif (!hydrating) return;\n\n\t// If the node has remaining siblings, something has gone wrong\n\tif (get_next_sibling(hydrate_node) !== null) {\n\t\tw.hydration_mismatch();\n\t\tthrow HYDRATION_ERROR;\n\t}\n\n\thydrate_node = node;\n}\n\n/**\n * @param {HTMLTemplateElement} template\n */\nexport function hydrate_template(template) {\n\tif (hydrating) {\n\t\t// @ts-expect-error TemplateNode doesn't include DocumentFragment, but it's actually fine\n\t\thydrate_node = template.content;\n\t}\n}\n\nexport function next(count = 1) {\n\tif (hydrating) {\n\t\tvar i = count;\n\t\tvar node = hydrate_node;\n\n\t\twhile (i--) {\n\t\t\tnode = /** @type {TemplateNode} */ (get_next_sibling(node));\n\t\t}\n\n\t\thydrate_node = node;\n\t}\n}\n\n/**\n * Removes all nodes starting at `hydrate_node` up until the next hydration end comment\n */\nexport function remove_nodes() {\n\tvar depth = 0;\n\tvar node = hydrate_node;\n\n\twhile (true) {\n\t\tif (node.nodeType === COMMENT_NODE) {\n\t\t\tvar data = /** @type {Comment} */ (node).data;\n\n\t\t\tif (data === HYDRATION_END) {\n\t\t\t\tif (depth === 0) return node;\n\t\t\t\tdepth -= 1;\n\t\t\t} else if (data === HYDRATION_START || data === HYDRATION_START_ELSE) {\n\t\t\t\tdepth += 1;\n\t\t\t}\n\t\t}\n\n\t\tvar next = /** @type {TemplateNode} */ (get_next_sibling(node));\n\t\tnode.remove();\n\t\tnode = next;\n\t}\n}\n\n/**\n *\n * @param {TemplateNode} node\n */\nexport function read_hydration_instruction(node) {\n\tif (!node || node.nodeType !== COMMENT_NODE) {\n\t\tw.hydration_mismatch();\n\t\tthrow HYDRATION_ERROR;\n\t}\n\n\treturn /** @type {Comment} */ (node).data;\n}\n", "import { hydrating } from '../hydration.js';\nimport { clear_text_content, get_first_child } from '../operations.js';\nimport { queue_micro_task } from '../task.js';\n\n/**\n * @param {HTMLElement} dom\n * @param {boolean} value\n * @returns {void}\n */\nexport function autofocus(dom, value) {\n\tif (value) {\n\t\tconst body = document.body;\n\t\tdom.autofocus = true;\n\n\t\tqueue_micro_task(() => {\n\t\t\tif (document.activeElement === body) {\n\t\t\t\tdom.focus();\n\t\t\t}\n\t\t});\n\t}\n}\n\n/**\n * The child of a textarea actually corresponds to the defaultValue property, so we need\n * to remove it upon hydration to avoid a bug when someone resets the form value.\n * @param {HTMLTextAreaElement} dom\n * @returns {void}\n */\nexport function remove_textarea_child(dom) {\n\tif (hydrating && get_first_child(dom) !== null) {\n\t\tclear_text_content(dom);\n\t}\n}\n\nlet listening_to_form_reset = false;\n\nexport function add_form_reset_listener() {\n\tif (!listening_to_form_reset) {\n\t\tlistening_to_form_reset = true;\n\t\tdocument.addEventListener(\n\t\t\t'reset',\n\t\t\t(evt) => {\n\t\t\t\t// Needs to happen one tick later or else the dom properties of the form\n\t\t\t\t// elements have not updated to their reset values yet\n\t\t\t\tPromise.resolve().then(() => {\n\t\t\t\t\tif (!evt.defaultPrevented) {\n\t\t\t\t\t\tfor (const e of /**@type {HTMLFormElement} */ (evt.target).elements) {\n\t\t\t\t\t\t\t// @ts-expect-error\n\t\t\t\t\t\t\te.__on_r?.();\n\t\t\t\t\t\t}\n\t\t\t\t\t}\n\t\t\t\t});\n\t\t\t},\n\t\t\t// In the capture phase to guarantee we get noticed of it (no possiblity of stopPropagation)\n\t\t\t{ capture: true }\n\t\t);\n\t}\n}\n", "import { teardown } from '../../../reactivity/effects.js';\nimport {\n\tactive_effect,\n\tactive_reaction,\n\tset_active_effect,\n\tset_active_reaction\n} from '../../../runtime.js';\nimport { add_form_reset_listener } from '../misc.js';\n\n/**\n * Fires the handler once immediately (unless corresponding arg is set to `false`),\n * then listens to the given events until the render effect context is destroyed\n * @param {EventTarget} target\n * @param {Array<string>} events\n * @param {(event?: Event) => void} handler\n * @param {any} call_handler_immediately\n */\nexport function listen(target, events, handler, call_handler_immediately = true) {\n\tif (call_handler_immediately) {\n\t\thandler();\n\t}\n\n\tfor (var name of events) {\n\t\ttarget.addEventListener(name, handler);\n\t}\n\n\tteardown(() => {\n\t\tfor (var name of events) {\n\t\t\ttarget.removeEventListener(name, handler);\n\t\t}\n\t});\n}\n\n/**\n * @template T\n * @param {() => T} fn\n */\nexport function without_reactive_context(fn) {\n\tvar previous_reaction = active_reaction;\n\tvar previous_effect = active_effect;\n\tset_active_reaction(null);\n\tset_active_effect(null);\n\ttry {\n\t\treturn fn();\n\t} finally {\n\t\tset_active_reaction(previous_reaction);\n\t\tset_active_effect(previous_effect);\n\t}\n}\n\n/**\n * Listen to the given event, and then instantiate a global form reset listener if not already done,\n * to notify all bindings when the form is reset\n * @param {HTMLElement} element\n * @param {string} event\n * @param {(is_reset?: true) => void} handler\n * @param {(is_reset?: true) => void} [on_reset]\n */\nexport function listen_to_event_and_reset_event(element, event, handler, on_reset = handler) {\n\telement.addEventListener(event, () => without_reactive_context(handler));\n\t// @ts-expect-error\n\tconst prev = element.__on_r;\n\tif (prev) {\n\t\t// special case for checkbox that can have multiple binds (group & checked)\n\t\t// @ts-expect-error\n\t\telement.__on_r = () => {\n\t\t\tprev();\n\t\t\ton_reset(true);\n\t\t};\n\t} else {\n\t\t// @ts-expect-error\n\t\telement.__on_r = () => on_reset(true);\n\t}\n\n\tadd_form_reset_listener();\n}\n", "import { teardown } from '../../reactivity/effects.js';\nimport { define_property, is_array } from '../../../shared/utils.js';\nimport { hydrating } from '../hydration.js';\nimport { queue_micro_task } from '../task.js';\nimport { FILENAME } from '../../../../constants.js';\nimport * as w from '../../warnings.js';\nimport {\n\tactive_effect,\n\tactive_reaction,\n\tset_active_effect,\n\tset_active_reaction\n} from '../../runtime.js';\nimport { without_reactive_context } from './bindings/shared.js';\n\n/** @type {Set<string>} */\nexport const all_registered_events = new Set();\n\n/** @type {Set<(events: Array<string>) => void>} */\nexport const root_event_handles = new Set();\n\n/**\n * SSR adds onload and onerror attributes to catch those events before the hydration.\n * This function detects those cases, removes the attributes and replays the events.\n * @param {HTMLElement} dom\n */\nexport function replay_events(dom) {\n\tif (!hydrating) return;\n\n\tdom.removeAttribute('onload');\n\tdom.removeAttribute('onerror');\n\t// @ts-expect-error\n\tconst event = dom.__e;\n\tif (event !== undefined) {\n\t\t// @ts-expect-error\n\t\tdom.__e = undefined;\n\t\tqueueMicrotask(() => {\n\t\t\tif (dom.isConnected) {\n\t\t\t\tdom.dispatchEvent(event);\n\t\t\t}\n\t\t});\n\t}\n}\n\n/**\n * @param {string} event_name\n * @param {EventTarget} dom\n * @param {EventListener} [handler]\n * @param {AddEventListenerOptions} [options]\n */\nexport function create_event(event_name, dom, handler, options = {}) {\n\t/**\n\t * @this {EventTarget}\n\t */\n\tfunction target_handler(/** @type {Event} */ event) {\n\t\tif (!options.capture) {\n\t\t\t// Only call in the bubble phase, else delegated events would be called before the capturing events\n\t\t\thandle_event_propagation.call(dom, event);\n\t\t}\n\t\tif (!event.cancelBubble) {\n\t\t\treturn without_reactive_context(() => {\n\t\t\t\treturn handler?.call(this, event);\n\t\t\t});\n\t\t}\n\t}\n\n\t// Chrome has a bug where pointer events don't work when attached to a DOM element that has been cloned\n\t// with cloneNode() and the DOM element is disconnected from the document. To ensure the event works, we\n\t// defer the attachment till after it's been appended to the document. TODO: remove this once Chrome fixes\n\t// this bug. The same applies to wheel events and touch events.\n\tif (\n\t\tevent_name.startsWith('pointer') ||\n\t\tevent_name.startsWith('touch') ||\n\t\tevent_name === 'wheel'\n\t) {\n\t\tqueue_micro_task(() => {\n\t\t\tdom.addEventListener(event_name, target_handler, options);\n\t\t});\n\t} else {\n\t\tdom.addEventListener(event_name, target_handler, options);\n\t}\n\n\treturn target_handler;\n}\n\n/**\n * Attaches an event handler to an element and returns a function that removes the handler. Using this\n * rather than `addEventListener` will preserve the correct order relative to handlers added declaratively\n * (with attributes like `onclick`), which use event delegation for performance reasons\n *\n * @param {EventTarget} element\n * @param {string} type\n * @param {EventListener} handler\n * @param {AddEventListenerOptions} [options]\n */\nexport function on(element, type, handler, options = {}) {\n\tvar target_handler = create_event(type, element, handler, options);\n\n\treturn () => {\n\t\telement.removeEventListener(type, target_handler, options);\n\t};\n}\n\n/**\n * @param {string} event_name\n * @param {Element} dom\n * @param {EventListener} [handler]\n * @param {boolean} [capture]\n * @param {boolean} [passive]\n * @returns {void}\n */\nexport function event(event_name, dom, handler, capture, passive) {\n\tvar options = { capture, passive };\n\tvar target_handler = create_event(event_name, dom, handler, options);\n\n\tif (\n\t\tdom === document.body ||\n\t\t// @ts-ignore\n\t\tdom === window ||\n\t\t// @ts-ignore\n\t\tdom === document ||\n\t\t// Firefox has quirky behavior, it can happen that we still get \"canplay\" events when the element is already removed\n\t\tdom instanceof HTMLMediaElement\n\t) {\n\t\tteardown(() => {\n\t\t\tdom.removeEventListener(event_name, target_handler, options);\n\t\t});\n\t}\n}\n\n/**\n * @param {Array<string>} events\n * @returns {void}\n */\nexport function delegate(events) {\n\tfor (var i = 0; i < events.length; i++) {\n\t\tall_registered_events.add(events[i]);\n\t}\n\n\tfor (var fn of root_event_handles) {\n\t\tfn(events);\n\t}\n}\n\n/**\n * @this {EventTarget}\n * @param {Event} event\n * @returns {void}\n */\nexport function handle_event_propagation(event) {\n\tvar handler_element = this;\n\tvar owner_document = /** @type {Node} */ (handler_element).ownerDocument;\n\tvar event_name = event.type;\n\tvar path = event.composedPath?.() || [];\n\tvar current_target = /** @type {null | Element} */ (path[0] || event.target);\n\n\t// composedPath contains list of nodes the event has propagated through.\n\t// We check __root to skip all nodes below it in case this is a\n\t// parent of the __root node, which indicates that there's nested\n\t// mounted apps. In this case we don't want to trigger events multiple times.\n\tvar path_idx = 0;\n\n\t// @ts-expect-error is added below\n\tvar handled_at = event.__root;\n\n\tif (handled_at) {\n\t\tvar at_idx = path.indexOf(handled_at);\n\t\tif (\n\t\t\tat_idx !== -1 &&\n\t\t\t(handler_element === document || handler_element === /** @type {any} */ (window))\n\t\t) {\n\t\t\t// This is the fallback document listener or a window listener, but the event was already handled\n\t\t\t// -> ignore, but set handle_at to document/window so that we're resetting the event\n\t\t\t// chain in case someone manually dispatches the same event object again.\n\t\t\t// @ts-expect-error\n\t\t\tevent.__root = handler_element;\n\t\t\treturn;\n\t\t}\n\n\t\t// We're deliberately not skipping if the index is higher, because\n\t\t// someone could create an event programmatically and emit it multiple times,\n\t\t// in which case we want to handle the whole propagation chain properly each time.\n\t\t// (this will only be a false negative if the event is dispatched multiple times and\n\t\t// the fallback document listener isn't reached in between, but that's super rare)\n\t\tvar handler_idx = path.indexOf(handler_element);\n\t\tif (handler_idx === -1) {\n\t\t\t// handle_idx can theoretically be -1 (happened in some JSDOM testing scenarios with an event listener on the window object)\n\t\t\t// so guard against that, too, and assume that everything was handled at this point.\n\t\t\treturn;\n\t\t}\n\n\t\tif (at_idx <= handler_idx) {\n\t\t\tpath_idx = at_idx;\n\t\t}\n\t}\n\n\tcurrent_target = /** @type {Element} */ (path[path_idx] || event.target);\n\t// there can only be one delegated event per element, and we either already handled the current target,\n\t// or this is the very first target in the chain which has a non-delegated listener, in which case it's safe\n\t// to handle a possible delegated event on it later (through the root delegation listener for example).\n\tif (current_target === handler_element) return;\n\n\t// Proxy currentTarget to correct target\n\tdefine_property(event, 'currentTarget', {\n\t\tconfigurable: true,\n\t\tget() {\n\t\t\treturn current_target || owner_document;\n\t\t}\n\t});\n\n\t// This started because of Chromium issue https://chromestatus.com/feature/5128696823545856,\n\t// where removal or moving of of the DOM can cause sync `blur` events to fire, which can cause logic\n\t// to run inside the current `active_reaction`, which isn't what we want at all. However, on reflection,\n\t// it's probably best that all event handled by Svelte have this behaviour, as we don't really want\n\t// an event handler to run in the context of another reaction or effect.\n\tvar previous_reaction = active_reaction;\n\tvar previous_effect = active_effect;\n\tset_active_reaction(null);\n\tset_active_effect(null);\n\n\ttry {\n\t\t/**\n\t\t * @type {unknown}\n\t\t */\n\t\tvar throw_error;\n\t\t/**\n\t\t * @type {unknown[]}\n\t\t */\n\t\tvar other_errors = [];\n\n\t\twhile (current_target !== null) {\n\t\t\t/** @type {null | Element} */\n\t\t\tvar parent_element =\n\t\t\t\tcurrent_target.assignedSlot ||\n\t\t\t\tcurrent_target.parentNode ||\n\t\t\t\t/** @type {any} */ (current_target).host ||\n\t\t\t\tnull;\n\n\t\t\ttry {\n\t\t\t\t// @ts-expect-error\n\t\t\t\tvar delegated = current_target['__' + event_name];\n\n\t\t\t\tif (\n\t\t\t\t\tdelegated != null &&\n\t\t\t\t\t(!(/** @type {any} */ (current_target).disabled) ||\n\t\t\t\t\t\t// DOM could've been updated already by the time this is reached, so we check this as well\n\t\t\t\t\t\t// -> the target could not have been disabled because it emits the event in the first place\n\t\t\t\t\t\tevent.target === current_target)\n\t\t\t\t) {\n\t\t\t\t\tif (is_array(delegated)) {\n\t\t\t\t\t\tvar [fn, ...data] = delegated;\n\t\t\t\t\t\tfn.apply(current_target, [event, ...data]);\n\t\t\t\t\t} else {\n\t\t\t\t\t\tdelegated.call(current_target, event);\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t} catch (error) {\n\t\t\t\tif (throw_error) {\n\t\t\t\t\tother_errors.push(error);\n\t\t\t\t} else {\n\t\t\t\t\tthrow_error = error;\n\t\t\t\t}\n\t\t\t}\n\t\t\tif (event.cancelBubble || parent_element === handler_element || parent_element === null) {\n\t\t\t\tbreak;\n\t\t\t}\n\t\t\tcurrent_target = parent_element;\n\t\t}\n\n\t\tif (throw_error) {\n\t\t\tfor (let error of other_errors) {\n\t\t\t\t// Throw the rest of the errors, one-by-one on a microtask\n\t\t\t\tqueueMicrotask(() => {\n\t\t\t\t\tthrow error;\n\t\t\t\t});\n\t\t\t}\n\t\t\tthrow throw_error;\n\t\t}\n\t} finally {\n\t\t// @ts-expect-error is used above\n\t\tevent.__root = handler_element;\n\t\t// @ts-ignore remove proxy on currentTarget\n\t\tdelete event.currentTarget;\n\t\tset_active_reaction(previous_reaction);\n\t\tset_active_effect(previous_effect);\n\t}\n}\n\n/**\n * In dev, warn if an event handler is not a function, as it means the\n * user probably called the handler or forgot to add a `() =>`\n * @param {() => (event: Event, ...args: any) => void} thunk\n * @param {EventTarget} element\n * @param {[Event, ...any]} args\n * @param {any} component\n * @param {[number, number]} [loc]\n * @param {boolean} [remove_parens]\n */\nexport function apply(\n\tthunk,\n\telement,\n\targs,\n\tcomponent,\n\tloc,\n\thas_side_effects = false,\n\tremove_parens = false\n) {\n\tlet handler;\n\tlet error;\n\n\ttry {\n\t\thandler = thunk();\n\t} catch (e) {\n\t\terror = e;\n\t}\n\n\tif (typeof handler !== 'function' && (has_side_effects || handler != null || error)) {\n\t\tconst filename = component?.[FILENAME];\n\t\tconst location = loc ? ` at ${filename}:${loc[0]}:${loc[1]}` : ` in ${filename}`;\n\t\tconst phase = args[0]?.eventPhase < Event.BUBBLING_PHASE ? 'capture' : '';\n\t\tconst event_name = args[0]?.type + phase;\n\t\tconst description = `\\`${event_name}\\` handler${location}`;\n\t\tconst suggestion = remove_parens ? 'remove the trailing `()`' : 'add a leading `() =>`';\n\n\t\tw.event_handler_invalid(description, suggestion);\n\n\t\tif (error) {\n\t\t\tthrow error;\n\t\t}\n\t}\n\thandler?.apply(element, args);\n}\n"], "mappings": ";;;;;;;;;;;;;;;AAAO,IAAM,qBAAqB;AAC3B,IAAM,sBAAsB,KAAK;AAEjC,IAAM,qBAAqB,KAAK;AAChC,IAAM,mBAAmB,KAAK;AAC9B,IAAM,sBAAsB,KAAK;AAEjC,IAAM,qBAAqB;AAC3B,IAAM,iBAAiB,KAAK;AAC5B,IAAM,mBAAmB,KAAK;AAC9B,IAAM,oBAAoB,KAAK;AAC/B,IAAM,wBAAwB,KAAK;AAEnC,IAAM,gBAAgB;AACtB,IAAM,iBAAiB,KAAK;AAC5B,IAAM,oBAAoB,KAAK;AAE/B,IAAM,oBAAoB;AAC1B,IAAM,2BAA2B,KAAK;AACtC,IAAM,mBAAmB,KAAK;AAC9B,IAAM,sBAAsB,KAAK;AAEjC,IAAM,kBAAkB;AAExB,IAAM,uBAAuB;AAC7B,IAAM,gBAAgB;AACtB,IAAM,kBAAkB,CAAC;AAGzB,IAAM,kCAAkC,KAAK;AAE7C,IAAM,gBAAgB,OAAO;AAG7B,IAAM,WAAW,OAAO,UAAU;AAClC,IAAM,MAAM,OAAO,KAAK;AAExB,IAAM,iBAAiB;AACvB,IAAM,gBAAgB;AACtB,IAAM,mBAAmB;AAqBzB,IAAM,iBAAiB;;;ACpDvB,SAAS,0BAA0B;AACzC,MAAI,cAAK;AACR,UAAM,QAAQ,IAAI,MAAM;AAAA;AAAA,6CAAwM;AAEhO,UAAM,OAAO;AAEb,UAAM;AAAA,EACP,OAAO;AACN,UAAM,IAAI,MAAM,8CAA8C;AAAA,EAC/D;AACD;AAMO,SAAS,4BAA4B;AAC3C,MAAI,cAAK;AACR,UAAM,QAAQ,IAAI,MAAM;AAAA;AAAA,+CAAwL;AAEhN,UAAM,OAAO;AAEb,UAAM;AAAA,EACP,OAAO;AACN,UAAM,IAAI,MAAM,gDAAgD;AAAA,EACjE;AACD;AAOO,SAAS,4BAA4B,MAAM;AACjD,MAAI,cAAK;AACR,UAAM,QAAQ,IAAI,MAAM;AAAA,IAAkC,IAAI;AAAA,iDAA4G;AAE1K,UAAM,OAAO;AAEb,UAAM;AAAA,EACP,OAAO;AACN,UAAM,IAAI,MAAM,kDAAkD;AAAA,EACnE;AACD;AAMO,SAAS,6BAA6B;AAC5C,MAAI,cAAK;AACR,UAAM,QAAQ,IAAI,MAAM;AAAA;AAAA,gDAA+S;AAEvU,UAAM,OAAO;AAEb,UAAM;AAAA,EACP,OAAO;AACN,UAAM,IAAI,MAAM,iDAAiD;AAAA,EAClE;AACD;AAOO,SAAS,oBAAoB,MAAM;AACzC,MAAI,cAAK;AACR,UAAM,QAAQ,IAAI,MAAM;AAAA,IAA0B,IAAI;AAAA,yCAAyF;AAE/I,UAAM,OAAO;AAEb,UAAM;AAAA,EACP,OAAO;AACN,UAAM,IAAI,MAAM,0CAA0C;AAAA,EAC3D;AACD;AAMO,SAAS,oCAAoC;AACnD,MAAI,cAAK;AACR,UAAM,QAAQ,IAAI,MAAM;AAAA;AAAA,uDAAmK;AAE3L,UAAM,OAAO;AAEb,UAAM;AAAA,EACP,OAAO;AACN,UAAM,IAAI,MAAM,wDAAwD;AAAA,EACzE;AACD;;;AClGO,IAAI,WAAW,MAAM;AACrB,IAAI,WAAW,MAAM,UAAU;AAC/B,IAAI,aAAa,MAAM;AACvB,IAAI,cAAc,OAAO;AACzB,IAAI,kBAAkB,OAAO;AAC7B,IAAI,iBAAiB,OAAO;AAC5B,IAAI,kBAAkB,OAAO;AAC7B,IAAI,mBAAmB,OAAO;AAC9B,IAAI,kBAAkB,MAAM;AAC5B,IAAI,mBAAmB,OAAO;AAC9B,IAAI,gBAAgB,OAAO;AAM3B,SAAS,YAAY,OAAO;AAClC,SAAO,OAAO,UAAU;AACzB;AAEO,IAAM,OAAO,MAAM;AAAC;AAUpB,SAAS,WAAW,OAAO;AACjC,SAAO,QAAO,+BAAO,UAAS;AAC/B;AAGO,SAAS,IAAI,IAAI;AACvB,SAAO,GAAG;AACX;AAGO,SAAS,QAAQ,KAAK;AAC5B,WAAS,IAAI,GAAG,IAAI,IAAI,QAAQ,KAAK;AACpC,QAAI,CAAC,EAAE;AAAA,EACR;AACD;AAMO,SAAS,WAAW;AAE1B,MAAI;AAGJ,MAAI;AAGJ,MAAI,UAAU,IAAI,QAAQ,CAAC,KAAK,QAAQ;AACvC,cAAU;AACV,aAAS;AAAA,EACV,CAAC;AAGD,SAAO,EAAE,SAAS,SAAS,OAAO;AACnC;AASO,SAAS,SAAS,OAAOA,WAAU,OAAO,OAAO;AACvD,SAAO,UAAU,SACd;AAAA;AAAA,IACyBA,UAAU;AAAA;AAAA;AAAA,IAChBA;AAAA,MACnB;AACJ;AAWO,SAAS,SAAS,OAAO,GAAG;AAElC,MAAI,MAAM,QAAQ,KAAK,GAAG;AACzB,WAAO;AAAA,EACR;AAKA,MAAI,MAAM,UAAa,EAAE,OAAO,YAAY,QAAQ;AACnD,WAAO,MAAM,KAAK,KAAK;AAAA,EACxB;AAKA,QAAM,QAAQ,CAAC;AAEf,aAAW,WAAW,OAAO;AAC5B,UAAM,KAAK,OAAO;AAClB,QAAI,MAAM,WAAW,EAAG;AAAA,EACzB;AAEA,SAAO;AACR;;;ACrHO,IAAM,UAAU,KAAK;AACrB,IAAM,SAAS,KAAK;AACpB,IAAM,gBAAgB,KAAK;AAC3B,IAAM,eAAe,KAAK;AAC1B,IAAM,gBAAgB,KAAK;AAC3B,IAAM,cAAc,KAAK;AACzB,IAAM,kBAAkB,KAAK;AAC7B,IAAM,UAAU,KAAK;AACrB,IAAM,eAAe,KAAK;AAC1B,IAAM,QAAQ,KAAK;AACnB,IAAM,QAAQ,KAAK;AACnB,IAAM,cAAc,KAAK;AACzB,IAAM,QAAQ,KAAK;AACnB,IAAM,YAAY,KAAK;AACvB,IAAM,aAAa,KAAK;AAExB,IAAM,qBAAqB,KAAK;AAChC,IAAM,iBAAiB,KAAK;AAC5B,IAAM,cAAc,KAAK;AACzB,IAAM,mBAAmB,KAAK;AAC9B,IAAM,qBAAqB,KAAK;AAChC,IAAM,cAAc,KAAK;AAEzB,IAAM,eAAe,OAAO,QAAQ;AACpC,IAAM,eAAe,OAAO,cAAc;AAC1C,IAAM,sBAAsB,OAAO,EAAE;AACrC,IAAM,oBAAoB,OAAO,YAAY;AAG7C,IAAM,iBAAiB,IAAK,MAAM,2BAA2B,MAAM;AAAA,EAAvC;AAAA;AAClC,gCAAO;AACP,mCAAU;AAAA;AACX,EAAG;AAEI,IAAM,eAAe;AACrB,IAAM,YAAY;AAClB,IAAM,eAAe;AACrB,IAAM,yBAAyB;;;AC7B/B,SAAS,8BAA8B;AAC7C,MAAI,cAAK;AACR,UAAM,QAAQ,IAAI,MAAM;AAAA;AAAA,iDAAiL;AAEzM,UAAM,OAAO;AAEb,UAAM;AAAA,EACP,OAAO;AACN,UAAM,IAAI,MAAM,kDAAkD;AAAA,EACnE;AACD;AA8CO,SAAS,sBAAsB,QAAQ,WAAW;AACxD,MAAI,cAAK;AACR,UAAM,QAAQ,IAAI,MAAM;AAAA,YAAoC,MAAM,kCAAkC,SAAS;AAAA,2CAA8E;AAE3L,UAAM,OAAO;AAEb,UAAM;AAAA,EACP,OAAO;AACN,UAAM,IAAI,MAAM,4CAA4C;AAAA,EAC7D;AACD;AAQO,SAAS,0BAA0B,WAAW,MAAM;AAC1D,MAAI,cAAK;AACR,UAAM,QAAQ,IAAI,MAAM;AAAA,2BAAuD,SAAS,eAAe,IAAI;AAAA,+CAA4N;AAEvU,UAAM,OAAO;AAEb,UAAM;AAAA,EACP,OAAO;AACN,UAAM,IAAI,MAAM,gDAAgD;AAAA,EACjE;AACD;AAMO,SAAS,0BAA0B;AACzC,MAAI,cAAK;AACR,UAAM,QAAQ,IAAI,MAAM;AAAA;AAAA,6CAA4H;AAEpJ,UAAM,OAAO;AAEb,UAAM;AAAA,EACP,OAAO;AACN,UAAM,IAAI,MAAM,8CAA8C;AAAA,EAC/D;AACD;AASO,SAAS,mBAAmB,GAAG,GAAG,OAAO;AAC/C,MAAI,cAAK;AACR,UAAM,QAAQ,IAAI,MAAM;AAAA,EAAuB,QAC5C,wCAAwC,KAAK,iBAAiB,CAAC,QAAQ,CAAC,KACxE,iDAAiD,CAAC,QAAQ,CAAC,EAAE;AAAA,wCAA2C;AAE3G,UAAM,OAAO;AAEb,UAAM;AAAA,EACP,OAAO;AACN,UAAM,IAAI,MAAM,yCAAyC;AAAA,EAC1D;AACD;AAOO,SAAS,mBAAmB,MAAM;AACxC,MAAI,cAAK;AACR,UAAM,QAAQ,IAAI,MAAM;AAAA,IAAyB,IAAI;AAAA,wCAA8F;AAEnJ,UAAM,OAAO;AAEb,UAAM;AAAA,EACP,OAAO;AACN,UAAM,IAAI,MAAM,yCAAyC;AAAA,EAC1D;AACD;AAMO,SAAS,4BAA4B;AAC3C,MAAI,cAAK;AACR,UAAM,QAAQ,IAAI,MAAM;AAAA;AAAA,+CAA8K;AAEtM,UAAM,OAAO;AAEb,UAAM;AAAA,EACP,OAAO;AACN,UAAM,IAAI,MAAM,gDAAgD;AAAA,EACjE;AACD;AAOO,SAAS,cAAc,MAAM;AACnC,MAAI,cAAK;AACR,UAAM,QAAQ,IAAI,MAAM;AAAA,IAAoB,IAAI;AAAA,mCAAiH;AAEjK,UAAM,OAAO;AAEb,UAAM;AAAA,EACP,OAAO;AACN,UAAM,IAAI,MAAM,oCAAoC;AAAA,EACrD;AACD;AAMO,SAAS,+BAA+B;AAC9C,MAAI,cAAK;AACR,UAAM,QAAQ,IAAI,MAAM;AAAA;AAAA,kDAAmQ;AAE3R,UAAM,OAAO;AAEb,UAAM;AAAA,EACP,OAAO;AACN,UAAM,IAAI,MAAM,mDAAmD;AAAA,EACpE;AACD;AAMO,SAAS,oCAAoC;AACnD,MAAI,cAAK;AACR,UAAM,QAAQ,IAAI,MAAM;AAAA;AAAA,uDAAgK;AAExL,UAAM,OAAO;AAEb,UAAM;AAAA,EACP,OAAO;AACN,UAAM,IAAI,MAAM,wDAAwD;AAAA,EACzE;AACD;AAMO,SAAS,mBAAmB;AAClC,MAAI,cAAK;AACR,UAAM,QAAQ,IAAI,MAAM;AAAA;AAAA,sCAA4F;AAEpH,UAAM,OAAO;AAEb,UAAM;AAAA,EACP,OAAO;AACN,UAAM,IAAI,MAAM,uCAAuC;AAAA,EACxD;AACD;AAMO,SAAS,kBAAkB;AACjC,MAAI,cAAK;AACR,UAAM,QAAQ,IAAI,MAAM;AAAA;AAAA,qCAA0M;AAElO,UAAM,OAAO;AAEb,UAAM;AAAA,EACP,OAAO;AACN,UAAM,IAAI,MAAM,sCAAsC;AAAA,EACvD;AACD;AAOO,SAAS,sBAAsB,MAAM;AAC3C,MAAI,cAAK;AACR,UAAM,QAAQ,IAAI,MAAM;AAAA,IAA4B,IAAI;AAAA,2CAAkF;AAE1I,UAAM,OAAO;AAEb,UAAM;AAAA,EACP,OAAO;AACN,UAAM,IAAI,MAAM,4CAA4C;AAAA,EAC7D;AACD;AAOO,SAAS,oBAAoB,KAAK;AACxC,MAAI,cAAK;AACR,UAAM,QAAQ,IAAI,MAAM;AAAA,mBAAyC,GAAG,yBAAyB,GAAG;AAAA,yCAAmE;AAEnK,UAAM,OAAO;AAEb,UAAM;AAAA,EACP,OAAO;AACN,UAAM,IAAI,MAAM,0CAA0C;AAAA,EAC3D;AACD;AAOO,SAAS,oBAAoB,UAAU;AAC7C,MAAI,cAAK;AACR,UAAM,QAAQ,IAAI,MAAM;AAAA,oDAA0E,QAAQ;AAAA,yCAA2D;AAErK,UAAM,OAAO;AAEb,UAAM;AAAA,EACP,OAAO;AACN,UAAM,IAAI,MAAM,0CAA0C;AAAA,EAC3D;AACD;AAOO,SAAS,oBAAoB,MAAM;AACzC,MAAI,cAAK;AACR,UAAM,QAAQ,IAAI,MAAM;AAAA,QAA8B,IAAI;AAAA,yCAAoH;AAE9K,UAAM,OAAO;AAEb,UAAM;AAAA,EACP,OAAO;AACN,UAAM,IAAI,MAAM,0CAA0C;AAAA,EAC3D;AACD;AAMO,SAAS,0BAA0B;AACzC,MAAI,cAAK;AACR,UAAM,QAAQ,IAAI,MAAM;AAAA;AAAA,6CAAmN;AAE3O,UAAM,OAAO;AAEb,UAAM;AAAA,EACP,OAAO;AACN,UAAM,IAAI,MAAM,8CAA8C;AAAA,EAC/D;AACD;AAMO,SAAS,wBAAwB;AACvC,MAAI,cAAK;AACR,UAAM,QAAQ,IAAI,MAAM;AAAA;AAAA,2CAA8G;AAEtI,UAAM,OAAO;AAEb,UAAM;AAAA,EACP,OAAO;AACN,UAAM,IAAI,MAAM,4CAA4C;AAAA,EAC7D;AACD;AAMO,SAAS,wBAAwB;AACvC,MAAI,cAAK;AACR,UAAM,QAAQ,IAAI,MAAM;AAAA;AAAA,2CAAyO;AAEjQ,UAAM,OAAO;AAEb,UAAM;AAAA,EACP,OAAO;AACN,UAAM,IAAI,MAAM,4CAA4C;AAAA,EAC7D;AACD;;;ACpWO,SAAS,OAAO,OAAO;AAC7B,SAAO,UAAU,KAAK;AACvB;AAOO,SAAS,eAAe,GAAG,GAAG;AACpC,SAAO,KAAK,IACT,KAAK,IACL,MAAM,KAAM,MAAM,QAAQ,OAAO,MAAM,YAAa,OAAO,MAAM;AACrE;AAOO,SAAS,UAAU,GAAG,GAAG;AAC/B,SAAO,MAAM;AACd;AAGO,SAAS,YAAY,OAAO;AAClC,SAAO,CAAC,eAAe,OAAO,KAAK,CAAC;AACrC;;;AC1BA,IAAI,OAAO;AACX,IAAI,SAAS;AAMN,SAAS,6BAA6BC,MAAK;AACjD,MAAI,cAAK;AACR,YAAQ,KAAK;AAAA,4BAAsEA,IAAG;AAAA,oDAAsG,MAAM,MAAM;AAAA,EACzM,OAAO;AACN,YAAQ,KAAK,mDAAmD;AAAA,EACjE;AACD;AAQO,SAAS,2BAA2B,YAAY;AACtD,MAAI,cAAK;AACR,YAAQ;AAAA,MACP;AAAA,IAA4C,aACzC;AAAA;AAAA,EAEJ,UAAU,KACN,iFAAiF;AAAA;AAAA,MACpF;AAAA,MACA;AAAA,IACD;AAAA,EACD,OAAO;AACN,YAAQ,KAAK,iDAAiD;AAAA,EAC/D;AACD;;;AC5BA,IAAM,QAAQ,CAAC;AAQR,SAAS,SAAS,OAAO,eAAe,OAAO;AACrD,MAAI,gBAAO,CAAC,cAAc;AAEzB,UAAM,QAAQ,CAAC;AAEf,UAAM,OAAO,MAAM,OAAO,oBAAI,IAAI,GAAG,IAAI,KAAK;AAC9C,QAAI,MAAM,WAAW,KAAK,MAAM,CAAC,MAAM,IAAI;AAE1C,MAAE,2BAA2B;AAAA,IAC9B,WAAW,MAAM,SAAS,GAAG;AAE5B,YAAM,QAAQ,MAAM,SAAS,KAAK,MAAM,MAAM,GAAG,CAAC,IAAI,MAAM,MAAM,GAAG,EAAE;AACvE,YAAM,SAAS,MAAM,SAAS,MAAM;AAEpC,UAAI,WAAW,MAAM,IAAI,CAAC,SAAS,YAAY,IAAI,EAAE,EAAE,KAAK,IAAI;AAChE,UAAI,SAAS,EAAG,aAAY;AAAA,WAAc,MAAM;AAEhD,MAAE,2BAA2B,QAAQ;AAAA,IACtC;AAEA,WAAO;AAAA,EACR;AAEA,SAAO,MAAM,OAAO,oBAAI,IAAI,GAAG,IAAI,KAAK;AACzC;AAWA,SAAS,MAAM,OAAO,QAAQ,MAAM,OAAO,WAAW,MAAM;AAC3D,MAAI,OAAO,UAAU,YAAY,UAAU,MAAM;AAChD,QAAI,YAAY,OAAO,IAAI,KAAK;AAChC,QAAI,cAAc,OAAW,QAAO;AAEpC,QAAI,iBAAiB,IAAK;AAAA;AAAA,MAAmC,IAAI,IAAI,KAAK;AAAA;AAC1E,QAAI,iBAAiB,IAAK;AAAA;AAAA,MAAmC,IAAI,IAAI,KAAK;AAAA;AAE1E,QAAI,SAAS,KAAK,GAAG;AACpB,UAAI;AAAA;AAAA,QAAqC,MAAM,MAAM,MAAM;AAAA;AAC3D,aAAO,IAAI,OAAO,IAAI;AAEtB,UAAI,aAAa,MAAM;AACtB,eAAO,IAAI,UAAU,IAAI;AAAA,MAC1B;AAEA,eAAS,IAAI,GAAG,IAAI,MAAM,QAAQ,KAAK,GAAG;AACzC,YAAI,UAAU,MAAM,CAAC;AACrB,YAAI,KAAK,OAAO;AACf,eAAK,CAAC,IAAI,MAAM,SAAS,QAAQ,eAAM,GAAG,IAAI,IAAI,CAAC,MAAM,MAAM,KAAK;AAAA,QACrE;AAAA,MACD;AAEA,aAAO;AAAA,IACR;AAEA,QAAI,iBAAiB,KAAK,MAAM,kBAAkB;AAEjD,aAAO,CAAC;AACR,aAAO,IAAI,OAAO,IAAI;AAEtB,UAAI,aAAa,MAAM;AACtB,eAAO,IAAI,UAAU,IAAI;AAAA,MAC1B;AAEA,eAAS,OAAO,OAAO;AAEtB,aAAK,GAAG,IAAI,MAAM,MAAM,GAAG,GAAG,QAAQ,eAAM,GAAG,IAAI,IAAI,GAAG,KAAK,MAAM,KAAK;AAAA,MAC3E;AAEA,aAAO;AAAA,IACR;AAEA,QAAI,iBAAiB,MAAM;AAC1B;AAAA;AAAA,QAAmC,gBAAgB,KAAK;AAAA;AAAA,IACzD;AAEA,QAAI;AAAA,IAA8C,MAAO,WAAY,YAAY;AAChF,aAAO;AAAA;AAAA,QACiC,MAAO,OAAO;AAAA,QACrD;AAAA,QACA,eAAM,GAAG,IAAI,cAAc;AAAA,QAC3B;AAAA;AAAA,QAEA;AAAA,MACD;AAAA,IACD;AAAA,EACD;AAEA,MAAI,iBAAiB,aAAa;AAEjC;AAAA;AAAA,MAAmC;AAAA;AAAA,EACpC;AAEA,MAAI;AACH;AAAA;AAAA,MAAmC,gBAAgB,KAAK;AAAA;AAAA,EACzD,SAAS,GAAG;AACX,QAAI,cAAK;AACR,YAAM,KAAK,IAAI;AAAA,IAChB;AAEA;AAAA;AAAA,MAAmC;AAAA;AAAA,EACpC;AACD;;;AChHO,IAAI,sBAAsB;AAMjC,SAAS,UAAU,QAAQ,OAAO;AACjC,QAAM,QAAQ,OAAO;AAErB,MAAI,UAAU,eAAe;AAC5B;AAAA,EACD;AAEA,QAAM,QAAQ,OAAO,IAAI,aAAa,IAAI,aAAa;AACvD,QAAM;AAAA;AAAA,IAA4C;AAAA;AAClD,QAAM,QAAQ,OAAO,KAAK,iBAAiB,MAAM,iBAAiB,OAAO;AACzE,QAAM,QAAQ,QACX,6CACA;AAGH,UAAQ;AAAA,IACP,OAAO,QAAQ,KAAK,IAAI,MAAM,OAAO,KAAK,KAAK,KAAK,IAAI;AAAA,IACxD;AAAA,IACA,QAAQ,wBAAwB;AAAA,IAChC,OAAO,UAAU,YAAY,UAAU,QAAQ,gBAAgB,QAC5D,SAAS,OAAO,IAAI,IACpB;AAAA,EACJ;AAEA,MAAI,SAAS,YAAY;AACxB,UAAM,OAAO,IAAI;AAAA;AAAA,MAA4B,OAAQ;AAAA,IAAI;AACzD,eAAW,OAAO,MAAM;AACvB,gBAAU,GAAG;AAAA,IACd;AAAA,EACD;AAEA,MAAI,OAAO,SAAS;AAEnB,YAAQ,IAAI,OAAO,OAAO;AAAA,EAC3B;AAEA,MAAI,SAAS,OAAO,SAAS;AAE5B,YAAQ,IAAI,OAAO,OAAO;AAAA,EAC3B;AAEA,MAAI,OAAO;AACV,aAASC,UAAS,MAAM,QAAQ;AAE/B,cAAQ,IAAIA,MAAK;AAAA,IAClB;AAAA,EACD;AAGA,UAAQ,SAAS;AAClB;AAOO,SAAS,MAAMC,QAAO,IAAI;AAChC,MAAI,iCAAiC;AAErC,MAAI;AACH,0BAAsB,EAAE,SAAS,oBAAI,IAAI,GAAG,UAAU,gBAAgB;AAEtE,QAAI,QAAQ,YAAY,IAAI;AAC5B,QAAI,QAAQ,GAAG;AACf,QAAI,QAAQ,YAAY,IAAI,IAAI,OAAO,QAAQ,CAAC;AAEhD,QAAI,SAAS,QAAQA,MAAK;AAE1B,QAAI,CAAC,gBAAgB,GAAG;AAEvB,cAAQ,IAAI,GAAG,MAAM,gCAAgC,IAAI,OAAO,aAAa;AAAA,IAC9E,WAAW,oBAAoB,QAAQ,SAAS,GAAG;AAElD,cAAQ,IAAI,GAAG,MAAM,gCAAgC,IAAI,OAAO,aAAa;AAAA,IAC9E,OAAO;AAEN,cAAQ,MAAM,GAAG,MAAM,OAAO,IAAI,OAAO,aAAa;AAEtD,UAAI,UAAU,oBAAoB;AAElC,cAAQ,MAAM;AACb,mBAAW,CAAC,QAAQ,MAAM,KAAK,SAAS;AACvC,oBAAU,QAAQ,MAAM;AAAA,QACzB;AAAA,MACD,CAAC;AAED,4BAAsB;AAGtB,cAAQ,SAAS;AAAA,IAClB;AAEA,WAAO;AAAA,EACR,UAAE;AACD,0BAAsB;AAAA,EACvB;AACD;AAKO,SAAS,UAAUA,QAAO;AAChC,MAAI,QAAQ,MAAM;AAClB,QAAMC,SAAQ,MAAM;AAEpB,MAAIA,QAAO;AACV,UAAM,QAAQA,OAAM,MAAM,IAAI;AAC9B,UAAM,YAAY,CAAC,IAAI;AAEvB,aAAS,IAAI,GAAG,IAAI,MAAM,QAAQ,KAAK;AACtC,YAAM,OAAO,MAAM,CAAC;AAEpB,UAAI,SAAS,SAAS;AACrB;AAAA,MACD;AACA,UAAI,KAAK,SAAS,oBAAoB,GAAG;AACxC,eAAO;AAAA,MACR;AACA,UAAI,KAAK,SAAS,qBAAqB,GAAG;AACzC;AAAA,MACD;AACA,gBAAU,KAAK,IAAI;AAAA,IACpB;AAEA,QAAI,UAAU,WAAW,GAAG;AAC3B,aAAO;AAAA,IACR;AAEA,oBAAgB,OAAO,SAAS;AAAA,MAC/B,OAAO,UAAU,KAAK,IAAI;AAAA,IAC3B,CAAC;AAED,oBAAgB,OAAO,QAAQ;AAAA;AAAA,MAE9B,OAAO,GAAGD,MAAK;AAAA,IAChB,CAAC;AAAA,EACF;AACA,SAAO;AACR;AAMO,SAAS,IAAIE,SAAQF,QAAO;AAClC,EAAAE,QAAO,QAAQF;AACf,YAAUE,QAAO,GAAGF,MAAK;AAEzB,SAAOE;AACR;AAMO,SAAS,UAAU,OAAOF,QAAO;AAjLxC;AAmLC,uCAAQ,uBAAR,+BAA6BA;AAC7B,SAAO;AACR;AAKO,SAAS,MAAM,OAAO;AAC5B,MAAI,OAAO,UAAU,SAAU,QAAO,UAAU,MAAM,WAAW;AACjE,MAAI,OAAO,UAAU,WAAY,QAAO;AACxC,MAAI,OAAO,UAAU,YAAY,MAAO,QAAO;AAC/C,SAAO,OAAO,KAAK;AACpB;;;AC/KO,IAAI,oBAAoB;AAGxB,SAAS,sBAAsB,SAAS;AAC9C,sBAAoB;AACrB;AAGO,IAAI,YAAY;AAGhB,SAAS,cAAcG,QAAO;AACpC,cAAYA;AACb;AAYO,SAAS,gBAAgB,UAAU,MAAM,WAAW,MAAM,QAAQ,YAAY;AACpF,QAAM,SAAS;AAEf,cAAY;AAAA,IACX;AAAA,IACA,MAAM,UAAU,QAAQ;AAAA,IACxB;AAAA,IACA;AAAA,IACA;AAAA,IACA,GAAG;AAAA,EACJ;AAEA,MAAI;AACH,WAAO,SAAS;AAAA,EACjB,UAAE;AACD,gBAAY;AAAA,EACb;AACD;AAYO,IAAI,iCAAiC;AAGrC,SAAS,mCAAmC,IAAI;AACtD,mCAAiC;AAClC;AAUO,SAAS,WAAW,KAAK;AAC/B,QAAM,cAAc,wBAAwB,YAAY;AACxD,QAAM;AAAA;AAAA,IAA2B,YAAY,IAAI,GAAG;AAAA;AACpD,SAAO;AACR;AAcO,SAAS,WAAW,KAAK,SAAS;AACxC,QAAM,cAAc,wBAAwB,YAAY;AACxD,cAAY,IAAI,KAAK,OAAO;AAC5B,SAAO;AACR;AASO,SAAS,WAAW,KAAK;AAC/B,QAAM,cAAc,wBAAwB,YAAY;AACxD,SAAO,YAAY,IAAI,GAAG;AAC3B;AAUO,SAAS,iBAAiB;AAChC,QAAM,cAAc,wBAAwB,gBAAgB;AAC5D;AAAA;AAAA,IAAyB;AAAA;AAC1B;AAQO,SAAS,KAAK,OAAO,QAAQ,OAAO,IAAI;AAC9C,MAAI,MAAO,oBAAoB;AAAA,IAC9B,GAAG;AAAA,IACH,GAAG;AAAA,IACH,GAAG;AAAA,IACH,GAAG;AAAA,IACH,GAAG;AAAA,IACH,GAAG;AAAA,IACH,GAAG;AAAA,IACH,GAAG;AAAA,EACJ;AAEA,MAAI,oBAAoB,CAAC,OAAO;AAC/B,sBAAkB,IAAI;AAAA,MACrB,GAAG;AAAA,MACH,GAAG;AAAA,MACH,IAAI,CAAC;AAAA,MACL,IAAI,OAAO,KAAK;AAAA,IACjB;AAAA,EACD;AAEA,WAAS,MAAM;AACkB,IAAC,IAAK,IAAI;AAAA,EAC3C,CAAC;AAED,MAAI,cAAK;AAER,sBAAkB,WAAW;AAC7B,qCAAiC;AAAA,EAClC;AACD;AAOO,SAAS,IAAI,WAAW;AAjL/B;AAkLC,QAAM,qBAAqB;AAC3B,MAAI,uBAAuB,MAAM;AAChC,QAAI,cAAc,QAAW;AAC5B,yBAAmB,IAAI;AAAA,IACxB;AACA,UAAM,oBAAoB,mBAAmB;AAC7C,QAAI,sBAAsB,MAAM;AAC/B,UAAI,kBAAkB;AACtB,UAAI,oBAAoB;AACxB,yBAAmB,IAAI;AACvB,UAAI;AACH,iBAAS,IAAI,GAAG,IAAI,kBAAkB,QAAQ,KAAK;AAClD,cAAI,mBAAmB,kBAAkB,CAAC;AAC1C,4BAAkB,iBAAiB,MAAM;AACzC,8BAAoB,iBAAiB,QAAQ;AAC7C,6BAAmB,iBAAiB,EAAE;AAAA,QACvC;AAAA,MACD,UAAE;AACD,0BAAkB,eAAe;AACjC,4BAAoB,iBAAiB;AAAA,MACtC;AAAA,IACD;AACA,wBAAoB,mBAAmB;AACvC,QAAI,cAAK;AACR,yCAAiC,wBAAmB,MAAnB,mBAAsB,aAAY;AAAA,IACpE;AACA,uBAAmB,IAAI;AAAA,EACxB;AAGA,SAAO;AAAA,EAA+B,CAAC;AACxC;AAGO,SAAS,WAAW;AAC1B,SAAO,CAAC,oBAAqB,sBAAsB,QAAQ,kBAAkB,MAAM;AACpF;AAMA,SAAS,wBAAwB,MAAM;AACtC,MAAI,sBAAsB,MAAM;AAC/B,gCAA4B,IAAI;AAAA,EACjC;AAEA,SAAQ,kBAAkB,MAAlB,kBAAkB,IAAM,IAAI,IAAI,mBAAmB,iBAAiB,KAAK,MAAS;AAC3F;AAMA,SAAS,mBAAmBC,oBAAmB;AAC9C,MAAI,SAASA,mBAAkB;AAC/B,SAAO,WAAW,MAAM;AACvB,UAAM,cAAc,OAAO;AAC3B,QAAI,gBAAgB,MAAM;AACzB,aAAO;AAAA,IACR;AACA,aAAS,OAAO;AAAA,EACjB;AACA,SAAO;AACR;;;ACtNO,SAAS,QAAQ,IAAI;AAC3B,MAAI,QAAQ,UAAU;AACtB,MAAI,iBACH,oBAAoB,SAAS,gBAAgB,IAAI,aAAa;AAAA;AAAA,IACnC;AAAA,MACxB;AAEJ,MAAI,kBAAkB,QAAS,mBAAmB,SAAS,eAAe,IAAI,aAAa,GAAI;AAC9F,aAAS;AAAA,EACV,OAAO;AAGN,kBAAc,KAAK;AAAA,EACpB;AAGA,QAAM,SAAS;AAAA,IACd,KAAK;AAAA,IACL,MAAM;AAAA,IACN,SAAS;AAAA,IACT;AAAA,IACA,GAAG;AAAA,IACH;AAAA,IACA,WAAW;AAAA,IACX,IAAI;AAAA,IACJ;AAAA;AAAA,MAAqB;AAAA;AAAA,IACrB,IAAI;AAAA,IACJ,QAAQ,kBAAkB;AAAA,IAC1B,IAAI;AAAA,EACL;AAEA,MAAI,gBAAO,mBAAmB;AAC7B,WAAO,UAAU,UAAU,WAAW;AAAA,EACvC;AAEA,SAAO;AACR;AAQO,SAAS,aAAa,IAAI;AAChC,QAAM,IAAI,QAAQ,EAAE;AAEpB,sBAAoB,CAAC;AAErB,SAAO;AACR;AAQO,SAAS,mBAAmB,IAAI;AACtC,QAAM,SAAS,QAAQ,EAAE;AACzB,SAAO,SAAS;AAChB,SAAO;AACR;AAMO,SAAS,wBAAwBC,UAAS;AAChD,MAAI,UAAUA,SAAQ;AAEtB,MAAI,YAAY,MAAM;AACrB,IAAAA,SAAQ,UAAU;AAElB,aAAS,IAAI,GAAG,IAAI,QAAQ,QAAQ,KAAK,GAAG;AAC3C;AAAA;AAAA,QAAsC,QAAQ,CAAC;AAAA,MAAE;AAAA,IAClD;AAAA,EACD;AACD;AAOA,IAAI,QAAQ,CAAC;AAMb,SAAS,0BAA0BA,UAAS;AAC3C,MAAI,SAASA,SAAQ;AACrB,SAAO,WAAW,MAAM;AACvB,SAAK,OAAO,IAAI,aAAa,GAAG;AAC/B;AAAA;AAAA,QAA8B;AAAA;AAAA,IAC/B;AACA,aAAS,OAAO;AAAA,EACjB;AACA,SAAO;AACR;AAOO,SAAS,gBAAgBA,UAAS;AACxC,MAAI;AACJ,MAAI,qBAAqB;AAEzB,oBAAkB,0BAA0BA,QAAO,CAAC;AAEpD,MAAI,cAAK;AACR,QAAI,uBAAuB;AAC3B,wBAAoB,oBAAI,IAAI,CAAC;AAC7B,QAAI;AACH,UAAI,MAAM,SAASA,QAAO,GAAG;AAC5B,QAAE,wBAAwB;AAAA,MAC3B;AAEA,YAAM,KAAKA,QAAO;AAElB,8BAAwBA,QAAO;AAC/B,cAAQ,gBAAgBA,QAAO;AAAA,IAChC,UAAE;AACD,wBAAkB,kBAAkB;AACpC,0BAAoB,oBAAoB;AACxC,YAAM,IAAI;AAAA,IACX;AAAA,EACD,OAAO;AACN,QAAI;AACH,8BAAwBA,QAAO;AAC/B,cAAQ,gBAAgBA,QAAO;AAAA,IAChC,UAAE;AACD,wBAAkB,kBAAkB;AAAA,IACrC;AAAA,EACD;AAEA,SAAO;AACR;AAMO,SAAS,eAAeA,UAAS;AACvC,MAAI,QAAQ,gBAAgBA,QAAO;AAEnC,MAAI,CAACA,SAAQ,OAAO,KAAK,GAAG;AAC3B,IAAAA,SAAQ,IAAI;AACZ,IAAAA,SAAQ,KAAK,wBAAwB;AAAA,EACtC;AAIA,MAAI,qBAAsB;AAE1B,MAAI,UACF,kBAAkBA,SAAQ,IAAI,aAAa,MAAMA,SAAQ,SAAS,OAAO,cAAc;AAEzF,oBAAkBA,UAAS,MAAM;AAClC;;;ACxJO,IAAI,kBAAkB,oBAAI,IAAI;AAG9B,IAAM,aAAa,oBAAI,IAAI;AAK3B,SAAS,oBAAoB,GAAG;AACtC,oBAAkB;AACnB;AASO,SAAS,OAAO,GAAGC,QAAO;AAEhC,MAAI,SAAS;AAAA,IACZ,GAAG;AAAA;AAAA,IACH;AAAA,IACA,WAAW;AAAA,IACX;AAAA,IACA,IAAI;AAAA,IACJ,IAAI;AAAA,EACL;AAEA,MAAI,gBAAO,mBAAmB;AAC7B,WAAO,UAAUA,UAAS,UAAU,WAAW;AAC/C,WAAO,UAAU;AACjB,WAAO,oBAAoB;AAC3B,WAAO,QAAQ;AAAA,EAChB;AAEA,SAAO;AACR;AAQO,SAAS,MAAM,GAAGA,QAAO;AAC/B,QAAM,IAAI,OAAO,GAAGA,MAAK;AAEzB,sBAAoB,CAAC;AAErB,SAAO;AACR;AASO,SAAS,eAAe,eAAe,YAAY,OAAO,YAAY,MAAM;AAnGnF;AAoGC,QAAM,IAAI,OAAO,aAAa;AAC9B,MAAI,CAAC,WAAW;AACf,MAAE,SAAS;AAAA,EACZ;AAIA,MAAI,oBAAoB,aAAa,sBAAsB,QAAQ,kBAAkB,MAAM,MAAM;AAChG,MAAC,uBAAkB,GAAE,MAApB,GAAoB,IAAM,CAAC,IAAG,KAAK,CAAC;AAAA,EACtC;AAEA,SAAO;AACR;AAOO,SAAS,OAAOC,SAAQ,OAAO;AACrC;AAAA,IACCA;AAAA,IACA,QAAQ,MAAM,IAAIA,OAAM,CAAC;AAAA,EAC1B;AACA,SAAO;AACR;AASO,SAAS,IAAIA,SAAQ,OAAO,eAAe,OAAO;AAtIzD;AAuIC,MACC,oBAAoB;AAAA;AAAA,GAGnB,CAAC,eAAe,gBAAgB,IAAI,oBAAoB,MACzD,SAAS,MACR,gBAAgB,KAAK,UAAU,eAAe,qBAAqB,KACpE,IAAE,6CAAkB,cAAa,mBAAmB,iBAAiB,QAAQ,SAASA,OAAM,IAC3F;AACD,IAAE,sBAAsB;AAAA,EACzB;AAEA,MAAI,YAAY,eAAe,MAAM,KAAK,IAAI;AAE9C,MAAI,cAAK;AACR;AAAA,MAAU;AAAA;AAAA,MAAkCA,QAAO;AAAA,IAAM;AAAA,EAC1D;AAEA,SAAO,aAAaA,SAAQ,SAAS;AACtC;AAQO,SAAS,aAAaA,SAAQ,OAAO;AAC3C,MAAI,CAACA,QAAO,OAAO,KAAK,GAAG;AAC1B,QAAI,YAAYA,QAAO;AAEvB,QAAI,sBAAsB;AACzB,iBAAW,IAAIA,SAAQ,KAAK;AAAA,IAC7B,OAAO;AACN,iBAAW,IAAIA,SAAQ,SAAS;AAAA,IACjC;AAEA,IAAAA,QAAO,IAAI;AAEX,QAAI,gBAAO,mBAAmB;AAC7B,MAAAA,QAAO,UAAU,UAAU,WAAW;AAEtC,UAAI,kBAAkB,MAAM;AAC3B,QAAAA,QAAO,oBAAoB;AAAA,MAC5B;AAAA,IACD;AAEA,SAAKA,QAAO,IAAI,aAAa,GAAG;AAE/B,WAAKA,QAAO,IAAI,WAAW,GAAG;AAC7B;AAAA;AAAA,UAAwCA;AAAA,QAAO;AAAA,MAChD;AACA,wBAAkBA,UAASA,QAAO,IAAI,aAAa,IAAI,QAAQ,WAAW;AAAA,IAC3E;AAEA,IAAAA,QAAO,KAAK,wBAAwB;AAEpC,mBAAeA,SAAQ,KAAK;AAM5B,QACC,SAAS,KACT,kBAAkB,SACjB,cAAc,IAAI,WAAW,MAC7B,cAAc,KAAK,gBAAgB,kBAAkB,GACrD;AACD,UAAI,qBAAqB,MAAM;AAC9B,6BAAqB,CAACA,OAAM,CAAC;AAAA,MAC9B,OAAO;AACN,yBAAiB,KAAKA,OAAM;AAAA,MAC7B;AAAA,IACD;AAEA,QAAI,gBAAO,gBAAgB,OAAO,GAAG;AACpC,YAAM,WAAW,MAAM,KAAK,eAAe;AAE3C,iBAAWC,WAAU,UAAU;AAG9B,aAAKA,QAAO,IAAI,WAAW,GAAG;AAC7B,4BAAkBA,SAAQ,WAAW;AAAA,QACtC;AACA,YAAI,gBAAgBA,OAAM,GAAG;AAC5B,wBAAcA,OAAM;AAAA,QACrB;AAAA,MACD;AAEA,sBAAgB,MAAM;AAAA,IACvB;AAAA,EACD;AAEA,SAAO;AACR;AAQO,SAAS,OAAOD,SAAQ,IAAI,GAAG;AACrC,MAAI,QAAQ,IAAIA,OAAM;AACtB,MAAI,SAAS,MAAM,IAAI,UAAU;AAEjC,MAAIA,SAAQ,KAAK;AAGjB,SAAO;AACR;AAQO,SAAS,WAAWA,SAAQ,IAAI,GAAG;AACzC,MAAI,QAAQ,IAAIA,OAAM;AAGtB,SAAO,IAAIA,SAAQ,MAAM,IAAI,EAAE,QAAQ,EAAE,KAAK;AAC/C;AAOA,SAAS,eAAe,QAAQ,QAAQ;AACvC,MAAI,YAAY,OAAO;AACvB,MAAI,cAAc,KAAM;AAExB,MAAI,QAAQ,SAAS;AACrB,MAAI,SAAS,UAAU;AAEvB,WAAS,IAAI,GAAG,IAAI,QAAQ,KAAK;AAChC,QAAI,WAAW,UAAU,CAAC;AAC1B,QAAI,QAAQ,SAAS;AAGrB,SAAK,QAAQ,WAAW,EAAG;AAG3B,QAAI,CAAC,SAAS,aAAa,cAAe;AAG1C,QAAI,iBAAQ,QAAQ,oBAAoB,GAAG;AAC1C,sBAAgB,IAAI,QAAQ;AAC5B;AAAA,IACD;AAEA,sBAAkB,UAAU,MAAM;AAGlC,SAAK,SAAS,QAAQ,cAAc,GAAG;AACtC,WAAK,QAAQ,aAAa,GAAG;AAC5B;AAAA;AAAA,UAAuC;AAAA,UAAW;AAAA,QAAW;AAAA,MAC9D,OAAO;AACN;AAAA;AAAA,UAAuC;AAAA,QAAS;AAAA,MACjD;AAAA,IACD;AAAA,EACD;AACD;;;AC3PO,SAAS,gBAAgB,MAAM;AACrC,MAAI,kBAAkB,QAAQ,oBAAoB,MAAM;AACvD,IAAE,cAAc,IAAI;AAAA,EACrB;AAEA,MAAI,oBAAoB,SAAS,gBAAgB,IAAI,aAAa,KAAK,kBAAkB,MAAM;AAC9F,IAAE,0BAA0B;AAAA,EAC7B;AAEA,MAAI,sBAAsB;AACzB,IAAE,mBAAmB,IAAI;AAAA,EAC1B;AACD;AAMA,SAAS,YAAYE,SAAQ,eAAe;AAC3C,MAAI,cAAc,cAAc;AAChC,MAAI,gBAAgB,MAAM;AACzB,kBAAc,OAAO,cAAc,QAAQA;AAAA,EAC5C,OAAO;AACN,gBAAY,OAAOA;AACnB,IAAAA,QAAO,OAAO;AACd,kBAAc,OAAOA;AAAA,EACtB;AACD;AASA,SAAS,cAAc,MAAM,IAAI,MAAMC,QAAO,MAAM;AACnD,MAAI,SAAS;AAEb,MAAI,cAAK;AAER,WAAO,WAAW,SAAS,OAAO,IAAI,oBAAoB,GAAG;AAC5D,eAAS,OAAO;AAAA,IACjB;AAAA,EACD;AAGA,MAAID,UAAS;AAAA,IACZ,KAAK;AAAA,IACL,MAAM;AAAA,IACN,aAAa;AAAA,IACb,WAAW;AAAA,IACX,GAAG,OAAO;AAAA,IACV,OAAO;AAAA,IACP;AAAA,IACA,MAAM;AAAA,IACN,MAAM;AAAA,IACN;AAAA,IACA,GAAG,UAAU,OAAO;AAAA,IACpB,MAAM;AAAA,IACN,UAAU;AAAA,IACV,aAAa;AAAA,IACb,IAAI;AAAA,IACJ,IAAI;AAAA,EACL;AAEA,MAAI,cAAK;AACR,IAAAA,QAAO,qBAAqB;AAAA,EAC7B;AAEA,MAAI,MAAM;AACT,QAAI;AACH,oBAAcA,OAAM;AACpB,MAAAA,QAAO,KAAK;AAAA,IACb,SAAS,GAAG;AACX,qBAAeA,OAAM;AACrB,YAAM;AAAA,IACP;AAAA,EACD,WAAW,OAAO,MAAM;AACvB,oBAAgBA,OAAM;AAAA,EACvB;AAIA,MAAI,QACH,QACAA,QAAO,SAAS,QAChBA,QAAO,UAAU,QACjBA,QAAO,gBAAgB,QACvBA,QAAO,aAAa,SACnBA,QAAO,KAAK,mBAAmB,sBAAsB;AAEvD,MAAI,CAAC,SAASC,OAAM;AACnB,QAAI,WAAW,MAAM;AACpB,kBAAYD,SAAQ,MAAM;AAAA,IAC3B;AAGA,QAAI,oBAAoB,SAAS,gBAAgB,IAAI,aAAa,GAAG;AACpE,UAAIE;AAAA;AAAA,QAAkC;AAAA;AACtC,OAACA,SAAQ,YAARA,SAAQ,UAAY,CAAC,IAAG,KAAKF,OAAM;AAAA,IACrC;AAAA,EACD;AAEA,SAAOA;AACR;AAMO,SAAS,kBAAkB;AACjC,SAAO,oBAAoB,QAAQ,CAAC;AACrC;AAKO,SAAS,SAAS,IAAI;AAC5B,QAAMA,UAAS,cAAc,eAAe,MAAM,KAAK;AACvD,oBAAkBA,SAAQ,KAAK;AAC/B,EAAAA,QAAO,WAAW;AAClB,SAAOA;AACR;AAMO,SAAS,YAAY,IAAI;AAC/B,kBAAgB,SAAS;AAIzB,MAAI,QACH,kBAAkB,SACjB,cAAc,IAAI,mBAAmB,KACtC,sBAAsB,QACtB,CAAC,kBAAkB;AAEpB,MAAI,cAAK;AACR,oBAAgB,IAAI,QAAQ;AAAA,MAC3B,OAAO;AAAA,IACR,CAAC;AAAA,EACF;AAEA,MAAI,OAAO;AACV,QAAI;AAAA;AAAA,MAA2C;AAAA;AAC/C,KAAC,QAAQ,MAAR,QAAQ,IAAM,CAAC,IAAG,KAAK;AAAA,MACvB;AAAA,MACA,QAAQ;AAAA,MACR,UAAU;AAAA,IACX,CAAC;AAAA,EACF,OAAO;AACN,WAAO,mBAAmB,EAAE;AAAA,EAC7B;AACD;AAKO,SAAS,mBAAmB,IAAI;AACtC,SAAO,cAAc,SAAS,aAAa,IAAI,KAAK;AACrD;AAOO,SAAS,gBAAgB,IAAI;AACnC,kBAAgB,aAAa;AAC7B,MAAI,cAAK;AACR,oBAAgB,IAAI,QAAQ;AAAA,MAC3B,OAAO;AAAA,IACR,CAAC;AAAA,EACF;AACA,SAAO,cAAc,gBAAgB,aAAa,IAAI,IAAI;AAC3D;AAGO,SAAS,eAAe,IAAI;AAClC,SAAO,cAAc,gBAAgB,IAAI,IAAI;AAC9C;AAOO,SAAS,YAAY,IAAI;AAC/B,QAAMA,UAAS,cAAc,aAAa,IAAI,IAAI;AAElD,SAAO,MAAM;AACZ,mBAAeA,OAAM;AAAA,EACtB;AACD;AAOO,SAAS,eAAe,IAAI;AAClC,QAAMA,UAAS,cAAc,aAAa,IAAI,IAAI;AAElD,SAAO,CAAC,UAAU,CAAC,MAAM;AACxB,WAAO,IAAI,QAAQ,CAAC,WAAW;AAC9B,UAAI,QAAQ,OAAO;AAClB,qBAAaA,SAAQ,MAAM;AAC1B,yBAAeA,OAAM;AACrB,iBAAO,MAAS;AAAA,QACjB,CAAC;AAAA,MACF,OAAO;AACN,uBAAeA,OAAM;AACrB,eAAO,MAAS;AAAA,MACjB;AAAA,IACD,CAAC;AAAA,EACF;AACD;AAMO,SAAS,OAAO,IAAI;AAC1B,SAAO,cAAc,QAAQ,IAAI,KAAK;AACvC;AAOO,SAAS,kBAAkB,MAAM,IAAI;AAC3C,MAAI;AAAA;AAAA,IAAiD;AAAA;AAGrD,MAAI,QAAQ,EAAE,QAAQ,MAAM,KAAK,MAAM;AACvC,UAAQ,EAAE,GAAG,KAAK,KAAK;AAEvB,QAAM,SAAS,cAAc,MAAM;AAClC,SAAK;AAIL,QAAI,MAAM,IAAK;AAEf,UAAM,MAAM;AACZ,QAAI,QAAQ,EAAE,IAAI,IAAI;AACtB,YAAQ,EAAE;AAAA,EACX,CAAC;AACF;AAEO,SAAS,0BAA0B;AACzC,MAAI;AAAA;AAAA,IAAiD;AAAA;AAErD,gBAAc,MAAM;AACnB,QAAI,CAAC,IAAI,QAAQ,EAAE,EAAE,EAAG;AAGxB,aAAS,SAAS,QAAQ,EAAE,IAAI;AAC/B,UAAIA,UAAS,MAAM;AAInB,WAAKA,QAAO,IAAI,WAAW,GAAG;AAC7B,0BAAkBA,SAAQ,WAAW;AAAA,MACtC;AAEA,UAAI,gBAAgBA,OAAM,GAAG;AAC5B,sBAAcA,OAAM;AAAA,MACrB;AAEA,YAAM,MAAM;AAAA,IACb;AAEA,YAAQ,EAAE,GAAG,IAAI;AAAA,EAClB,CAAC;AACF;AAMO,SAAS,cAAc,IAAI;AACjC,SAAO,cAAc,eAAe,IAAI,IAAI;AAC7C;AAQO,SAAS,gBAAgB,IAAI,SAAS,CAAC,GAAG,IAAI,SAAS;AAC7D,MAAI,cAAK;AAGR,WAAO,cAAc,MAAM;AAC1B,UAAI;AAAA;AAAA,QAA+B;AAAA;AACnC,UAAI,QAAQ,MAAM,GAAG,GAAGG,UAAS,IAAI,GAAG,CAAC;AAEzC,sBAAgB,MAAM,IAAI,QAAQ,EAAE,OAAO,eAAe,CAAC;AAC3D,sBAAgB,OAAO,QAAQ,EAAE,OAAO,eAAe,CAAC;AAExD,YAAMA,YAAW,OAAO,IAAI,CAAC;AAC7B,YAAM,KAAK;AAAA,IACZ,CAAC;AAAA,EACF;AAEA,QAAM,WAAW,OAAO,IAAI,CAAC;AAC7B,SAAO,MAAM,MAAM,GAAG,GAAG,SAAS,IAAI,GAAG,CAAC,CAAC;AAC5C;AAMO,SAAS,MAAM,IAAI,QAAQ,GAAG;AACpC,MAAIH,UAAS,cAAc,gBAAgB,eAAe,OAAO,IAAI,IAAI;AACzE,MAAI,cAAK;AACR,IAAAA,QAAO,YAAY;AAAA,EACpB;AACA,SAAOA;AACR;AAMO,SAAS,OAAO,IAAIC,QAAO,MAAM;AACvC,SAAO,cAAc,gBAAgB,eAAe,IAAI,MAAMA,KAAI;AACnE;AAKO,SAAS,wBAAwBD,SAAQ;AAC/C,MAAII,YAAWJ,QAAO;AACtB,MAAII,cAAa,MAAM;AACtB,UAAM,+BAA+B;AACrC,UAAM,oBAAoB;AAC1B,6BAAyB,IAAI;AAC7B,wBAAoB,IAAI;AACxB,QAAI;AACH,MAAAA,UAAS,KAAK,IAAI;AAAA,IACnB,UAAE;AACD,+BAAyB,4BAA4B;AACrD,0BAAoB,iBAAiB;AAAA,IACtC;AAAA,EACD;AACD;AAOO,SAAS,wBAAwB,QAAQ,aAAa,OAAO;AAxZpE;AAyZC,MAAIJ,UAAS,OAAO;AACpB,SAAO,QAAQ,OAAO,OAAO;AAE7B,SAAOA,YAAW,MAAM;AACvB,UAAAA,QAAO,OAAP,mBAAW,MAAM;AAEjB,QAAIK,QAAOL,QAAO;AAElB,SAAKA,QAAO,IAAI,iBAAiB,GAAG;AAEnC,MAAAA,QAAO,SAAS;AAAA,IACjB,OAAO;AACN,qBAAeA,SAAQ,UAAU;AAAA,IAClC;AAEA,IAAAA,UAASK;AAAA,EACV;AACD;AAMO,SAAS,8BAA8B,QAAQ;AACrD,MAAIL,UAAS,OAAO;AAEpB,SAAOA,YAAW,MAAM;AACvB,QAAIK,QAAOL,QAAO;AAClB,SAAKA,QAAO,IAAI,mBAAmB,GAAG;AACrC,qBAAeA,OAAM;AAAA,IACtB;AACA,IAAAA,UAASK;AAAA,EACV;AACD;AAOO,SAAS,eAAeL,SAAQ,aAAa,MAAM;AACzD,MAAI,UAAU;AAEd,OACE,eAAeA,QAAO,IAAI,iBAAiB,MAC5CA,QAAO,gBAAgB,QACvBA,QAAO,cAAc,MACpB;AACD;AAAA,MAAkBA,QAAO;AAAA;AAAA,MAA0CA,QAAO;AAAA,IAAU;AACpF,cAAU;AAAA,EACX;AAEA,0BAAwBA,SAAQ,cAAc,CAAC,OAAO;AACtD,mBAAiBA,SAAQ,CAAC;AAC1B,oBAAkBA,SAAQ,SAAS;AAEnC,MAAI,cAAcA,QAAO;AAEzB,MAAI,gBAAgB,MAAM;AACzB,eAAW,cAAc,aAAa;AACrC,iBAAW,KAAK;AAAA,IACjB;AAAA,EACD;AAEA,0BAAwBA,OAAM;AAE9B,MAAI,SAASA,QAAO;AAGpB,MAAI,WAAW,QAAQ,OAAO,UAAU,MAAM;AAC7C,kBAAcA,OAAM;AAAA,EACrB;AAEA,MAAI,cAAK;AACR,IAAAA,QAAO,qBAAqB;AAAA,EAC7B;AAIA,EAAAA,QAAO,OACNA,QAAO,OACPA,QAAO,WACPA,QAAO,MACPA,QAAO,OACPA,QAAO,KACPA,QAAO,cACPA,QAAO,YACPA,QAAO,KACN;AACH;AAOO,SAAS,kBAAkB,MAAM,KAAK;AAC5C,SAAO,SAAS,MAAM;AAErB,QAAIK,QAAO,SAAS,MAAM;AAAA;AAAA,MAAoC,iBAAiB,IAAI;AAAA;AAEnF,SAAK,OAAO;AACZ,WAAOA;AAAA,EACR;AACD;AAOO,SAAS,cAAcL,SAAQ;AACrC,MAAI,SAASA,QAAO;AACpB,MAAI,OAAOA,QAAO;AAClB,MAAIK,QAAOL,QAAO;AAElB,MAAI,SAAS,KAAM,MAAK,OAAOK;AAC/B,MAAIA,UAAS,KAAM,CAAAA,MAAK,OAAO;AAE/B,MAAI,WAAW,MAAM;AACpB,QAAI,OAAO,UAAUL,QAAQ,QAAO,QAAQK;AAC5C,QAAI,OAAO,SAASL,QAAQ,QAAO,OAAO;AAAA,EAC3C;AACD;AAWO,SAAS,aAAaA,SAAQ,UAAU;AAE9C,MAAI,cAAc,CAAC;AAEnB,iBAAeA,SAAQ,aAAa,IAAI;AAExC,sBAAoB,aAAa,MAAM;AACtC,mBAAeA,OAAM;AACrB,QAAI,SAAU,UAAS;AAAA,EACxB,CAAC;AACF;AAMO,SAAS,oBAAoB,aAAa,IAAI;AACpD,MAAI,YAAY,YAAY;AAC5B,MAAI,YAAY,GAAG;AAClB,QAAI,QAAQ,MAAM,EAAE,aAAa,GAAG;AACpC,aAAS,cAAc,aAAa;AACnC,iBAAW,IAAI,KAAK;AAAA,IACrB;AAAA,EACD,OAAO;AACN,OAAG;AAAA,EACJ;AACD;AAOO,SAAS,eAAeA,SAAQ,aAAa,OAAO;AAC1D,OAAKA,QAAO,IAAI,WAAW,EAAG;AAC9B,EAAAA,QAAO,KAAK;AAEZ,MAAIA,QAAO,gBAAgB,MAAM;AAChC,eAAW,cAAcA,QAAO,aAAa;AAC5C,UAAI,WAAW,aAAa,OAAO;AAClC,oBAAY,KAAK,UAAU;AAAA,MAC5B;AAAA,IACD;AAAA,EACD;AAEA,MAAIM,SAAQN,QAAO;AAEnB,SAAOM,WAAU,MAAM;AACtB,QAAIC,WAAUD,OAAM;AACpB,QAAI,eAAeA,OAAM,IAAI,wBAAwB,MAAMA,OAAM,IAAI,mBAAmB;AAIxF,mBAAeA,QAAO,aAAa,cAAc,QAAQ,KAAK;AAC9D,IAAAA,SAAQC;AAAA,EACT;AACD;AAOO,SAAS,cAAcP,SAAQ;AACrC,kBAAgBA,SAAQ,IAAI;AAC7B;AAMA,SAAS,gBAAgBA,SAAQ,OAAO;AACvC,OAAKA,QAAO,IAAI,WAAW,EAAG;AAC9B,EAAAA,QAAO,KAAK;AAEZ,MAAIM,SAAQN,QAAO;AAEnB,SAAOM,WAAU,MAAM;AACtB,QAAIC,WAAUD,OAAM;AACpB,QAAI,eAAeA,OAAM,IAAI,wBAAwB,MAAMA,OAAM,IAAI,mBAAmB;AAIxF,oBAAgBA,QAAO,cAAc,QAAQ,KAAK;AAClD,IAAAA,SAAQC;AAAA,EACT;AAEA,MAAIP,QAAO,gBAAgB,MAAM;AAChC,eAAW,cAAcA,QAAO,aAAa;AAC5C,UAAI,WAAW,aAAa,OAAO;AAClC,mBAAW,GAAG;AAAA,MACf;AAAA,IACD;AAAA,EACD;AACD;;;AC1nBA,IAAM,wBACL,OAAO,wBAAwB,cAC5B,CAA2B,OAAO,WAAW,IAAI,CAAC,IAClD;AAGJ,IAAI,cAAc,CAAC;AAGnB,IAAI,aAAa,CAAC;AAElB,SAAS,kBAAkB;AAC1B,MAAI,QAAQ;AACZ,gBAAc,CAAC;AACf,UAAQ,KAAK;AACd;AAEA,SAAS,iBAAiB;AACzB,MAAI,QAAQ;AACZ,eAAa,CAAC;AACd,UAAQ,KAAK;AACd;AAKO,SAAS,iBAAiB,IAAI;AACpC,MAAI,YAAY,WAAW,GAAG;AAC7B,mBAAe,eAAe;AAAA,EAC/B;AAEA,cAAY,KAAK,EAAE;AACpB;AAKO,SAAS,gBAAgB,IAAI;AACnC,MAAI,WAAW,WAAW,GAAG;AAC5B,0BAAsB,cAAc;AAAA,EACrC;AAEA,aAAW,KAAK,EAAE;AACnB;AAKO,SAAS,cAAc;AAC7B,MAAI,YAAY,SAAS,GAAG;AAC3B,oBAAgB;AAAA,EACjB;AAEA,MAAI,WAAW,SAAS,GAAG;AAC1B,mBAAe;AAAA,EAChB;AACD;;;AC/CO,SAAS,aAAa,OAAO;AACnC,MAAIQ;AAAA;AAAA,IAAgC;AAAA;AAEpC,MAAI,gBAAO,iBAAiB,OAAO;AAClC,iBAAa,OAAOA,OAAM;AAAA,EAC3B;AAEA,OAAKA,QAAO,IAAI,gBAAgB,GAAG;AAGlC,SAAKA,QAAO,IAAI,qBAAqB,GAAG;AACvC,YAAM;AAAA,IACP;AAGA,IAAAA,QAAO,GAAG,KAAK;AAAA,EAChB,OAAO;AAEN,0BAAsB,OAAOA,OAAM;AAAA,EACpC;AACD;AAMO,SAAS,sBAAsB,OAAOA,SAAQ;AACpD,SAAOA,YAAW,MAAM;AACvB,SAAKA,QAAO,IAAI,qBAAqB,GAAG;AACvC,UAAI;AACqB,QAACA,QAAO,EAAG,MAAM,KAAK;AAC9C;AAAA,MACD,QAAQ;AAAA,MAAC;AAAA,IACV;AAEA,IAAAA,UAASA,QAAO;AAAA,EACjB;AAEA,QAAM;AACP;AAGA,IAAM,kBAAkB,oBAAI,QAAQ;AAOpC,SAAS,aAAa,OAAOA,SAAQ;AA7DrC;AA8DC,MAAI,gBAAgB,IAAI,KAAK,EAAG;AAChC,kBAAgB,IAAI,KAAK;AAEzB,QAAM,qBAAqB,eAAe,OAAO,SAAS;AAI1D,MAAI,sBAAsB,CAAC,mBAAmB,aAAc;AAE5D,MAAI,SAAS,aAAa,OAAO;AACjC,MAAI,kBAAkB;AAAA,EAAK,MAAM,QAAM,KAAAA,QAAO,OAAP,mBAAW,SAAQ,WAAW;AACrE,MAAI,UAAUA,QAAO;AAErB,SAAO,YAAY,MAAM;AACxB,uBAAmB;AAAA,EAAK,MAAM,OAAM,aAAQ,aAAR,mBAAmB,UAAU,MAAM,KAAK,KAAK;AACjF,cAAU,QAAQ;AAAA,EACnB;AAEA,kBAAgB,OAAO,WAAW;AAAA,IACjC,OAAO,MAAM,UAAU;AAAA,EAAK,eAAe;AAAA;AAAA,EAC5C,CAAC;AAED,MAAI,MAAM,OAAO;AAEhB,oBAAgB,OAAO,SAAS;AAAA,MAC/B,OAAO,MAAM,MACX,MAAM,IAAI,EACV,OAAO,CAAC,SAAS,CAAC,KAAK,SAAS,qBAAqB,CAAC,EACtD,KAAK,IAAI;AAAA,IACZ,CAAC;AAAA,EACF;AACD;;;AChDA,IAAI,cAAc;AAGlB,IAAI,wBAAwB;AAE5B,IAAI,qBAAqB;AAElB,IAAI,uBAAuB;AAG3B,SAAS,yBAAyB,OAAO;AAC/C,yBAAuB;AACxB;AAKA,IAAI,sBAAsB,CAAC;AAG3B,IAAI,mBAAmB,CAAC;AAIjB,IAAI,kBAAkB;AAEtB,IAAI,aAAa;AAGjB,SAAS,oBAAoB,UAAU;AAC7C,oBAAkB;AACnB;AAGO,IAAI,gBAAgB;AAGpB,SAAS,kBAAkBC,SAAQ;AACzC,kBAAgBA;AACjB;AAOO,IAAI,mBAAmB;AAGvB,SAAS,oBAAoB,OAAO;AAC1C,MAAI,oBAAoB,QAAQ,gBAAgB,IAAI,oBAAoB;AACvE,QAAI,qBAAqB,MAAM;AAC9B,yBAAmB,EAAE,UAAU,iBAAiB,SAAS,CAAC,KAAK,EAAE;AAAA,IAClE,OAAO;AACN,uBAAiB,QAAQ,KAAK,KAAK;AAAA,IACpC;AAAA,EACD;AACD;AAQA,IAAI,WAAW;AAEf,IAAI,eAAe;AAOZ,IAAI,mBAAmB;AAGvB,SAAS,qBAAqB,OAAO;AAC3C,qBAAmB;AACpB;AAMA,IAAI,gBAAgB;AAGpB,IAAI,eAAe;AAIZ,IAAI,gBAAgB;AAGpB,IAAI,mBAAmB;AAOvB,SAAS,0BAA0B;AACzC,SAAO,EAAE;AACV;AAQO,SAAS,gBAAgB,UAAU;AA7J1C;AA8JC,MAAI,QAAQ,SAAS;AAErB,OAAK,QAAQ,WAAW,GAAG;AAC1B,WAAO;AAAA,EACR;AAEA,OAAK,QAAQ,iBAAiB,GAAG;AAChC,QAAI,eAAe,SAAS;AAC5B,QAAI,cAAc,QAAQ,aAAa;AAEvC,QAAI,iBAAiB,MAAM;AAC1B,UAAI;AACJ,UAAI;AACJ,UAAI,mBAAmB,QAAQ,kBAAkB;AACjD,UAAI,uBAAuB,cAAc,kBAAkB,QAAQ,CAAC;AACpE,UAAI,SAAS,aAAa;AAI1B,UAAI,mBAAmB,sBAAsB;AAC5C,YAAIC;AAAA;AAAA,UAAkC;AAAA;AACtC,YAAI,SAASA,SAAQ;AAErB,aAAK,IAAI,GAAG,IAAI,QAAQ,KAAK;AAC5B,uBAAa,aAAa,CAAC;AAK3B,cAAI,mBAAmB,GAAC,8CAAY,cAAZ,mBAAuB,SAASA,YAAU;AACjE,aAAC,WAAW,cAAX,WAAW,YAAc,CAAC,IAAG,KAAKA,QAAO;AAAA,UAC3C;AAAA,QACD;AAEA,YAAI,iBAAiB;AACpB,UAAAA,SAAQ,KAAK;AAAA,QACd;AAIA,YAAI,wBAAwB,WAAW,SAAS,OAAO,IAAI,aAAa,GAAG;AAC1E,UAAAA,SAAQ,KAAK;AAAA,QACd;AAAA,MACD;AAEA,WAAK,IAAI,GAAG,IAAI,QAAQ,KAAK;AAC5B,qBAAa,aAAa,CAAC;AAE3B,YAAI;AAAA;AAAA,UAAwC;AAAA,QAAW,GAAG;AACzD;AAAA;AAAA,YAAuC;AAAA,UAAW;AAAA,QACnD;AAEA,YAAI,WAAW,KAAK,SAAS,IAAI;AAChC,iBAAO;AAAA,QACR;AAAA,MACD;AAAA,IACD;AAIA,QAAI,CAAC,cAAe,kBAAkB,QAAQ,CAAC,eAAgB;AAC9D,wBAAkB,UAAU,KAAK;AAAA,IAClC;AAAA,EACD;AAEA,SAAO;AACR;AAOA,SAAS,2CAA2C,QAAQC,SAAQ,OAAO,MAAM;AAChF,MAAI,YAAY,OAAO;AACvB,MAAI,cAAc,KAAM;AAExB,WAAS,IAAI,GAAG,IAAI,UAAU,QAAQ,KAAK;AAC1C,QAAI,WAAW,UAAU,CAAC;AAE1B,SACC,qDAAkB,cAAa,mBAC/B,iBAAiB,QAAQ,SAAS,MAAM,GACvC;AACD;AAAA,IACD;AAEA,SAAK,SAAS,IAAI,aAAa,GAAG;AACjC;AAAA;AAAA,QAAmE;AAAA,QAAWA;AAAA,QAAQ;AAAA,MAAK;AAAA,IAC5F,WAAWA,YAAW,UAAU;AAC/B,UAAI,MAAM;AACT,0BAAkB,UAAU,KAAK;AAAA,MAClC,YAAY,SAAS,IAAI,WAAW,GAAG;AACtC,0BAAkB,UAAU,WAAW;AAAA,MACxC;AACA;AAAA;AAAA,QAAuC;AAAA,MAAS;AAAA,IACjD;AAAA,EACD;AACD;AAGO,SAAS,gBAAgB,UAAU;AAnQ1C;AAoQC,MAAI,gBAAgB;AACpB,MAAI,wBAAwB;AAC5B,MAAI,4BAA4B;AAChC,MAAI,oBAAoB;AACxB,MAAI,yBAAyB;AAC7B,MAAI,4BAA4B;AAChC,MAAI,6BAA6B;AACjC,MAAI,sBAAsB;AAE1B,MAAI,QAAQ,SAAS;AAErB;AAAA,EAA0C;AAC1C,iBAAe;AACf,qBAAmB;AACnB,mBACE,QAAQ,aAAa,MAAM,cAAc,CAAC,sBAAsB,oBAAoB;AACtF,qBAAmB,SAAS,gBAAgB,kBAAkB,IAAI,WAAW;AAE7E,qBAAmB;AACnB,wBAAsB,SAAS,GAAG;AAClC,eAAa;AACb;AAEA,WAAS,KAAK;AAEd,MAAI,SAAS,OAAO,MAAM;AACzB,aAAS,GAAG,MAAM,cAAc;AAChC,aAAS,KAAK;AAAA,EACf;AAEA,MAAI;AACH,QAAI;AAAA;AAAA,OAAkC,GAAG,SAAS,IAAI;AAAA;AACtD,QAAI,OAAO,SAAS;AAEpB,QAAI,aAAa,MAAM;AACtB,UAAI;AAEJ,uBAAiB,UAAU,YAAY;AAEvC,UAAI,SAAS,QAAQ,eAAe,GAAG;AACtC,aAAK,SAAS,eAAe,SAAS;AACtC,aAAK,IAAI,GAAG,IAAI,SAAS,QAAQ,KAAK;AACrC,eAAK,eAAe,CAAC,IAAI,SAAS,CAAC;AAAA,QACpC;AAAA,MACD,OAAO;AACN,iBAAS,OAAO,OAAO;AAAA,MACxB;AAEA,UACC,CAAC;AAAA,OAEC,QAAQ,aAAa;AAAA,MACoB,SAAU,cAAc,MAClE;AACD,aAAK,IAAI,cAAc,IAAI,KAAK,QAAQ,KAAK;AAC5C,YAAC,UAAK,CAAC,GAAE,cAAR,GAAQ,YAAc,CAAC,IAAG,KAAK,QAAQ;AAAA,QACzC;AAAA,MACD;AAAA,IACD,WAAW,SAAS,QAAQ,eAAe,KAAK,QAAQ;AACvD,uBAAiB,UAAU,YAAY;AACvC,WAAK,SAAS;AAAA,IACf;AAKA,QACC,SAAS,KACT,qBAAqB,QACrB,CAAC,cACD,SAAS,SACR,SAAS,KAAK,UAAU,cAAc,YAAY,GAClD;AACD,WAAK,IAAI,GAAG;AAAA,MAA6B,iBAAkB,QAAQ,KAAK;AACvE;AAAA,UACC,iBAAiB,CAAC;AAAA;AAAA,UACK;AAAA,QACxB;AAAA,MACD;AAAA,IACD;AAMA,QAAI,sBAAsB,QAAQ,sBAAsB,UAAU;AACjE;AAEA,UAAI,qBAAqB,MAAM;AAC9B,YAAI,8BAA8B,MAAM;AACvC,sCAA4B;AAAA,QAC7B,OAAO;AACN,oCAA0B,KAAK;AAAA,UAA4B,gBAAiB;AAAA,QAC7E;AAAA,MACD;AAAA,IACD;AAEA,WAAO;AAAA,EACR,SAAS,OAAO;AACf,iBAAa,KAAK;AAAA,EACnB,UAAE;AACD,eAAW;AACX,mBAAe;AACf,uBAAmB;AACnB,sBAAkB;AAClB,oBAAgB;AAChB,uBAAmB;AACnB,0BAAsB,0BAA0B;AAChD,iBAAa;AAEb,aAAS,KAAK;AAAA,EACf;AACD;AAQA,SAAS,gBAAgB,QAAQ,YAAY;AAC5C,MAAI,YAAY,WAAW;AAC3B,MAAI,cAAc,MAAM;AACvB,QAAI,QAAQ,SAAS,KAAK,WAAW,MAAM;AAC3C,QAAI,UAAU,IAAI;AACjB,UAAI,aAAa,UAAU,SAAS;AACpC,UAAI,eAAe,GAAG;AACrB,oBAAY,WAAW,YAAY;AAAA,MACpC,OAAO;AAEN,kBAAU,KAAK,IAAI,UAAU,UAAU;AACvC,kBAAU,IAAI;AAAA,MACf;AAAA,IACD;AAAA,EACD;AAGA,MACC,cAAc,SACb,WAAW,IAAI,aAAa;AAAA;AAAA;AAAA,GAI5B,aAAa,QAAQ,CAAC,SAAS,SAAS,UAAU,IAClD;AACD,sBAAkB,YAAY,WAAW;AAGzC,SAAK,WAAW,KAAK,UAAU,mBAAmB,GAAG;AACpD,iBAAW,KAAK;AAAA,IACjB;AAEA;AAAA;AAAA,MAAiD;AAAA,IAAW;AAC5D;AAAA;AAAA,MAA0C;AAAA,MAAa;AAAA,IAAC;AAAA,EACzD;AACD;AAOO,SAAS,iBAAiB,QAAQ,aAAa;AACrD,MAAI,eAAe,OAAO;AAC1B,MAAI,iBAAiB,KAAM;AAE3B,WAAS,IAAI,aAAa,IAAI,aAAa,QAAQ,KAAK;AACvD,oBAAgB,QAAQ,aAAa,CAAC,CAAC;AAAA,EACxC;AACD;AAMO,SAAS,cAAcA,SAAQ;AACrC,MAAI,QAAQA,QAAO;AAEnB,OAAK,QAAQ,eAAe,GAAG;AAC9B;AAAA,EACD;AAEA,oBAAkBA,SAAQ,KAAK;AAE/B,MAAI,kBAAkB;AACtB,MAAI,sBAAsB;AAE1B,kBAAgBA;AAChB,uBAAqB;AAErB,MAAI,cAAK;AACR,QAAI,wBAAwB;AAC5B,uCAAmCA,QAAO,kBAAkB;AAC5D,QAAI;AAAA;AAAA,MAAqC;AAAA;AAEzC,kBAAcA,QAAO,aAAa,SAAS;AAAA,EAC5C;AAEA,MAAI;AACH,SAAK,QAAQ,kBAAkB,GAAG;AACjC,oCAA8BA,OAAM;AAAA,IACrC,OAAO;AACN,8BAAwBA,OAAM;AAAA,IAC/B;AAEA,4BAAwBA,OAAM;AAC9B,QAAIC,YAAW,gBAAgBD,OAAM;AACrC,IAAAA,QAAO,WAAW,OAAOC,cAAa,aAAaA,YAAW;AAC9D,IAAAD,QAAO,KAAK;AAIZ,QAAI,gBAAO,sBAAsBA,QAAO,IAAI,WAAW,KAAKA,QAAO,SAAS,MAAM;AACjF,eAAS,OAAOA,QAAO,MAAM;AAC5B,YAAI,IAAI,mBAAmB;AAC1B,cAAI,KAAK,wBAAwB;AACjC,cAAI,oBAAoB;AAAA,QACzB;AAAA,MACD;AAAA,IACD;AAEA,QAAI,cAAK;AACR,uBAAiB,KAAKA,OAAM;AAAA,IAC7B;AAAA,EACD,UAAE;AACD,yBAAqB;AACrB,oBAAgB;AAEhB,QAAI,cAAK;AACR,yCAAmC,qBAAqB;AACxD,oBAAc,cAAc;AAAA,IAC7B;AAAA,EACD;AACD;AAEA,SAAS,mBAAmB;AAE3B,UAAQ;AAAA,IACP;AAAA,IACA,iBAAiB,MAAM,GAAG,EAAE,IAAI,CAAC,MAAM,EAAE,EAAE;AAAA,EAC5C;AACA,qBAAmB,CAAC;AACrB;AAEA,SAAS,sBAAsB;AAC9B,MAAI;AACH,IAAE,6BAA6B;AAAA,EAChC,SAAS,OAAO;AACf,QAAI,cAAK;AAER,sBAAgB,OAAO,SAAS;AAAA,QAC/B,OAAO;AAAA,MACR,CAAC;AAAA,IACF;AAGA,QAAI,0BAA0B,MAAM;AACnC,UAAI,cAAK;AACR,YAAI;AACH,gCAAsB,OAAO,qBAAqB;AAAA,QACnD,SAAS,GAAG;AAEX,2BAAiB;AACjB,gBAAM;AAAA,QACP;AAAA,MACD,OAAO;AACN,8BAAsB,OAAO,qBAAqB;AAAA,MACnD;AAAA,IACD,OAAO;AACN,UAAI,cAAK;AACR,yBAAiB;AAAA,MAClB;AACA,YAAM;AAAA,IACP;AAAA,EACD;AACD;AAEA,SAAS,4BAA4B;AACpC,MAAI,sBAAsB;AAE1B,MAAI;AACH,QAAI,cAAc;AAClB,yBAAqB;AAErB,WAAO,oBAAoB,SAAS,GAAG;AACtC,UAAI,gBAAgB,KAAM;AACzB,4BAAoB;AAAA,MACrB;AAEA,UAAI,eAAe;AACnB,UAAI,SAAS,aAAa;AAE1B,4BAAsB,CAAC;AAEvB,eAAS,IAAI,GAAG,IAAI,QAAQ,KAAK;AAChC,YAAI,oBAAoB,gBAAgB,aAAa,CAAC,CAAC;AACvD,6BAAqB,iBAAiB;AAAA,MACvC;AACA,iBAAW,MAAM;AAAA,IAClB;AAAA,EACD,UAAE;AACD,kBAAc;AACd,yBAAqB;AAErB,4BAAwB;AACxB,QAAI,cAAK;AACR,yBAAmB,CAAC;AAAA,IACrB;AAAA,EACD;AACD;AAMA,SAAS,qBAAqB,SAAS;AACtC,MAAI,SAAS,QAAQ;AACrB,MAAI,WAAW,EAAG;AAElB,WAAS,IAAI,GAAG,IAAI,QAAQ,KAAK;AAChC,QAAIA,UAAS,QAAQ,CAAC;AAEtB,SAAKA,QAAO,KAAK,YAAY,YAAY,GAAG;AAC3C,UAAI,gBAAgBA,OAAM,GAAG;AAC5B,YAAI,KAAK;AAET,sBAAcA,OAAM;AAOpB,YAAIA,QAAO,SAAS,QAAQA,QAAO,UAAU,QAAQA,QAAO,gBAAgB,MAAM;AACjF,cAAIA,QAAO,aAAa,MAAM;AAE7B,0BAAcA,OAAM;AAAA,UACrB,OAAO;AAEN,YAAAA,QAAO,KAAK;AAAA,UACb;AAAA,QACD;AAIA,YAAI,gBAAgB,OAAOA,QAAO,IAAI,iBAAiB,GAAG;AACzD;AAAA,QACD;AAAA,MACD;AAAA,IACD;AAAA,EACD;AAEA,SAAO,IAAI,QAAQ,KAAK,GAAG;AAC1B,oBAAgB,QAAQ,CAAC,CAAC;AAAA,EAC3B;AACD;AAMO,SAAS,gBAAgB,QAAQ;AACvC,MAAI,CAAC,aAAa;AACjB,kBAAc;AACd,mBAAe,yBAAyB;AAAA,EACzC;AAEA,MAAIA,UAAU,wBAAwB;AAEtC,SAAOA,QAAO,WAAW,MAAM;AAC9B,IAAAA,UAASA,QAAO;AAChB,QAAI,QAAQA,QAAO;AAEnB,SAAK,SAAS,cAAc,oBAAoB,GAAG;AAClD,WAAK,QAAQ,WAAW,EAAG;AAC3B,MAAAA,QAAO,KAAK;AAAA,IACb;AAAA,EACD;AAEA,sBAAoB,KAAKA,OAAM;AAChC;AAYA,SAAS,gBAAgB,MAAM;AAE9B,MAAI,UAAU,CAAC;AAGf,MAAIA,UAAS;AAEb,SAAOA,YAAW,MAAM;AACvB,QAAI,QAAQA,QAAO;AACnB,QAAI,aAAa,SAAS,gBAAgB,kBAAkB;AAC5D,QAAI,sBAAsB,cAAc,QAAQ,WAAW;AAE3D,QAAI,CAAC,wBAAwB,QAAQ,WAAW,GAAG;AAClD,WAAK,QAAQ,YAAY,GAAG;AAC3B,gBAAQ,KAAKA,OAAM;AAAA,MACpB,WAAW,WAAW;AACrB,QAAAA,QAAO,KAAK;AAAA,MACb,OAAO;AACN,YAAI,gBAAgBA,OAAM,GAAG;AAC5B,wBAAcA,OAAM;AAAA,QACrB;AAAA,MACD;AAGA,UAAIE,SAAQF,QAAO;AAEnB,UAAIE,WAAU,MAAM;AACnB,QAAAF,UAASE;AACT;AAAA,MACD;AAAA,IACD;AAEA,QAAI,SAASF,QAAO;AACpB,IAAAA,UAASA,QAAO;AAEhB,WAAOA,YAAW,QAAQ,WAAW,MAAM;AAC1C,MAAAA,UAAS,OAAO;AAChB,eAAS,OAAO;AAAA,IACjB;AAAA,EACD;AAEA,SAAO;AACR;AASO,SAAS,UAAU,IAAI;AAC7B,MAAI;AAEJ,MAAI,IAAI;AACP,kBAAc;AACd,8BAA0B;AAE1B,kBAAc;AACd,aAAS,GAAG;AAAA,EACb;AAEA,SAAO,MAAM;AACZ,gBAAY;AAEZ,QAAI,oBAAoB,WAAW,GAAG;AAGrC,oBAAc;AACd,8BAAwB;AACxB,UAAI,cAAK;AACR,2BAAmB,CAAC;AAAA,MACrB;AACA;AAAA;AAAA,QAAyB;AAAA;AAAA,IAC1B;AAEA,kBAAc;AACd,8BAA0B;AAAA,EAC3B;AACD;AAMA,eAAsB,OAAO;AAC5B,QAAM,QAAQ,QAAQ;AAGtB,YAAU;AACX;AAOO,SAAS,IAAI,QAAQ;AAC3B,MAAI,QAAQ,OAAO;AACnB,MAAI,cAAc,QAAQ,aAAa;AAEvC,MAAI,qBAAqB,MAAM;AAC9B,qBAAiB,IAAI,MAAM;AAAA,EAC5B;AAGA,MAAI,oBAAoB,QAAQ,CAAC,YAAY;AAC5C,SACC,qDAAkB,cAAa,mBAC/B,EAAC,qDAAkB,QAAQ,SAAS,UACnC;AACD,UAAI,OAAO,gBAAgB;AAC3B,UAAI,OAAO,KAAK,cAAc;AAC7B,eAAO,KAAK;AAIZ,YAAI,aAAa,QAAQ,SAAS,QAAQ,KAAK,YAAY,MAAM,QAAQ;AACxE;AAAA,QACD,WAAW,aAAa,MAAM;AAC7B,qBAAW,CAAC,MAAM;AAAA,QACnB,WAAW,CAAC,iBAAiB,CAAC,SAAS,SAAS,MAAM,GAAG;AAIxD,mBAAS,KAAK,MAAM;AAAA,QACrB;AAAA,MACD;AAAA,IACD;AAAA,EACD,WACC;AAAA,EACwB,OAAQ,SAAS;AAAA,EACjB,OAAQ,YAAY,MAC3C;AACD,QAAID;AAAA;AAAA,MAAkC;AAAA;AACtC,QAAI,SAASA,SAAQ;AAErB,QAAI,WAAW,SAAS,OAAO,IAAI,aAAa,GAAG;AAIlD,MAAAA,SAAQ,KAAK;AAAA,IACd;AAAA,EACD;AAEA,MAAI,YAAY;AACf,IAAAA;AAAA,IAAkC;AAElC,QAAI,gBAAgBA,QAAO,GAAG;AAC7B,qBAAeA,QAAO;AAAA,IACvB;AAAA,EACD;AAEA,MACC,gBACA,qBACA,CAAC,cACD,wBAAwB,QACxB,oBAAoB,QACpB,oBAAoB,aAAa,iBAChC;AAED,QAAI,OAAO,OAAO;AACjB,aAAO,MAAM;AAAA,IACd,OAAO;AACN,UAAII,SAAQ,UAAU,UAAU;AAEhC,UAAIA,QAAO;AACV,YAAI,QAAQ,oBAAoB,QAAQ,IAAI,MAAM;AAElD,YAAI,UAAU,QAAW;AACxB,kBAAQ,EAAE,QAAQ,CAAC,EAAE;AACrB,8BAAoB,QAAQ,IAAI,QAAQ,KAAK;AAAA,QAC9C;AAEA,YAAI,OAAO,MAAM,OAAO,MAAM,OAAO,SAAS,CAAC;AAI/C,YAAIA,OAAM,WAAU,6BAAM,QAAO;AAChC,gBAAM,OAAO,KAAKA,MAAK;AAAA,QACxB;AAAA,MACD;AAAA,IACD;AAAA,EACD;AAEA,MAAI,wBAAwB,WAAW,IAAI,MAAM,GAAG;AACnD,WAAO,WAAW,IAAI,MAAM;AAAA,EAC7B;AAEA,SAAO,OAAO;AACf;AAQO,SAAS,SAAS,QAAQ;AAChC,SAAO,UAAU,IAAI,MAAM;AAC5B;AAOA,SAAS,gBAAgB,IAAI;AAC5B,MAAI,4BAA4B;AAChC,qBAAmB,oBAAI,IAAI;AAE3B,MAAI,WAAW;AACf,MAAI;AAEJ,MAAI;AACH,YAAQ,EAAE;AACV,QAAI,8BAA8B,MAAM;AACvC,WAAK,UAAU,kBAAkB;AAChC,kCAA0B,IAAI,MAAM;AAAA,MACrC;AAAA,IACD;AAAA,EACD,UAAE;AACD,uBAAmB;AAAA,EACpB;AAEA,SAAO;AACR;AAOO,SAAS,yBAAyB,IAAI;AAC5C,MAAI,WAAW,gBAAgB,MAAM,QAAQ,EAAE,CAAC;AAEhD,WAAS,UAAU,UAAU;AAC5B,iBAAa,QAAQ,OAAO,CAAC;AAAA,EAC9B;AACD;AAkBO,SAAS,QAAQ,IAAI;AAC3B,MAAI,sBAAsB;AAC1B,MAAI;AACH,iBAAa;AACb,WAAO,GAAG;AAAA,EACX,UAAE;AACD,iBAAa;AAAA,EACd;AACD;AAEA,IAAM,cAAc,EAAE,QAAQ,cAAc;AAOrC,SAAS,kBAAkB,QAAQ,QAAQ;AACjD,SAAO,IAAK,OAAO,IAAI,cAAe;AACvC;AAOO,SAAS,oBAAoB,KAAK,MAAM;AAE9C,MAAI,SAAS,CAAC;AAEd,WAAS,OAAO,KAAK;AACpB,QAAI,CAAC,KAAK,SAAS,GAAG,GAAG;AACxB,aAAO,GAAG,IAAI,IAAI,GAAG;AAAA,IACtB;AAAA,EACD;AAEA,SAAO;AACR;AAQO,SAAS,gBAAgB,OAAO;AACtC,MAAI,OAAO,UAAU,YAAY,CAAC,SAAS,iBAAiB,aAAa;AACxE;AAAA,EACD;AAEA,MAAI,gBAAgB,OAAO;AAC1B,cAAU,KAAK;AAAA,EAChB,WAAW,CAAC,MAAM,QAAQ,KAAK,GAAG;AACjC,aAAS,OAAO,OAAO;AACtB,YAAM,OAAO,MAAM,GAAG;AACtB,UAAI,OAAO,SAAS,YAAY,QAAQ,gBAAgB,MAAM;AAC7D,kBAAU,IAAI;AAAA,MACf;AAAA,IACD;AAAA,EACD;AACD;AASO,SAAS,UAAU,OAAO,UAAU,oBAAI,IAAI,GAAG;AACrD,MACC,OAAO,UAAU,YACjB,UAAU;AAAA,EAEV,EAAE,iBAAiB,gBACnB,CAAC,QAAQ,IAAI,KAAK,GACjB;AACD,YAAQ,IAAI,KAAK;AAGjB,QAAI,iBAAiB,MAAM;AAC1B,YAAM,QAAQ;AAAA,IACf;AACA,aAAS,OAAO,OAAO;AACtB,UAAI;AACH,kBAAU,MAAM,GAAG,GAAG,OAAO;AAAA,MAC9B,SAAS,GAAG;AAAA,MAEZ;AAAA,IACD;AACA,UAAM,QAAQ,iBAAiB,KAAK;AACpC,QACC,UAAU,OAAO,aACjB,UAAU,MAAM,aAChB,UAAU,IAAI,aACd,UAAU,IAAI,aACd,UAAU,KAAK,WACd;AACD,YAAM,cAAc,gBAAgB,KAAK;AACzC,eAAS,OAAO,aAAa;AAC5B,cAAMC,OAAM,YAAY,GAAG,EAAE;AAC7B,YAAIA,MAAK;AACR,cAAI;AACH,YAAAA,KAAI,KAAK,KAAK;AAAA,UACf,SAAS,GAAG;AAAA,UAEZ;AAAA,QACD;AAAA,MACD;AAAA,IACD;AAAA,EACD;AACD;;;AC3+BA,IAAM,4BAA4B;AAO3B,SAAS,MAAM,OAAO;AAE5B,MAAI,OAAO,UAAU,YAAY,UAAU,QAAQ,gBAAgB,OAAO;AACzE,WAAO;AAAA,EACR;AAEA,QAAM,YAAY,iBAAiB,KAAK;AAExC,MAAI,cAAc,oBAAoB,cAAc,iBAAiB;AACpE,WAAO;AAAA,EACR;AAGA,MAAI,UAAU,oBAAI,IAAI;AACtB,MAAI,mBAAmB,SAAS,KAAK;AACrC,MAAI,UAAU,MAAO,CAAC;AAEtB,MAAIC,SAAQ,gBAAO,oBAAoB,UAAU,WAAW,IAAI;AAChE,MAAI,WAAW;AAOf,MAAI,cAAc,CAAC,OAAO;AACzB,QAAI,oBAAoB;AACxB,wBAAoB,QAAQ;AAG5B,QAAI,SAAS,GAAG;AAEhB,wBAAoB,iBAAiB;AACrC,WAAO;AAAA,EACR;AAEA,MAAI,kBAAkB;AAGrB,YAAQ,IAAI,UAAU;AAAA;AAAA,MAA6B,MAAO;AAAA,MAAQA;AAAA,IAAK,CAAC;AAAA,EACzE;AAGA,MAAI,OAAO;AAGX,WAAS,YAAY,UAAU;AAC9B,WAAO;AAEP,QAAI,SAAS,GAAG,IAAI,UAAU;AAG9B,eAAW,CAAC,MAAMC,OAAM,KAAK,SAAS;AACrC,UAAIA,SAAQ,UAAU,MAAM,IAAI,CAAC;AAAA,IAClC;AAAA,EACD;AAEA,SAAO,IAAI;AAAA;AAAA,IAA0B;AAAA,IAAQ;AAAA,MAC5C,eAAe,GAAG,MAAM,YAAY;AACnC,YACC,EAAE,WAAW,eACb,WAAW,iBAAiB,SAC5B,WAAW,eAAe,SAC1B,WAAW,aAAa,OACvB;AAKD,UAAE,wBAAwB;AAAA,QAC3B;AACA,YAAI,IAAI,QAAQ,IAAI,IAAI;AACxB,YAAI,MAAM,QAAW;AACpB,cAAI,YAAY,MAAM;AACrB,gBAAIC,KAAI,MAAO,WAAW,OAAOF,MAAK;AACtC,oBAAQ,IAAI,MAAME,EAAC;AACnB,gBAAI,gBAAO,OAAO,SAAS,UAAU;AACpC,kBAAIA,IAAG,UAAU,MAAM,IAAI,CAAC;AAAA,YAC7B;AACA,mBAAOA;AAAA,UACR,CAAC;AAAA,QACF,OAAO;AACN,cAAI,GAAG,WAAW,OAAO,IAAI;AAAA,QAC9B;AAEA,eAAO;AAAA,MACR;AAAA,MAEA,eAAe,QAAQ,MAAM;AAC5B,YAAI,IAAI,QAAQ,IAAI,IAAI;AAExB,YAAI,MAAM,QAAW;AACpB,cAAI,QAAQ,QAAQ;AACnB,kBAAMA,KAAI,YAAY,MAAM,MAAO,eAAeF,MAAK,CAAC;AACxD,oBAAQ,IAAI,MAAME,EAAC;AACnB,2BAAe,OAAO;AAEtB,gBAAI,cAAK;AACR,kBAAIA,IAAG,UAAU,MAAM,IAAI,CAAC;AAAA,YAC7B;AAAA,UACD;AAAA,QACD,OAAO;AAGN,cAAI,oBAAoB,OAAO,SAAS,UAAU;AACjD,gBAAI;AAAA;AAAA,cAAoC,QAAQ,IAAI,QAAQ;AAAA;AAC5D,gBAAI,IAAI,OAAO,IAAI;AAEnB,gBAAI,OAAO,UAAU,CAAC,KAAK,IAAI,GAAG,GAAG;AACpC,kBAAI,IAAI,CAAC;AAAA,YACV;AAAA,UACD;AACA,cAAI,GAAG,aAAa;AACpB,yBAAe,OAAO;AAAA,QACvB;AAEA,eAAO;AAAA,MACR;AAAA,MAEA,IAAI,QAAQ,MAAM,UAAU;AAhJ9B;AAiJG,YAAI,SAAS,cAAc;AAC1B,iBAAO;AAAA,QACR;AAEA,YAAI,gBAAO,SAAS,mBAAmB;AACtC,iBAAO;AAAA,QACR;AAEA,YAAI,IAAI,QAAQ,IAAI,IAAI;AACxB,YAAI,SAAS,QAAQ;AAGrB,YAAI,MAAM,WAAc,CAAC,YAAU,oBAAe,QAAQ,IAAI,MAA3B,mBAA8B,YAAW;AAC3E,cAAI,YAAY,MAAM;AACrB,gBAAI,IAAI,MAAM,SAAS,OAAO,IAAI,IAAI,aAAa;AACnD,gBAAIA,KAAI,MAAO,GAAGF,MAAK;AAEvB,gBAAI,cAAK;AACR,kBAAIE,IAAG,UAAU,MAAM,IAAI,CAAC;AAAA,YAC7B;AAEA,mBAAOA;AAAA,UACR,CAAC;AAED,kBAAQ,IAAI,MAAM,CAAC;AAAA,QACpB;AAEA,YAAI,MAAM,QAAW;AACpB,cAAI,IAAI,IAAI,CAAC;AACb,iBAAO,MAAM,gBAAgB,SAAY;AAAA,QAC1C;AAEA,eAAO,QAAQ,IAAI,QAAQ,MAAM,QAAQ;AAAA,MAC1C;AAAA,MAEA,yBAAyB,QAAQ,MAAM;AACtC,YAAI,aAAa,QAAQ,yBAAyB,QAAQ,IAAI;AAE9D,YAAI,cAAc,WAAW,YAAY;AACxC,cAAI,IAAI,QAAQ,IAAI,IAAI;AACxB,cAAI,EAAG,YAAW,QAAQ,IAAI,CAAC;AAAA,QAChC,WAAW,eAAe,QAAW;AACpC,cAAID,UAAS,QAAQ,IAAI,IAAI;AAC7B,cAAIE,SAAQF,WAAA,gBAAAA,QAAQ;AAEpB,cAAIA,YAAW,UAAaE,WAAU,eAAe;AACpD,mBAAO;AAAA,cACN,YAAY;AAAA,cACZ,cAAc;AAAA,cACd,OAAAA;AAAA,cACA,UAAU;AAAA,YACX;AAAA,UACD;AAAA,QACD;AAEA,eAAO;AAAA,MACR;AAAA,MAEA,IAAI,QAAQ,MAAM;AA3MpB;AA4MG,YAAI,SAAS,cAAc;AAC1B,iBAAO;AAAA,QACR;AAEA,YAAI,IAAI,QAAQ,IAAI,IAAI;AACxB,YAAI,MAAO,MAAM,UAAa,EAAE,MAAM,iBAAkB,QAAQ,IAAI,QAAQ,IAAI;AAEhF,YACC,MAAM,UACL,kBAAkB,SAAS,CAAC,SAAO,oBAAe,QAAQ,IAAI,MAA3B,mBAA8B,YACjE;AACD,cAAI,MAAM,QAAW;AACpB,gBAAI,YAAY,MAAM;AACrB,kBAAI,IAAI,MAAM,MAAM,OAAO,IAAI,CAAC,IAAI;AACpC,kBAAID,KAAI,MAAO,GAAGF,MAAK;AAEvB,kBAAI,cAAK;AACR,oBAAIE,IAAG,UAAU,MAAM,IAAI,CAAC;AAAA,cAC7B;AAEA,qBAAOA;AAAA,YACR,CAAC;AAED,oBAAQ,IAAI,MAAM,CAAC;AAAA,UACpB;AAEA,cAAIC,SAAQ,IAAI,CAAC;AACjB,cAAIA,WAAU,eAAe;AAC5B,mBAAO;AAAA,UACR;AAAA,QACD;AAEA,eAAO;AAAA,MACR;AAAA,MAEA,IAAI,QAAQ,MAAMA,QAAO,UAAU;AA/OrC;AAgPG,YAAI,IAAI,QAAQ,IAAI,IAAI;AACxB,YAAI,MAAM,QAAQ;AAGlB,YAAI,oBAAoB,SAAS,UAAU;AAC1C,mBAAS,IAAIA,QAAO;AAAA,UAAmC,EAAG,GAAG,KAAK,GAAG;AACpE,gBAAI,UAAU,QAAQ,IAAI,IAAI,EAAE;AAChC,gBAAI,YAAY,QAAW;AAC1B,kBAAI,SAAS,aAAa;AAAA,YAC3B,WAAW,KAAK,QAAQ;AAIvB,wBAAU,YAAY,MAAM,MAAO,eAAeH,MAAK,CAAC;AACxD,sBAAQ,IAAI,IAAI,IAAI,OAAO;AAE3B,kBAAI,cAAK;AACR,oBAAI,SAAS,UAAU,MAAM,CAAC,CAAC;AAAA,cAChC;AAAA,YACD;AAAA,UACD;AAAA,QACD;AAMA,YAAI,MAAM,QAAW;AACpB,cAAI,CAAC,SAAO,oBAAe,QAAQ,IAAI,MAA3B,mBAA8B,WAAU;AACnD,gBAAI,YAAY,MAAM,MAAO,QAAWA,MAAK,CAAC;AAC9C,gBAAI,GAAG,MAAMG,MAAK,CAAC;AAEnB,oBAAQ,IAAI,MAAM,CAAC;AAEnB,gBAAI,cAAK;AACR,kBAAI,GAAG,UAAU,MAAM,IAAI,CAAC;AAAA,YAC7B;AAAA,UACD;AAAA,QACD,OAAO;AACN,gBAAM,EAAE,MAAM;AAEd,cAAI,IAAI,YAAY,MAAM,MAAMA,MAAK,CAAC;AACtC,cAAI,GAAG,CAAC;AAAA,QACT;AAEA,YAAI,aAAa,QAAQ,yBAAyB,QAAQ,IAAI;AAG9D,YAAI,yCAAY,KAAK;AACpB,qBAAW,IAAI,KAAK,UAAUA,MAAK;AAAA,QACpC;AAEA,YAAI,CAAC,KAAK;AAKT,cAAI,oBAAoB,OAAO,SAAS,UAAU;AACjD,gBAAI;AAAA;AAAA,cAAoC,QAAQ,IAAI,QAAQ;AAAA;AAC5D,gBAAI,IAAI,OAAO,IAAI;AAEnB,gBAAI,OAAO,UAAU,CAAC,KAAK,KAAK,GAAG,GAAG;AACrC,kBAAI,IAAI,IAAI,CAAC;AAAA,YACd;AAAA,UACD;AAEA,yBAAe,OAAO;AAAA,QACvB;AAEA,eAAO;AAAA,MACR;AAAA,MAEA,QAAQ,QAAQ;AACf,YAAI,OAAO;AAEX,YAAI,WAAW,QAAQ,QAAQ,MAAM,EAAE,OAAO,CAACC,SAAQ;AACtD,cAAIH,UAAS,QAAQ,IAAIG,IAAG;AAC5B,iBAAOH,YAAW,UAAaA,QAAO,MAAM;AAAA,QAC7C,CAAC;AAED,iBAAS,CAAC,KAAKA,OAAM,KAAK,SAAS;AAClC,cAAIA,QAAO,MAAM,iBAAiB,EAAE,OAAO,SAAS;AACnD,qBAAS,KAAK,GAAG;AAAA,UAClB;AAAA,QACD;AAEA,eAAO;AAAA,MACR;AAAA,MAEA,iBAAiB;AAChB,QAAE,sBAAsB;AAAA,MACzB;AAAA,IACD;AAAA,EAAC;AACF;AAMA,SAAS,UAAU,MAAM,MAAM;AAC9B,MAAI,OAAO,SAAS,SAAU,QAAO,GAAG,IAAI,WAAW,KAAK,eAAe,EAAE;AAC7E,MAAI,0BAA0B,KAAK,IAAI,EAAG,QAAO,GAAG,IAAI,IAAI,IAAI;AAChE,SAAO,QAAQ,KAAK,IAAI,IAAI,GAAG,IAAI,IAAI,IAAI,MAAM,GAAG,IAAI,KAAK,IAAI;AAClE;AAMA,SAAS,eAAe,QAAQ,IAAI,GAAG;AACtC,MAAI,QAAQ,OAAO,IAAI,CAAC;AACzB;AAKO,SAAS,kBAAkB,OAAO;AACxC,MAAI;AACH,QAAI,UAAU,QAAQ,OAAO,UAAU,YAAY,gBAAgB,OAAO;AACzE,aAAO,MAAM,YAAY;AAAA,IAC1B;AAAA,EACD,QAAQ;AAAA,EAQR;AAEA,SAAO;AACR;AAMO,SAAS,GAAG,GAAG,GAAG;AACxB,SAAO,OAAO,GAAG,kBAAkB,CAAC,GAAG,kBAAkB,CAAC,CAAC;AAC5D;;;ACzXO,SAAS,gCAAgC;AAC/C,QAAMI,mBAAkB,MAAM;AAI9B,QAAM,UAAU,MAAM;AACtB,MAAI,SAAS;AACZ,YAAQ;AAAA,EACT;AAEA,QAAM,EAAE,SAAS,aAAa,SAAS,IAAIA;AAE3C,EAAAA,iBAAgB,UAAU,SAAU,MAAM,YAAY;AACrD,UAAM,QAAQ,QAAQ,KAAK,MAAM,MAAM,UAAU;AAEjD,QAAI,UAAU,IAAI;AACjB,eAAS,IAAI,cAAc,GAAG,IAAI,KAAK,QAAQ,KAAK,GAAG;AACtD,YAAI,kBAAkB,KAAK,CAAC,CAAC,MAAM,MAAM;AACxC,UAAE,8BAA8B,oBAAoB;AACpD;AAAA,QACD;AAAA,MACD;AAAA,IACD;AAEA,WAAO;AAAA,EACR;AAEA,EAAAA,iBAAgB,cAAc,SAAU,MAAM,YAAY;AAGzD,UAAM,QAAQ,YAAY,KAAK,MAAM,MAAM,cAAc,KAAK,SAAS,CAAC;AAExE,QAAI,UAAU,IAAI;AACjB,eAAS,IAAI,GAAG,MAAM,cAAc,KAAK,SAAS,IAAI,KAAK,GAAG;AAC7D,YAAI,kBAAkB,KAAK,CAAC,CAAC,MAAM,MAAM;AACxC,UAAE,8BAA8B,wBAAwB;AACxD;AAAA,QACD;AAAA,MACD;AAAA,IACD;AAEA,WAAO;AAAA,EACR;AAEA,EAAAA,iBAAgB,WAAW,SAAU,MAAM,YAAY;AACtD,UAAM,MAAM,SAAS,KAAK,MAAM,MAAM,UAAU;AAEhD,QAAI,CAAC,KAAK;AACT,eAAS,IAAI,GAAG,IAAI,KAAK,QAAQ,KAAK,GAAG;AACxC,YAAI,kBAAkB,KAAK,CAAC,CAAC,MAAM,MAAM;AACxC,UAAE,8BAA8B,qBAAqB;AACrD;AAAA,QACD;AAAA,MACD;AAAA,IACD;AAEA,WAAO;AAAA,EACR;AAGA,QAAM,mBAAmB,MAAM;AAC9B,IAAAA,iBAAgB,UAAU;AAC1B,IAAAA,iBAAgB,cAAc;AAC9B,IAAAA,iBAAgB,WAAW;AAAA,EAC5B;AACD;AAQO,SAAS,cAAc,GAAG,GAAG,QAAQ,MAAM;AAGjD,MAAI;AACH,QAAK,MAAM,OAAQ,kBAAkB,CAAC,MAAM,kBAAkB,CAAC,IAAI;AAClE,MAAE,8BAA8B,QAAQ,QAAQ,KAAK;AAAA,IACtD;AAAA,EACD,QAAQ;AAAA,EAAC;AAET,SAAQ,MAAM,MAAO;AACtB;AAQO,SAASC,QAAO,GAAG,GAAG,QAAQ,MAAM;AAC1C,MAAK,KAAK,OAAQ,kBAAkB,CAAC,KAAK,kBAAkB,CAAC,IAAI;AAChE,IAAE,8BAA8B,QAAQ,OAAO,IAAI;AAAA,EACpD;AAEA,SAAQ,KAAK,MAAO;AACrB;;;AC3FO,IAAI;AAGJ,IAAI;AAGJ,IAAI;AAGX,IAAI;AAEJ,IAAI;AAMG,SAAS,kBAAkB;AACjC,MAAI,YAAY,QAAW;AAC1B;AAAA,EACD;AAEA,YAAU;AACV,cAAY;AACZ,eAAa,UAAU,KAAK,UAAU,SAAS;AAE/C,MAAI,oBAAoB,QAAQ;AAChC,MAAI,iBAAiB,KAAK;AAC1B,MAAI,iBAAiB,KAAK;AAG1B,uBAAqB,eAAe,gBAAgB,YAAY,EAAE;AAElE,wBAAsB,eAAe,gBAAgB,aAAa,EAAE;AAEpE,MAAI,cAAc,iBAAiB,GAAG;AAGrC,sBAAkB,UAAU;AAE5B,sBAAkB,cAAc;AAEhC,sBAAkB,eAAe;AAEjC,sBAAkB,UAAU;AAE5B,sBAAkB,MAAM;AAAA,EACzB;AAEA,MAAI,cAAc,cAAc,GAAG;AAElC,mBAAe,MAAM;AAAA,EACtB;AAEA,MAAI,cAAK;AAER,sBAAkB,gBAAgB;AAElC,kCAA8B;AAAA,EAC/B;AACD;AAMO,SAAS,YAAY,QAAQ,IAAI;AACvC,SAAO,SAAS,eAAe,KAAK;AACrC;AAQO,SAAS,gBAAgB,MAAM;AACrC,SAAO,mBAAmB,KAAK,IAAI;AACpC;AAQO,SAAS,iBAAiB,MAAM;AACtC,SAAO,oBAAoB,KAAK,IAAI;AACrC;AASO,SAAS,MAAM,MAAM,SAAS;AACpC,MAAI,CAAC,WAAW;AACf,WAAO,gBAAgB,IAAI;AAAA,EAC5B;AAEA,MAAIC;AAAA;AAAA,IAAqC,gBAAgB,YAAY;AAAA;AAGrE,MAAIA,WAAU,MAAM;AACnB,IAAAA,SAAQ,aAAa,YAAY,YAAY,CAAC;AAAA,EAC/C,WAAW,WAAWA,OAAM,aAAa,WAAW;AACnD,QAAI,OAAO,YAAY;AACvB,IAAAA,UAAA,gBAAAA,OAAO,OAAO;AACd,qBAAiB,IAAI;AACrB,WAAO;AAAA,EACR;AAEA,mBAAiBA,MAAK;AACtB,SAAOA;AACR;AAQO,SAAS,YAAY,UAAU,SAAS;AArI/C;AAsIC,MAAI,CAAC,WAAW;AAEf,QAAI;AAAA;AAAA,MAAyC;AAAA;AAAA,QAAqC;AAAA,MAAS;AAAA;AAG3F,QAAI,iBAAiB,WAAW,MAAM,SAAS,GAAI,QAAO,iBAAiB,KAAK;AAEhF,WAAO;AAAA,EACR;AAIA,MAAI,aAAW,yCAAc,cAAa,WAAW;AACpD,QAAI,OAAO,YAAY;AAEvB,8CAAc,OAAO;AACrB,qBAAiB,IAAI;AACrB,WAAO;AAAA,EACR;AAEA,SAAO;AACR;AASO,SAAS,QAAQ,MAAM,QAAQ,GAAG,UAAU,OAAO;AACzD,MAAI,eAAe,YAAY,eAAe;AAC9C,MAAI;AAEJ,SAAO,SAAS;AACf,mBAAe;AACf;AAAA,IAA4C,iBAAiB,YAAY;AAAA,EAC1E;AAEA,MAAI,CAAC,WAAW;AACf,WAAO;AAAA,EACR;AAIA,MAAI,YAAW,6CAAc,cAAa,WAAW;AACpD,QAAI,OAAO,YAAY;AAIvB,QAAI,iBAAiB,MAAM;AAC1B,mDAAc,MAAM;AAAA,IACrB,OAAO;AACN,mBAAa,OAAO,IAAI;AAAA,IACzB;AACA,qBAAiB,IAAI;AACrB,WAAO;AAAA,EACR;AAEA,mBAAiB,YAAY;AAC7B;AAAA;AAAA,IAAoC;AAAA;AACrC;AAOO,SAAS,mBAAmB,MAAM;AACxC,OAAK,cAAc;AACpB;AASO,SAAS,eAAeC,MAAK,WAAWC,KAAI;AAClD,MAAI,UAAUA,MAAK,EAAE,IAAAA,IAAG,IAAI;AAC5B,MAAI,WAAW;AACd,WAAO,SAAS,gBAAgB,WAAWD,MAAK,OAAO;AAAA,EACxD;AACA,SAAO,SAAS,cAAcA,MAAK,OAAO;AAC3C;AAEO,SAAS,kBAAkB;AACjC,SAAO,SAAS,uBAAuB;AACxC;AAMO,SAAS,eAAe,OAAO,IAAI;AACzC,SAAO,SAAS,cAAc,IAAI;AACnC;AAQO,SAAS,cAAc,SAAS,KAAK,QAAQ,IAAI;AACvD,MAAI,IAAI,WAAW,QAAQ,GAAG;AAC7B,YAAQ,eAAe,gCAAgC,KAAK,KAAK;AACjE;AAAA,EACD;AACA,SAAO,QAAQ,aAAa,KAAK,KAAK;AACvC;;;ACrOO,IAAI,YAAY;AAGhB,SAAS,cAAc,OAAO;AACpC,cAAY;AACb;AASO,IAAI;AAGJ,SAAS,iBAAiB,MAAM;AACtC,MAAI,SAAS,MAAM;AAClB,IAAE,mBAAmB;AACrB,UAAM;AAAA,EACP;AAEA,SAAQ,eAAe;AACxB;AAEO,SAAS,eAAe;AAC9B,SAAO;AAAA;AAAA,IAA8C,iBAAiB,YAAY;AAAA,EAAE;AACrF;AAGO,SAAS,MAAM,MAAM;AAC3B,MAAI,CAAC,UAAW;AAGhB,MAAI,iBAAiB,YAAY,MAAM,MAAM;AAC5C,IAAE,mBAAmB;AACrB,UAAM;AAAA,EACP;AAEA,iBAAe;AAChB;AAKO,SAAS,iBAAiB,UAAU;AAC1C,MAAI,WAAW;AAEd,mBAAe,SAAS;AAAA,EACzB;AACD;AAEO,SAAS,KAAK,QAAQ,GAAG;AAC/B,MAAI,WAAW;AACd,QAAI,IAAI;AACR,QAAI,OAAO;AAEX,WAAO,KAAK;AACX;AAAA,MAAoC,iBAAiB,IAAI;AAAA,IAC1D;AAEA,mBAAe;AAAA,EAChB;AACD;AAKO,SAAS,eAAe;AAC9B,MAAI,QAAQ;AACZ,MAAI,OAAO;AAEX,SAAO,MAAM;AACZ,QAAI,KAAK,aAAa,cAAc;AACnC,UAAI;AAAA;AAAA,QAA+B,KAAM;AAAA;AAEzC,UAAI,SAAS,eAAe;AAC3B,YAAI,UAAU,EAAG,QAAO;AACxB,iBAAS;AAAA,MACV,WAAW,SAAS,mBAAmB,SAAS,sBAAsB;AACrE,iBAAS;AAAA,MACV;AAAA,IACD;AAEA,QAAIE;AAAA;AAAA,MAAoC,iBAAiB,IAAI;AAAA;AAC7D,SAAK,OAAO;AACZ,WAAOA;AAAA,EACR;AACD;AAMO,SAAS,2BAA2B,MAAM;AAChD,MAAI,CAAC,QAAQ,KAAK,aAAa,cAAc;AAC5C,IAAE,mBAAmB;AACrB,UAAM;AAAA,EACP;AAEA;AAAA;AAAA,IAA+B,KAAM;AAAA;AACtC;;;AC7GO,SAAS,UAAU,KAAK,OAAO;AACrC,MAAI,OAAO;AACV,UAAM,OAAO,SAAS;AACtB,QAAI,YAAY;AAEhB,qBAAiB,MAAM;AACtB,UAAI,SAAS,kBAAkB,MAAM;AACpC,YAAI,MAAM;AAAA,MACX;AAAA,IACD,CAAC;AAAA,EACF;AACD;AAQO,SAAS,sBAAsB,KAAK;AAC1C,MAAI,aAAa,gBAAgB,GAAG,MAAM,MAAM;AAC/C,uBAAmB,GAAG;AAAA,EACvB;AACD;AAEA,IAAI,0BAA0B;AAEvB,SAAS,0BAA0B;AACzC,MAAI,CAAC,yBAAyB;AAC7B,8BAA0B;AAC1B,aAAS;AAAA,MACR;AAAA,MACA,CAAC,QAAQ;AAGR,gBAAQ,QAAQ,EAAE,KAAK,MAAM;AA5CjC;AA6CK,cAAI,CAAC,IAAI,kBAAkB;AAC1B;AAAA,oBAAW;AAAA;AAAA,cAAoC,IAAI,OAAQ;AAAA,cAAU;AAEpE,sBAAE,WAAF;AAAA,YACD;AAAA,UACD;AAAA,QACD,CAAC;AAAA,MACF;AAAA;AAAA,MAEA,EAAE,SAAS,KAAK;AAAA,IACjB;AAAA,EACD;AACD;;;ACxCO,SAAS,OAAO,QAAQ,QAAQ,SAAS,2BAA2B,MAAM;AAChF,MAAI,0BAA0B;AAC7B,YAAQ;AAAA,EACT;AAEA,WAAS,QAAQ,QAAQ;AACxB,WAAO,iBAAiB,MAAM,OAAO;AAAA,EACtC;AAEA,WAAS,MAAM;AACd,aAASC,SAAQ,QAAQ;AACxB,aAAO,oBAAoBA,OAAM,OAAO;AAAA,IACzC;AAAA,EACD,CAAC;AACF;AAMO,SAAS,yBAAyB,IAAI;AAC5C,MAAI,oBAAoB;AACxB,MAAI,kBAAkB;AACtB,sBAAoB,IAAI;AACxB,oBAAkB,IAAI;AACtB,MAAI;AACH,WAAO,GAAG;AAAA,EACX,UAAE;AACD,wBAAoB,iBAAiB;AACrC,sBAAkB,eAAe;AAAA,EAClC;AACD;AAUO,SAAS,gCAAgC,SAASC,QAAO,SAAS,WAAW,SAAS;AAC5F,UAAQ,iBAAiBA,QAAO,MAAM,yBAAyB,OAAO,CAAC;AAEvE,QAAM,OAAO,QAAQ;AACrB,MAAI,MAAM;AAGT,YAAQ,SAAS,MAAM;AACtB,WAAK;AACL,eAAS,IAAI;AAAA,IACd;AAAA,EACD,OAAO;AAEN,YAAQ,SAAS,MAAM,SAAS,IAAI;AAAA,EACrC;AAEA,0BAAwB;AACzB;;;AC5DO,IAAM,wBAAwB,oBAAI,IAAI;AAGtC,IAAM,qBAAqB,oBAAI,IAAI;AAOnC,SAAS,cAAc,KAAK;AAClC,MAAI,CAAC,UAAW;AAEhB,MAAI,gBAAgB,QAAQ;AAC5B,MAAI,gBAAgB,SAAS;AAE7B,QAAMC,SAAQ,IAAI;AAClB,MAAIA,WAAU,QAAW;AAExB,QAAI,MAAM;AACV,mBAAe,MAAM;AACpB,UAAI,IAAI,aAAa;AACpB,YAAI,cAAcA,MAAK;AAAA,MACxB;AAAA,IACD,CAAC;AAAA,EACF;AACD;AAQO,SAAS,aAAa,YAAY,KAAK,SAAS,UAAU,CAAC,GAAG;AAIpE,WAAS,eAAoCA,QAAO;AACnD,QAAI,CAAC,QAAQ,SAAS;AAErB,+BAAyB,KAAK,KAAKA,MAAK;AAAA,IACzC;AACA,QAAI,CAACA,OAAM,cAAc;AACxB,aAAO,yBAAyB,MAAM;AACrC,eAAO,mCAAS,KAAK,MAAMA;AAAA,MAC5B,CAAC;AAAA,IACF;AAAA,EACD;AAMA,MACC,WAAW,WAAW,SAAS,KAC/B,WAAW,WAAW,OAAO,KAC7B,eAAe,SACd;AACD,qBAAiB,MAAM;AACtB,UAAI,iBAAiB,YAAY,gBAAgB,OAAO;AAAA,IACzD,CAAC;AAAA,EACF,OAAO;AACN,QAAI,iBAAiB,YAAY,gBAAgB,OAAO;AAAA,EACzD;AAEA,SAAO;AACR;AAYO,SAAS,GAAG,SAAS,MAAM,SAAS,UAAU,CAAC,GAAG;AACxD,MAAI,iBAAiB,aAAa,MAAM,SAAS,SAAS,OAAO;AAEjE,SAAO,MAAM;AACZ,YAAQ,oBAAoB,MAAM,gBAAgB,OAAO;AAAA,EAC1D;AACD;AAUO,SAAS,MAAM,YAAY,KAAK,SAAS,SAAS,SAAS;AACjE,MAAI,UAAU,EAAE,SAAS,QAAQ;AACjC,MAAI,iBAAiB,aAAa,YAAY,KAAK,SAAS,OAAO;AAEnE,MACC,QAAQ,SAAS;AAAA,EAEjB,QAAQ;AAAA,EAER,QAAQ;AAAA,EAER,eAAe,kBACd;AACD,aAAS,MAAM;AACd,UAAI,oBAAoB,YAAY,gBAAgB,OAAO;AAAA,IAC5D,CAAC;AAAA,EACF;AACD;AAMO,SAAS,SAAS,QAAQ;AAChC,WAAS,IAAI,GAAG,IAAI,OAAO,QAAQ,KAAK;AACvC,0BAAsB,IAAI,OAAO,CAAC,CAAC;AAAA,EACpC;AAEA,WAAS,MAAM,oBAAoB;AAClC,OAAG,MAAM;AAAA,EACV;AACD;AAOO,SAAS,yBAAyBA,QAAO;AApJhD;AAqJC,MAAI,kBAAkB;AACtB,MAAI;AAAA;AAAA,IAAsC,gBAAiB;AAAA;AAC3D,MAAI,aAAaA,OAAM;AACvB,MAAI,SAAO,KAAAA,OAAM,iBAAN,wBAAAA,YAA0B,CAAC;AACtC,MAAI;AAAA;AAAA,IAAgD,KAAK,CAAC,KAAKA,OAAM;AAAA;AAMrE,MAAI,WAAW;AAGf,MAAI,aAAaA,OAAM;AAEvB,MAAI,YAAY;AACf,QAAI,SAAS,KAAK,QAAQ,UAAU;AACpC,QACC,WAAW,OACV,oBAAoB,YAAY;AAAA,IAAwC,SACxE;AAKD,MAAAA,OAAM,SAAS;AACf;AAAA,IACD;AAOA,QAAI,cAAc,KAAK,QAAQ,eAAe;AAC9C,QAAI,gBAAgB,IAAI;AAGvB;AAAA,IACD;AAEA,QAAI,UAAU,aAAa;AAC1B,iBAAW;AAAA,IACZ;AAAA,EACD;AAEA;AAAA,EAAyC,KAAK,QAAQ,KAAKA,OAAM;AAIjE,MAAI,mBAAmB,gBAAiB;AAGxC,kBAAgBA,QAAO,iBAAiB;AAAA,IACvC,cAAc;AAAA,IACd,MAAM;AACL,aAAO,kBAAkB;AAAA,IAC1B;AAAA,EACD,CAAC;AAOD,MAAI,oBAAoB;AACxB,MAAI,kBAAkB;AACtB,sBAAoB,IAAI;AACxB,oBAAkB,IAAI;AAEtB,MAAI;AAIH,QAAI;AAIJ,QAAI,eAAe,CAAC;AAEpB,WAAO,mBAAmB,MAAM;AAE/B,UAAI,iBACH,eAAe,gBACf,eAAe;AAAA,MACK,eAAgB,QACpC;AAED,UAAI;AAEH,YAAI,YAAY,eAAe,OAAO,UAAU;AAEhD,YACC,aAAa,SACZ;AAAA,QAAsB,eAAgB;AAAA;AAAA,QAGtCA,OAAM,WAAW,iBACjB;AACD,cAAI,SAAS,SAAS,GAAG;AACxB,gBAAI,CAAC,IAAI,GAAG,IAAI,IAAI;AACpB,eAAG,MAAM,gBAAgB,CAACA,QAAO,GAAG,IAAI,CAAC;AAAA,UAC1C,OAAO;AACN,sBAAU,KAAK,gBAAgBA,MAAK;AAAA,UACrC;AAAA,QACD;AAAA,MACD,SAAS,OAAO;AACf,YAAI,aAAa;AAChB,uBAAa,KAAK,KAAK;AAAA,QACxB,OAAO;AACN,wBAAc;AAAA,QACf;AAAA,MACD;AACA,UAAIA,OAAM,gBAAgB,mBAAmB,mBAAmB,mBAAmB,MAAM;AACxF;AAAA,MACD;AACA,uBAAiB;AAAA,IAClB;AAEA,QAAI,aAAa;AAChB,eAAS,SAAS,cAAc;AAE/B,uBAAe,MAAM;AACpB,gBAAM;AAAA,QACP,CAAC;AAAA,MACF;AACA,YAAM;AAAA,IACP;AAAA,EACD,UAAE;AAED,IAAAA,OAAM,SAAS;AAEf,WAAOA,OAAM;AACb,wBAAoB,iBAAiB;AACrC,sBAAkB,eAAe;AAAA,EAClC;AACD;AAYO,SAAS,MACf,OACA,SACA,MACA,WACA,KACA,mBAAmB,OACnB,gBAAgB,OACf;AAjTF;AAkTC,MAAI;AACJ,MAAI;AAEJ,MAAI;AACH,cAAU,MAAM;AAAA,EACjB,SAAS,GAAG;AACX,YAAQ;AAAA,EACT;AAEA,MAAI,OAAO,YAAY,eAAe,oBAAoB,WAAW,QAAQ,QAAQ;AACpF,UAAM,WAAW,uCAAY;AAC7B,UAAM,WAAW,MAAM,OAAO,QAAQ,IAAI,IAAI,CAAC,CAAC,IAAI,IAAI,CAAC,CAAC,KAAK,OAAO,QAAQ;AAC9E,UAAM,UAAQ,UAAK,CAAC,MAAN,mBAAS,cAAa,MAAM,iBAAiB,YAAY;AACvE,UAAM,eAAa,UAAK,CAAC,MAAN,mBAAS,QAAO;AACnC,UAAM,cAAc,KAAK,UAAU,aAAa,QAAQ;AACxD,UAAM,aAAa,gBAAgB,6BAA6B;AAEhE,IAAE,sBAAsB,aAAa,UAAU;AAE/C,QAAI,OAAO;AACV,YAAM;AAAA,IACP;AAAA,EACD;AACA,qCAAS,MAAM,SAAS;AACzB;", "names": ["fallback", "tag", "trace", "label", "stack", "source", "stack", "component_context", "derived", "stack", "source", "effect", "effect", "push", "derived", "deriveds", "teardown", "next", "child", "sibling", "effect", "effect", "derived", "effect", "teardown", "child", "trace", "get", "stack", "source", "s", "value", "key", "array_prototype", "equals", "child", "tag", "is", "next", "name", "event", "event"]}
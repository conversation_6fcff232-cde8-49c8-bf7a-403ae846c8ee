// @ts-nocheck
import type { PageLoad } from "./$types";
import type { PostEntity } from "./[id]/types";

import { handleUnauthorized } from "$lib";

export const load = async ({ fetch }: Parameters<PageLoad>[0]) => {
  const response = await fetch("/api/reactor/post?page=1&size=20");

  handleUnauthorized(response);

  if (!response.ok) {
    return {
      posts: [],
      isHasMorePosts: false,
    };
  }

  const data = await response.json() as {
    items: PostEntity[];
    total: number;
  };

  // Convert date strings to Date objects
  const posts = data.items.map((post) => ({
    ...post,
    createdAt: new Date(post.createdAt),
    updatedAt: new Date(post.updatedAt),
  }));

  return {
    posts,
    isHasMorePosts: data.items.length === 20, // If we got a full page, there might be more
  };
};

// locale
enum Locale {
    @@map("locale")

    en
    ru
}

// localization
model Localization {
    @@map("localizations")

    id String @id @db.Uuid @default(uuid(7))

    userName        User[] @relation("user_name")
    userDescription User[] @relation("user_description")

    communeName        Commune[] @relation("commune_name")
    communeDescription Commune[] @relation("commune_description")

    votingTitle             Voting[]       @relation("voting_title")
    votingDescription       Voting[]       @relation("voting_description")
    votingOptionTitle       VotingOption[] @relation("voting_option_title")
    votingOptionDescription VotingOption[] @relation("voting_option_description")

    postTitle       Post[] @relation("post_title")
    postDescription Post[] @relation("post_description")

    reactorPostTitle ReactorPost[] @relation("reactor_post_title")
    reactorPostBody  ReactorPost[] @relation("reactor_post_body")

    reactorCommentBody ReactorComment[] @relation("reactor_comment_body")

    reactorHubName        ReactorHub[] @relation("reactor_hub_name")
    reactorHubDescription ReactorHub[] @relation("reactor_hub_description")

    reactorGroupName        ReactorGroup[] @relation("reactor_group_name")
    reactorGroupDescription ReactorGroup[] @relation("reactor_group_description")

    key String

    locale Locale
    value String

    createdAt DateTime  @map("created_at") @db.Timestamptz(3) @default(now())
    updatedAt DateTime  @map("updated_at") @db.Timestamptz(3) @updatedAt
    deletedAt DateTime? @map("deleted_at") @db.Timestamptz(3)
}

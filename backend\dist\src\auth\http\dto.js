"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
Object.defineProperty(exports, "__esModule", { value: true });
exports.Logout = exports.Login = exports.Register = exports.Me = exports.Otp = void 0;
const prisma = __importStar(require("@prisma/client"));
const zod_1 = require("../../zod");
const otp = zod_1.z.string().nonempty().length(6);
exports.Otp = zod_1.z.object({
    email: zod_1.z.string().email(),
});
exports.Me = zod_1.z.object({
    id: zod_1.ZodHelper.Uuid,
    email: zod_1.z.string().email(),
    role: zod_1.z.nativeEnum(prisma.UserRole),
    name: zod_1.ZodHelper.Localizations,
    description: zod_1.ZodHelper.Localizations,
    images: zod_1.z.array(zod_1.ZodHelper.Image).optional(),
    joinedAt: zod_1.z.date(),
});
exports.Register = zod_1.z.object({
    referrerId: zod_1.z.string().uuid().nullable().optional(),
    email: zod_1.z.string().email(),
    otp,
});
exports.Login = zod_1.z.object({
    email: zod_1.z.string().email(),
    otp,
});
exports.Logout = zod_1.z.object({
    refreshToken: zod_1.z.string().nonempty(),
});
//# sourceMappingURL=dto.js.map
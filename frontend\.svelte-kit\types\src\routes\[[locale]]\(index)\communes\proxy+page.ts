// @ts-nocheck
import type { PageLoad } from "./$types";
import type { Localization } from "$lib";

import { handleUnauthorized } from "$lib";

export type CommuneMemberType = "user" | "commune";

export interface Commune {
  id: string;
  name: Localization[];
  description: Localization[];
  memberCount: number;
  headMember: {
    actorType: CommuneMemberType;
    actorId: string;
    name: Localization[];
  };
  images?: {
    id: string;
    url: string;
    source: string;
  }[];
  createdAt: string;
  updatedAt: string;
}

export const load = async ({ fetch, url }: Parameters<PageLoad>[0]) => {
  const response = await fetch("/api/commune");

  handleUnauthorized(response);

  const data: Commune[] = response.ok ? await response.json() : [];

  return {
    communes: data,
    isHasMoreCommunes: data.length === 20, // If we got a full page, there might be more
  };
};

<script lang="ts">
  import type { Locale } from "$lib";

  interface Props {
    locale: Locale;
    toLocaleHref: (href: string) => string;
    isExpanded?: boolean;
  }

  const i18n = {
    en: {
      sections: "Sections",
      answers: "Answers",
      myComments: "My Comments",
      ratings: "Ratings",
      saved: "Saved",
      viewed: "Viewed",
      settings: "Settings",
      toggleSections: "Toggle sections",
    },
    ru: {
      sections: "Разделы",
      answers: "Ответы",
      myComments: "Мои комментарии",
      ratings: "Оценки",
      saved: "Сохраненное",
      viewed: "Просмотренное",
      settings: "Настройки",
      toggleSections: "Переключить разделы",
    },
  };

  let { isExpanded = $bindable(false), ...props }: Props = $props();
  const { locale, toLocaleHref } = props;

  const t = $derived(i18n[locale]);

  function toggleIsExpanded() {
    isExpanded = !isExpanded;
  }

  const ENABLED: boolean = false;
</script>

{#if ENABLED}
  <div
    class="right-menu {!isExpanded ? 'collapsed' : ''}"
    role="navigation"
    aria-label={t.sections}
  >
    <div class="right-menu-header d-flex justify-content-between align-items-center p-3 bg-light">
      <h6 class="mb-0">{t.sections}</h6>
      <button
        class="btn btn-sm btn-link p-0"
        onclick={toggleIsExpanded}
        aria-label={t.toggleSections}
      >
        <i class="bi bi-chevron-{isExpanded ? 'up' : 'down'}"></i>
      </button>
    </div>

    {#if isExpanded}
      <div class="right-menu-content p-3">
        <ul class="list-group list-group-flush">
          <li class="list-group-item border-0 px-0 ps-1">
            <a href={toLocaleHref("/reactor/answers")} class="text-decoration-none text-body">
              <i class="bi bi-chat-dots me-2"></i>
              {t.answers}
            </a>
          </li>
          <li class="list-group-item border-0 px-0 ps-1">
            <a href={toLocaleHref("/reactor/my-comments")} class="text-decoration-none text-body">
              <i class="bi bi-chat-left-text me-2"></i>
              {t.myComments}
            </a>
          </li>
          <li class="list-group-item border-0 px-0 ps-1">
            <a href={toLocaleHref("/reactor/rates")} class="text-decoration-none text-body">
              <i class="bi bi-star me-2"></i>
              {t.ratings}
            </a>
          </li>
          <li class="list-group-item border-0 px-0 ps-1">
            <a href={toLocaleHref("/reactor/saved")} class="text-decoration-none text-body">
              <i class="bi bi-bookmark me-2"></i>
              {t.saved}
            </a>
          </li>
          <li class="list-group-item border-0 px-0 ps-1">
            <a href={toLocaleHref("/reactor/seen")} class="text-decoration-none text-body">
              <i class="bi bi-eye me-2"></i>
              {t.viewed}
            </a>
          </li>
          <li class="list-group-item border-0 px-0 ps-1">
            <a href={toLocaleHref("/reactor/settings")} class="text-decoration-none text-body">
              <i class="bi bi-gear me-2"></i>
              {t.settings}
            </a>
          </li>
        </ul>
      </div>
    {/if}
  </div>
{/if}

<style lang="scss">
  .right-menu {
    background-color: #f8f9fa;
    border-radius: 0.5rem;
    overflow: hidden;
    transition: opacity 0.3s ease;

    &.collapsed {
      opacity: 0.5;

      &:hover {
        opacity: 1;
      }
    }
  }

  .right-menu-header {
    border-bottom: 1px solid #dee2e6;
  }

  .list-group-item {
    background-color: transparent;
    padding-top: 0.5rem;
    padding-bottom: 0.5rem;
    transition: background-color 0.2s ease;

    &:hover {
      background-color: rgba(0, 0, 0, 0.05);
    }

    a {
      display: block;
    }
  }
</style>

class Parser {
    constructor(tokens) {
        this.pos = 0;
        this.tokens = tokens;
    }
    peek() {
        return this.tokens[this.pos];
    }
    next() {
        return this.tokens[this.pos++];
    }
    eat(token) {
        if (this.peek()?.image !== token) {
            throw new Error(`Expected ${token}, got ${this.peek()?.image} at ${this.pos}`);
        }
        this.pos++;
    }
    parse() {
        return this.parseAnd();
    }
    parseAnd() {
        let left = this.parseOr();
        const statements = [left];
        while (this.peek()?.image === "&&") {
            this.next();
            statements.push(this.parseOr());
        }
        return statements.length > 1 ? { type: "and", statements } : left;
    }
    parseOr() {
        let left = this.parseTerm();
        const statements = [left];
        while (this.peek()?.image === "||") {
            this.next();
            statements.push(this.parseTerm());
        }
        return statements.length > 1 ? { type: "or", statements } : left;
    }
    parseTerm() {
        if (this.peek()?.image === "(") {
            this.next();
            const expr = this.parseAnd();
            this.eat(")");
            return expr;
        }
        return this.parseComparison();
    }
    parseComparison() {
        const field = this.next();
        if (!field) {
            throw new Error(`Unexpected end of input at position ${this.pos}`);
        }
        const op = this.next();
        if (!op) {
            throw new Error(`Unexpected end of input at position ${this.pos}`);
        }
        const value = this.parseValue();
        return {
            type: "comparison",
            identifier: field.image,
            operator: op.image,
            value,
        };
    }
    parseValue() {
        if (this.peek()?.image === "[") {
            this.next();
            const arr = [];
            while (this.peek()?.image !== "]") {
                let lit = this.next();
                if (!lit) {
                    throw new Error(`Unexpected end of input at position ${this.pos}`);
                }
                let literal = lit.image;
                if (literal.startsWith('"') && literal.endsWith('"')) {
                    literal = literal.slice(1, -1).replaceAll('""', '"');
                }
                arr.push(literal);
                if (this.peek()?.image === ",") {
                    this.next();
                }
            }
            this.eat("]");
            return arr;
        }
        else {
            let lit = this.next();
            if (!lit) {
                throw new Error(`Unexpected end of input at position ${this.pos}`);
            }
            let literal = lit.image;
            if (literal.startsWith('"') && literal.endsWith('"')) {
                return literal.slice(1, -1).replaceAll('""', '"');
            }
            return literal;
        }
    }
}
export function createAst(tokens) {
    return new Parser(tokens).parse();
}
//# sourceMappingURL=create-ast.mjs.map
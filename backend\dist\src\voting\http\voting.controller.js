"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.VotingController = void 0;
const common_1 = require("@nestjs/common");
const voting_service_1 = require("../voting.service");
const Dto = __importStar(require("./dto"));
const zod_1 = require("../../zod");
let VotingController = class VotingController {
    constructor(votingService) {
        this.votingService = votingService;
    }
    async getVotings(page, size) {
        const votings = await this.votingService.getMany({
            deletedAt: null,
        }, { page, size });
        return zod_1.ZodHelper.parseInput(Dto.Votings, votings);
    }
    async createVoting(body) {
        const voting = await this.votingService.create(body);
        return zod_1.ZodHelper.parseInput(Dto.Voting, voting);
    }
    async getVoting(id) {
        const voting = await this.votingService.getOneOrThrow(id);
        return zod_1.ZodHelper.parseInput(Dto.Voting, voting);
    }
    async deleteVoting(id) {
        await this.votingService.deleteOne(id);
    }
};
exports.VotingController = VotingController;
__decorate([
    (0, common_1.Get)(),
    __param(0, (0, common_1.Query)("page", new zod_1.ZodPipe(zod_1.ZodHelper.pagination.page))),
    __param(1, (0, common_1.Query)("size", new zod_1.ZodPipe(zod_1.ZodHelper.pagination.size))),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Number, Number]),
    __metadata("design:returntype", Promise)
], VotingController.prototype, "getVotings", null);
__decorate([
    (0, common_1.Post)(),
    __param(0, (0, common_1.Body)(new zod_1.ZodPipe(Dto.CreateVoting))),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object]),
    __metadata("design:returntype", Promise)
], VotingController.prototype, "createVoting", null);
__decorate([
    (0, common_1.Get)(":id"),
    __param(0, (0, common_1.Param)("id", new zod_1.ZodPipe(zod_1.ZodHelper.Uuid))),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", Promise)
], VotingController.prototype, "getVoting", null);
__decorate([
    (0, common_1.Delete)(":id"),
    __param(0, (0, common_1.Param)("id", new zod_1.ZodPipe(zod_1.ZodHelper.Uuid))),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", Promise)
], VotingController.prototype, "deleteVoting", null);
exports.VotingController = VotingController = __decorate([
    (0, common_1.Controller)("voting"),
    __metadata("design:paramtypes", [voting_service_1.VotingService])
], VotingController);
//# sourceMappingURL=voting.controller.js.map
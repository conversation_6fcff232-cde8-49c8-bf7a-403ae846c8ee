{"version": 3, "sources": ["../../../../node_modules/esm-env/true.js", "../../../../node_modules/svelte/src/internal/client/warnings.js"], "sourcesContent": ["export default true;\n", "/* This file is generated by scripts/process-messages/index.js. Do not edit! */\n\nimport { DEV } from 'esm-env';\n\nvar bold = 'font-weight: bold';\nvar normal = 'font-weight: normal';\n\n/**\n * Assignment to `%property%` property (%location%) will evaluate to the right-hand side, not the value of `%property%` following the assignment. This may result in unexpected behaviour.\n * @param {string} property\n * @param {string} location\n */\nexport function assignment_value_stale(property, location) {\n\tif (DEV) {\n\t\tconsole.warn(`%c[svelte] assignment_value_stale\\n%cAssignment to \\`${property}\\` property (${location}) will evaluate to the right-hand side, not the value of \\`${property}\\` following the assignment. This may result in unexpected behaviour.\\nhttps://svelte.dev/e/assignment_value_stale`, bold, normal);\n\t} else {\n\t\tconsole.warn(`https://svelte.dev/e/assignment_value_stale`);\n\t}\n}\n\n/**\n * `%binding%` (%location%) is binding to a non-reactive property\n * @param {string} binding\n * @param {string | undefined | null} [location]\n */\nexport function binding_property_non_reactive(binding, location) {\n\tif (DEV) {\n\t\tconsole.warn(\n\t\t\t`%c[svelte] binding_property_non_reactive\\n%c${location\n\t\t\t\t? `\\`${binding}\\` (${location}) is binding to a non-reactive property`\n\t\t\t\t: `\\`${binding}\\` is binding to a non-reactive property`}\\nhttps://svelte.dev/e/binding_property_non_reactive`,\n\t\t\tbold,\n\t\t\tnormal\n\t\t);\n\t} else {\n\t\tconsole.warn(`https://svelte.dev/e/binding_property_non_reactive`);\n\t}\n}\n\n/**\n * Your `console.%method%` contained `$state` proxies. Consider using `$inspect(...)` or `$state.snapshot(...)` instead\n * @param {string} method\n */\nexport function console_log_state(method) {\n\tif (DEV) {\n\t\tconsole.warn(`%c[svelte] console_log_state\\n%cYour \\`console.${method}\\` contained \\`$state\\` proxies. Consider using \\`$inspect(...)\\` or \\`$state.snapshot(...)\\` instead\\nhttps://svelte.dev/e/console_log_state`, bold, normal);\n\t} else {\n\t\tconsole.warn(`https://svelte.dev/e/console_log_state`);\n\t}\n}\n\n/**\n * %handler% should be a function. Did you mean to %suggestion%?\n * @param {string} handler\n * @param {string} suggestion\n */\nexport function event_handler_invalid(handler, suggestion) {\n\tif (DEV) {\n\t\tconsole.warn(`%c[svelte] event_handler_invalid\\n%c${handler} should be a function. Did you mean to ${suggestion}?\\nhttps://svelte.dev/e/event_handler_invalid`, bold, normal);\n\t} else {\n\t\tconsole.warn(`https://svelte.dev/e/event_handler_invalid`);\n\t}\n}\n\n/**\n * The `%attribute%` attribute on `%html%` changed its value between server and client renders. The client value, `%value%`, will be ignored in favour of the server value\n * @param {string} attribute\n * @param {string} html\n * @param {string} value\n */\nexport function hydration_attribute_changed(attribute, html, value) {\n\tif (DEV) {\n\t\tconsole.warn(`%c[svelte] hydration_attribute_changed\\n%cThe \\`${attribute}\\` attribute on \\`${html}\\` changed its value between server and client renders. The client value, \\`${value}\\`, will be ignored in favour of the server value\\nhttps://svelte.dev/e/hydration_attribute_changed`, bold, normal);\n\t} else {\n\t\tconsole.warn(`https://svelte.dev/e/hydration_attribute_changed`);\n\t}\n}\n\n/**\n * The value of an `{@html ...}` block %location% changed between server and client renders. The client value will be ignored in favour of the server value\n * @param {string | undefined | null} [location]\n */\nexport function hydration_html_changed(location) {\n\tif (DEV) {\n\t\tconsole.warn(\n\t\t\t`%c[svelte] hydration_html_changed\\n%c${location\n\t\t\t\t? `The value of an \\`{@html ...}\\` block ${location} changed between server and client renders. The client value will be ignored in favour of the server value`\n\t\t\t\t: 'The value of an `{@html ...}` block changed between server and client renders. The client value will be ignored in favour of the server value'}\\nhttps://svelte.dev/e/hydration_html_changed`,\n\t\t\tbold,\n\t\t\tnormal\n\t\t);\n\t} else {\n\t\tconsole.warn(`https://svelte.dev/e/hydration_html_changed`);\n\t}\n}\n\n/**\n * Hydration failed because the initial UI does not match what was rendered on the server. The error occurred near %location%\n * @param {string | undefined | null} [location]\n */\nexport function hydration_mismatch(location) {\n\tif (DEV) {\n\t\tconsole.warn(\n\t\t\t`%c[svelte] hydration_mismatch\\n%c${location\n\t\t\t\t? `Hydration failed because the initial UI does not match what was rendered on the server. The error occurred near ${location}`\n\t\t\t\t: 'Hydration failed because the initial UI does not match what was rendered on the server'}\\nhttps://svelte.dev/e/hydration_mismatch`,\n\t\t\tbold,\n\t\t\tnormal\n\t\t);\n\t} else {\n\t\tconsole.warn(`https://svelte.dev/e/hydration_mismatch`);\n\t}\n}\n\n/**\n * The `render` function passed to `createRawSnippet` should return HTML for a single element\n */\nexport function invalid_raw_snippet_render() {\n\tif (DEV) {\n\t\tconsole.warn(`%c[svelte] invalid_raw_snippet_render\\n%cThe \\`render\\` function passed to \\`createRawSnippet\\` should return HTML for a single element\\nhttps://svelte.dev/e/invalid_raw_snippet_render`, bold, normal);\n\t} else {\n\t\tconsole.warn(`https://svelte.dev/e/invalid_raw_snippet_render`);\n\t}\n}\n\n/**\n * Detected a migrated `$:` reactive block in `%filename%` that both accesses and updates the same reactive value. This may cause recursive updates when converted to an `$effect`.\n * @param {string} filename\n */\nexport function legacy_recursive_reactive_block(filename) {\n\tif (DEV) {\n\t\tconsole.warn(`%c[svelte] legacy_recursive_reactive_block\\n%cDetected a migrated \\`$:\\` reactive block in \\`${filename}\\` that both accesses and updates the same reactive value. This may cause recursive updates when converted to an \\`$effect\\`.\\nhttps://svelte.dev/e/legacy_recursive_reactive_block`, bold, normal);\n\t} else {\n\t\tconsole.warn(`https://svelte.dev/e/legacy_recursive_reactive_block`);\n\t}\n}\n\n/**\n * Tried to unmount a component that was not mounted\n */\nexport function lifecycle_double_unmount() {\n\tif (DEV) {\n\t\tconsole.warn(`%c[svelte] lifecycle_double_unmount\\n%cTried to unmount a component that was not mounted\\nhttps://svelte.dev/e/lifecycle_double_unmount`, bold, normal);\n\t} else {\n\t\tconsole.warn(`https://svelte.dev/e/lifecycle_double_unmount`);\n\t}\n}\n\n/**\n * %parent% passed property `%prop%` to %child% with `bind:`, but its parent component %owner% did not declare `%prop%` as a binding. Consider creating a binding between %owner% and %parent% (e.g. `bind:%prop%={...}` instead of `%prop%={...}`)\n * @param {string} parent\n * @param {string} prop\n * @param {string} child\n * @param {string} owner\n */\nexport function ownership_invalid_binding(parent, prop, child, owner) {\n\tif (DEV) {\n\t\tconsole.warn(`%c[svelte] ownership_invalid_binding\\n%c${parent} passed property \\`${prop}\\` to ${child} with \\`bind:\\`, but its parent component ${owner} did not declare \\`${prop}\\` as a binding. Consider creating a binding between ${owner} and ${parent} (e.g. \\`bind:${prop}={...}\\` instead of \\`${prop}={...}\\`)\\nhttps://svelte.dev/e/ownership_invalid_binding`, bold, normal);\n\t} else {\n\t\tconsole.warn(`https://svelte.dev/e/ownership_invalid_binding`);\n\t}\n}\n\n/**\n * Mutating unbound props (`%name%`, at %location%) is strongly discouraged. Consider using `bind:%prop%={...}` in %parent% (or using a callback) instead\n * @param {string} name\n * @param {string} location\n * @param {string} prop\n * @param {string} parent\n */\nexport function ownership_invalid_mutation(name, location, prop, parent) {\n\tif (DEV) {\n\t\tconsole.warn(`%c[svelte] ownership_invalid_mutation\\n%cMutating unbound props (\\`${name}\\`, at ${location}) is strongly discouraged. Consider using \\`bind:${prop}={...}\\` in ${parent} (or using a callback) instead\\nhttps://svelte.dev/e/ownership_invalid_mutation`, bold, normal);\n\t} else {\n\t\tconsole.warn(`https://svelte.dev/e/ownership_invalid_mutation`);\n\t}\n}\n\n/**\n * The `value` property of a `<select multiple>` element should be an array, but it received a non-array value. The selection will be kept as is.\n */\nexport function select_multiple_invalid_value() {\n\tif (DEV) {\n\t\tconsole.warn(`%c[svelte] select_multiple_invalid_value\\n%cThe \\`value\\` property of a \\`<select multiple>\\` element should be an array, but it received a non-array value. The selection will be kept as is.\\nhttps://svelte.dev/e/select_multiple_invalid_value`, bold, normal);\n\t} else {\n\t\tconsole.warn(`https://svelte.dev/e/select_multiple_invalid_value`);\n\t}\n}\n\n/**\n * Reactive `$state(...)` proxies and the values they proxy have different identities. Because of this, comparisons with `%operator%` will produce unexpected results\n * @param {string} operator\n */\nexport function state_proxy_equality_mismatch(operator) {\n\tif (DEV) {\n\t\tconsole.warn(`%c[svelte] state_proxy_equality_mismatch\\n%cReactive \\`$state(...)\\` proxies and the values they proxy have different identities. Because of this, comparisons with \\`${operator}\\` will produce unexpected results\\nhttps://svelte.dev/e/state_proxy_equality_mismatch`, bold, normal);\n\t} else {\n\t\tconsole.warn(`https://svelte.dev/e/state_proxy_equality_mismatch`);\n\t}\n}\n\n/**\n * The `slide` transition does not work correctly for elements with `display: %value%`\n * @param {string} value\n */\nexport function transition_slide_display(value) {\n\tif (DEV) {\n\t\tconsole.warn(`%c[svelte] transition_slide_display\\n%cThe \\`slide\\` transition does not work correctly for elements with \\`display: ${value}\\`\\nhttps://svelte.dev/e/transition_slide_display`, bold, normal);\n\t} else {\n\t\tconsole.warn(`https://svelte.dev/e/transition_slide_display`);\n\t}\n}"], "mappings": ";AAAA,IAAO,eAAQ;;;ACIf,IAAI,OAAO;AACX,IAAI,SAAS;AAON,SAAS,uBAAuB,UAAU,UAAU;AAC1D,MAAI,cAAK;AACR,YAAQ,KAAK;AAAA,oBAAwD,QAAQ,gBAAgB,QAAQ,8DAA8D,QAAQ;AAAA,8CAAsH,MAAM,MAAM;AAAA,EAC9S,OAAO;AACN,YAAQ,KAAK,6CAA6C;AAAA,EAC3D;AACD;AAOO,SAAS,8BAA8B,SAAS,UAAU;AAChE,MAAI,cAAK;AACR,YAAQ;AAAA,MACP;AAAA,IAA+C,WAC5C,KAAK,OAAO,OAAO,QAAQ,4CAC3B,KAAK,OAAO,0CAA0C;AAAA;AAAA,MACzD;AAAA,MACA;AAAA,IACD;AAAA,EACD,OAAO;AACN,YAAQ,KAAK,oDAAoD;AAAA,EAClE;AACD;AAMO,SAAS,kBAAkB,QAAQ;AACzC,MAAI,cAAK;AACR,YAAQ,KAAK;AAAA,mBAAkD,MAAM;AAAA,yCAAiJ,MAAM,MAAM;AAAA,EACnO,OAAO;AACN,YAAQ,KAAK,wCAAwC;AAAA,EACtD;AACD;AAOO,SAAS,sBAAsB,SAAS,YAAY;AAC1D,MAAI,cAAK;AACR,YAAQ,KAAK;AAAA,IAAuC,OAAO,0CAA0C,UAAU;AAAA,6CAAiD,MAAM,MAAM;AAAA,EAC7K,OAAO;AACN,YAAQ,KAAK,4CAA4C;AAAA,EAC1D;AACD;AAQO,SAAS,4BAA4B,WAAW,MAAM,OAAO;AACnE,MAAI,cAAK;AACR,YAAQ,KAAK;AAAA,UAAmD,SAAS,qBAAqB,IAAI,+EAA+E,KAAK;AAAA,mDAAuG,MAAM,MAAM;AAAA,EAC1S,OAAO;AACN,YAAQ,KAAK,kDAAkD;AAAA,EAChE;AACD;AAMO,SAAS,uBAAuB,UAAU;AAChD,MAAI,cAAK;AACR,YAAQ;AAAA,MACP;AAAA,IAAwC,WACrC,yCAAyC,QAAQ,+GACjD,+IAA+I;AAAA;AAAA,MAClJ;AAAA,MACA;AAAA,IACD;AAAA,EACD,OAAO;AACN,YAAQ,KAAK,6CAA6C;AAAA,EAC3D;AACD;AAMO,SAAS,mBAAmB,UAAU;AAC5C,MAAI,cAAK;AACR,YAAQ;AAAA,MACP;AAAA,IAAoC,WACjC,mHAAmH,QAAQ,KAC3H,wFAAwF;AAAA;AAAA,MAC3F;AAAA,MACA;AAAA,IACD;AAAA,EACD,OAAO;AACN,YAAQ,KAAK,yCAAyC;AAAA,EACvD;AACD;AAKO,SAAS,6BAA6B;AAC5C,MAAI,cAAK;AACR,YAAQ,KAAK;AAAA;AAAA,kDAA4L,MAAM,MAAM;AAAA,EACtN,OAAO;AACN,YAAQ,KAAK,iDAAiD;AAAA,EAC/D;AACD;AAMO,SAAS,gCAAgC,UAAU;AACzD,MAAI,cAAK;AACR,YAAQ,KAAK;AAAA,mDAAgG,QAAQ;AAAA,uDAAuL,MAAM,MAAM;AAAA,EACzT,OAAO;AACN,YAAQ,KAAK,sDAAsD;AAAA,EACpE;AACD;AAKO,SAAS,2BAA2B;AAC1C,MAAI,cAAK;AACR,YAAQ,KAAK;AAAA;AAAA,gDAA2I,MAAM,MAAM;AAAA,EACrK,OAAO;AACN,YAAQ,KAAK,+CAA+C;AAAA,EAC7D;AACD;AASO,SAAS,0BAA0B,QAAQ,MAAM,OAAO,OAAO;AACrE,MAAI,cAAK;AACR,YAAQ,KAAK;AAAA,IAA2C,MAAM,sBAAsB,IAAI,SAAS,KAAK,6CAA6C,KAAK,sBAAsB,IAAI,wDAAwD,KAAK,QAAQ,MAAM,iBAAiB,IAAI,yBAAyB,IAAI;AAAA,iDAA6D,MAAM,MAAM;AAAA,EACzX,OAAO;AACN,YAAQ,KAAK,gDAAgD;AAAA,EAC9D;AACD;AASO,SAAS,2BAA2B,MAAM,UAAU,MAAM,QAAQ;AACxE,MAAI,cAAK;AACR,YAAQ,KAAK;AAAA,8BAAsE,IAAI,UAAU,QAAQ,oDAAoD,IAAI,eAAe,MAAM;AAAA,kDAAmF,MAAM,MAAM;AAAA,EACtR,OAAO;AACN,YAAQ,KAAK,iDAAiD;AAAA,EAC/D;AACD;AAKO,SAAS,gCAAgC;AAC/C,MAAI,cAAK;AACR,YAAQ,KAAK;AAAA;AAAA,qDAAsP,MAAM,MAAM;AAAA,EAChR,OAAO;AACN,YAAQ,KAAK,oDAAoD;AAAA,EAClE;AACD;AAMO,SAAS,8BAA8B,UAAU;AACvD,MAAI,cAAK;AACR,YAAQ,KAAK;AAAA,8HAAyK,QAAQ;AAAA,qDAA0F,MAAM,MAAM;AAAA,EACrS,OAAO;AACN,YAAQ,KAAK,oDAAoD;AAAA,EAClE;AACD;AAMO,SAAS,yBAAyB,OAAO;AAC/C,MAAI,cAAK;AACR,YAAQ,KAAK;AAAA,kFAAwH,KAAK;AAAA,gDAAqD,MAAM,MAAM;AAAA,EAC5M,OAAO;AACN,YAAQ,KAAK,+CAA+C;AAAA,EAC7D;AACD;", "names": []}